import mongoose from "mongoose";
import bcrypt from "bcrypt";
import dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import fs from "fs";

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

// Define User schema
const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  name: { type: String, required: true },
  phone: { type: String },
  address: { type: String },
  foodChain: { type: mongoose.Schema.Types.ObjectId, ref: "FoodChain" },
  role: {
    type: String,
    enum: ["user", "admin", "super_admin"],
    default: "user",
  },
  status: {
    type: String,
    enum: ["active", "blocked"],
    default: "active",
  },
  isFirstLogin: { type: Boolean, default: false },
  createdBy: {
    type: String,
    enum: ["self", "admin", "system"],
    default: "self",
  },
  twoFactorEnabled: { type: Boolean, default: false },
  twoFactorSecret: { type: String },
  twoFactorBackupCodes: [{ type: String }],
  resetPasswordToken: { type: String },
  resetPasswordExpires: { type: Date },
  lastLogin: { type: Date },
  lastPasswordChange: { type: Date },
  failedLoginAttempts: { type: Number, default: 0 },
  lockedUntil: { type: Date },
  permissions: [{ type: String }],
  preferences: {
    darkMode: { type: Boolean, default: false },
    language: { type: String, default: "en" },
    emailNotifications: { type: Boolean, default: true },
    pushNotifications: { type: Boolean, default: true },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  aiMessage: { type: String },
});

const User = mongoose.model("User", userSchema);

// Read MongoDB URI from .env file
const envPath = join(__dirname, ".env");
let mongoUri;

try {
  const envContent = fs.readFileSync(envPath, "utf8");
  const mongoUriMatch = envContent.match(/MONGO_URI=(.+)/);
  if (mongoUriMatch && mongoUriMatch[1]) {
    mongoUri = mongoUriMatch[1].trim();
  } else {
    console.error("MONGO_URI not found in .env file");
    process.exit(1);
  }
} catch (error) {
  console.error("Error reading .env file:", error);
  process.exit(1);
}

// Connect to MongoDB
mongoose.connect(mongoUri)
  .then(async () => {
    console.log("Connected to MongoDB");

    try {
      // Check if super admin already exists
      const existingSuperAdmin = await User.findOne({ role: "super_admin" });
      if (existingSuperAdmin) {
        console.log("Super admin already exists:", existingSuperAdmin.email);
        process.exit(0);
      }

      // Create super admin
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash("password123", salt);
      
      const superAdmin = new User({
        email: "<EMAIL>",
        password: hashedPassword,
        name: "Super Admin",
        role: "super_admin",
      });

      await superAdmin.save();
      console.log("Super admin created successfully:", superAdmin.email);
    } catch (error) {
      console.error("Error creating super admin:", error);
    } finally {
      mongoose.connection.close();
    }
  })
  .catch((error) => {
    console.error("Error connecting to MongoDB:", error);
  });
