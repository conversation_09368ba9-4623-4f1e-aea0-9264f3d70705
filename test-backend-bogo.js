// Test script to verify backend BOGO calculation fixes
const BASE_URL = 'http://localhost:3001';

// Test data
const testOffer = {
  name: 'Test Buy 1 Get 1 Free',
  description: 'Buy 1 item, get 1 free',
  offerType: 'BOGO',
  discountDetails: {
    buyQuantity: 1,
    getQuantity: 1,
  },
  startDate: new Date(),
  endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
  isActive: true,
  displayOnApp: true,
  autoApply: true,
};

const testOrderData = {
  outletId: '67d46268bde37643fa02bcc',
  foodChainId: '67d3068336223ee92a96456d',
  orderAmount: 100,
  items: [
    {
      dishId: '67d3068336223ee92a96456e',
      dishName: 'Test Pizza',
      quantity: 1,
      price: 100,
    }
  ]
};

async function apiCall(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    return { success: response.ok, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testBOGOCalculation() {
  console.log('🧪 Testing backend BOGO calculation...\n');

  // Test 1: Single item should get 50% discount
  console.log('Test 1: Single item BOGO calculation');
  const singleItemResult = await apiCall('/api/v1/offers/applicable', 'POST', testOrderData);
  
  if (singleItemResult.success) {
    console.log('✅ API call successful');
    const bogoOffers = singleItemResult.data.data.filter(offer => offer.offerType === 'BOGO');
    
    if (bogoOffers.length > 0) {
      const bogoOffer = bogoOffers[0];
      console.log(`  Estimated savings: ₹${bogoOffer.estimatedSavings || 0}`);
      console.log(`  Can auto apply: ${bogoOffer.canAutoApply}`);
      
      // For single item Buy 1 Get 1, should get 50% discount (₹50)
      if (bogoOffer.estimatedSavings === 50) {
        console.log('  ✅ Single item BOGO calculation: PASS');
      } else {
        console.log('  ❌ Single item BOGO calculation: FAIL');
        console.log(`     Expected: ₹50, Got: ₹${bogoOffer.estimatedSavings}`);
      }
    } else {
      console.log('  ❌ No BOGO offers found');
    }
  } else {
    console.log(`❌ API call failed: ${singleItemResult.error || singleItemResult.data.message}`);
  }

  console.log('');

  // Test 2: Two items should get one free
  console.log('Test 2: Two items BOGO calculation');
  const twoItemOrderData = {
    ...testOrderData,
    orderAmount: 200,
    items: [
      {
        dishId: '67d3068336223ee92a96456e',
        dishName: 'Test Pizza',
        quantity: 2,
        price: 100,
      }
    ]
  };

  const twoItemResult = await apiCall('/api/v1/offers/applicable', 'POST', twoItemOrderData);
  
  if (twoItemResult.success) {
    console.log('✅ API call successful');
    const bogoOffers = twoItemResult.data.data.filter(offer => offer.offerType === 'BOGO');
    
    if (bogoOffers.length > 0) {
      const bogoOffer = bogoOffers[0];
      console.log(`  Estimated savings: ₹${bogoOffer.estimatedSavings || 0}`);
      console.log(`  Can auto apply: ${bogoOffer.canAutoApply}`);
      
      // For two items Buy 1 Get 1, should get one item free (₹100)
      if (bogoOffer.estimatedSavings === 100) {
        console.log('  ✅ Two items BOGO calculation: PASS');
      } else {
        console.log('  ❌ Two items BOGO calculation: FAIL');
        console.log(`     Expected: ₹100, Got: ₹${bogoOffer.estimatedSavings}`);
      }
    } else {
      console.log('  ❌ No BOGO offers found');
    }
  } else {
    console.log(`❌ API call failed: ${twoItemResult.error || twoItemResult.data.message}`);
  }

  console.log('\n🏁 Backend BOGO test completed!');
}

// Run the test
testBOGOCalculation().catch(console.error);
