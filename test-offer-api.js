// Test script for offer functionality
const API_BASE = "http://localhost:3001/api/v1";

// Test configuration - update these with real values
const ADMIN_TOKEN = "your-admin-token-here"; // Get from localStorage after admin login
const FOOD_CHAIN_ID = "your-food-chain-id"; // Get from admin panel
const OUTLET_ID = "your-outlet-id"; // Get from admin panel
const CUSTOMER_TOKEN = "your-customer-token"; // Get from customer login

// Test data
const testOffers = [
  {
    name: "Test Discount 10%",
    description: "10% discount on all items",
    offerType: "discount",
    discountDetails: {
      discountType: "percentage",
      discountValue: 10,
      maxDiscount: 100,
    },
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    isActive: true,
    displayOnApp: true,
    autoApply: false,
  },
  {
    name: "Buy 2 Get 1 Free",
    description: "Buy 2 items, get 1 free",
    offerType: "BOGO",
    discountDetails: {
      buyQuantity: 2,
      getQuantity: 1,
    },
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    isActive: true,
    displayOnApp: true,
    autoApply: true,
  },
  {
    name: "₹50 off on ₹500+",
    description: "Get ₹50 off on orders above ₹500",
    offerType: "minimumAmount",
    discountDetails: {
      discountType: "fixed",
      discountValue: 50,
      minimumOrderValue: 500,
    },
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    isActive: true,
    displayOnApp: true,
    autoApply: true,
  },
];

// Helper function to make API calls
async function apiCall(
  endpoint,
  method = "GET",
  data = null,
  token = ADMIN_TOKEN
) {
  const options = {
    method,
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    const result = await response.json();
    return { success: response.ok, status: response.status, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Test functions
async function testCreateOffer(offerData) {
  console.log(`\n🧪 Testing offer creation: ${offerData.name}`);

  const result = await apiCall("/admin/offers", "POST", offerData);

  if (result.success) {
    console.log(`✅ Offer created successfully: ${result.data.data._id}`);
    return result.data.data;
  } else {
    console.log(
      `❌ Failed to create offer: ${result.data.message || result.error}`
    );
    return null;
  }
}

async function testGetOffers() {
  console.log("\n🧪 Testing get all offers");

  const result = await apiCall("/admin/offers");

  if (result.success) {
    console.log(`✅ Retrieved ${result.data.count} offers`);
    return result.data.data;
  } else {
    console.log(
      `❌ Failed to get offers: ${result.data.message || result.error}`
    );
    return [];
  }
}

async function testUpdateOffer(offerId, updateData) {
  console.log(`\n🧪 Testing offer update: ${offerId}`);

  const result = await apiCall(`/admin/offers/${offerId}`, "PUT", updateData);

  if (result.success) {
    console.log(`✅ Offer updated successfully`);
    return result.data.data;
  } else {
    console.log(
      `❌ Failed to update offer: ${result.data.message || result.error}`
    );
    return null;
  }
}

async function testDeleteOffer(offerId) {
  console.log(`\n🧪 Testing offer deletion: ${offerId}`);

  const result = await apiCall(`/admin/offers/${offerId}`, "DELETE");

  if (result.success) {
    console.log(`✅ Offer deleted successfully`);
    return true;
  } else {
    console.log(
      `❌ Failed to delete offer: ${result.data.message || result.error}`
    );
    return false;
  }
}

async function testOfferApplication(orderData) {
  console.log("\n🧪 Testing offer application");

  const result = await apiCall("/offers/applicable", "POST", orderData);

  if (result.success) {
    console.log(`✅ Found ${result.data.count} applicable offers`);
    result.data.data.forEach((offer) => {
      console.log(
        `  - ${offer.name}: Estimated savings ₹${offer.estimatedSavings || 0}`
      );
    });
    return result.data.data;
  } else {
    console.log(
      `❌ Failed to get applicable offers: ${
        result.data.message || result.error
      }`
    );
    return [];
  }
}

// Main test execution
async function runTests() {
  console.log("🚀 Starting Offer System Tests\n");

  const createdOffers = [];

  // Test 1: Create offers
  console.log("=== TEST 1: CREATE OFFERS ===");
  for (const offerData of testOffers) {
    const created = await testCreateOffer(offerData);
    if (created) {
      createdOffers.push(created);
    }
  }

  // Test 2: Get all offers
  console.log("\n=== TEST 2: GET ALL OFFERS ===");
  const allOffers = await testGetOffers();

  // Test 3: Update an offer
  if (createdOffers.length > 0) {
    console.log("\n=== TEST 3: UPDATE OFFER ===");
    const offerToUpdate = createdOffers[0];
    await testUpdateOffer(offerToUpdate._id, {
      name: offerToUpdate.name + " (Updated)",
      description: offerToUpdate.description + " - Updated description",
    });
  }

  // Test 4: Test offer application
  console.log("\n=== TEST 4: OFFER APPLICATION ===");
  const testOrder = {
    orderAmount: 600,
    customerId: "test-customer-id",
    outletId: "test-outlet-id",
    items: [
      { dishId: "dish1", quantity: 2, price: 200 },
      { dishId: "dish2", quantity: 1, price: 200 },
    ],
  };

  await testOfferApplication(testOrder);

  // Test 5: Clean up - delete created offers
  console.log("\n=== TEST 5: CLEANUP ===");
  for (const offer of createdOffers) {
    await testDeleteOffer(offer._id);
  }

  console.log("\n🎉 All tests completed!");
}

// Export for use in Node.js or run directly
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    runTests,
    testCreateOffer,
    testGetOffers,
    testUpdateOffer,
    testDeleteOffer,
    testOfferApplication,
  };
} else {
  // Run tests if executed directly
  runTests().catch(console.error);
}
