import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3001/api/v1';

// Test data
const testData = {
  // You'll need to replace these with actual IDs from your database
  outletId: '6761b8b8b8b8b8b8b8b8b8b8', // Replace with actual outlet ID
  foodChainId: '6761b8b8b8b8b8b8b8b8b8b8', // Replace with actual food chain ID
  userId: '6761b8b8b8b8b8b8b8b8b8b8', // Replace with actual user ID
  dishId: '6761b8b8b8b8b8b8b8b8b8b8', // Replace with actual dish ID
  userToken: 'your-test-token-here', // Replace with actual user token
};

async function testOfferIntegration() {
  console.log('🧪 Testing Offer Integration End-to-End\n');

  try {
    // Step 1: Get applicable offers
    console.log('1️⃣ Getting applicable offers...');
    const offerResponse = await fetch(`${BASE_URL}/offers/applicable`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${testData.userToken}`,
      },
      body: JSON.stringify({
        outletId: testData.outletId,
        orderAmount: 500,
        customerId: testData.userId,
        items: [
          {
            dishId: testData.dishId,
            quantity: 2,
            price: 250,
            dishName: 'Test Dish',
          },
        ],
        foodChainId: testData.foodChainId,
      }),
    });

    const offers = await offerResponse.json();
    console.log('✅ Offers response:', JSON.stringify(offers, null, 2));

    if (!offers.success || !offers.data || offers.data.length === 0) {
      console.log('⚠️ No offers available for testing. Creating a test order without offers...');
    }

    // Step 2: Create order with offers (if available)
    console.log('\n2️⃣ Creating order with offers...');
    const appliedOffers = offers.success && offers.data.length > 0 
      ? offers.data.slice(0, 1).map(offer => ({
          _id: offer._id,
          offerId: offer._id,
          offerName: offer.name,
          offerType: offer.offerType,
          discount: offer.estimatedSavings || 0,
        }))
      : [];

    const orderResponse = await fetch(`${BASE_URL}/user/create-order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${testData.userToken}`,
      },
      body: JSON.stringify({
        outletId: testData.outletId,
        items: [
          {
            _id: testData.dishId,
            quantity: 2,
            price: 250,
            name: 'Test Dish',
          },
        ],
        specialInstructions: 'Test order for offer integration',
        appliedOffers: appliedOffers,
        finalAmount: 500 - (appliedOffers.reduce((sum, offer) => sum + offer.discount, 0)),
      }),
    });

    const order = await orderResponse.json();
    console.log('✅ Order creation response:', JSON.stringify(order, null, 2));

    if (order.success) {
      console.log('\n3️⃣ Verifying order amounts...');
      const createdOrder = order.data;
      
      console.log(`📊 Order Summary:`);
      console.log(`   Total Amount: ₹${createdOrder.totalAmount}`);
      console.log(`   Offer Discount: ₹${createdOrder.offerDiscount || 0}`);
      console.log(`   Coupon Discount: ₹${createdOrder.couponDiscount || 0}`);
      console.log(`   Final Amount: ₹${createdOrder.finalAmount}`);
      console.log(`   Applied Offers: ${createdOrder.appliedOffers?.length || 0}`);

      if (createdOrder.appliedOffers && createdOrder.appliedOffers.length > 0) {
        console.log('\n✅ SUCCESS: Offers were applied to the order!');
        createdOrder.appliedOffers.forEach((offer, index) => {
          console.log(`   Offer ${index + 1}: ${offer.offerName} - ₹${offer.discount} discount`);
        });
      } else {
        console.log('\n⚠️ No offers were applied (this might be expected if no offers are available)');
      }

      // Verify the math
      const expectedFinalAmount = createdOrder.totalAmount - (createdOrder.offerDiscount || 0) - (createdOrder.couponDiscount || 0);
      if (Math.abs(createdOrder.finalAmount - expectedFinalAmount) < 0.01) {
        console.log('✅ Amount calculation is correct!');
      } else {
        console.log(`❌ Amount calculation error: Expected ₹${expectedFinalAmount}, got ₹${createdOrder.finalAmount}`);
      }

      return createdOrder._id;
    } else {
      console.log('❌ Order creation failed:', order.message);
      return null;
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    return null;
  }
}

// Instructions for running the test
console.log(`
📋 INSTRUCTIONS FOR RUNNING THIS TEST:

1. Update the testData object with actual IDs from your database:
   - outletId: Get from your outlets collection
   - foodChainId: Get from your food chains collection  
   - userId: Get from your users collection
   - dishId: Get from your dishes collection
   - userToken: Get a valid JWT token for the user

2. Make sure you have at least one active offer in your database

3. Run the test:
   node test-offer-integration.js

4. Check the output to verify offers are being applied correctly

🔍 What this test checks:
- ✅ Offers can be fetched for an order
- ✅ Orders can be created with applied offers
- ✅ Offer discounts are stored in the order
- ✅ Final amount calculation includes offer discounts
- ✅ Applied offers are saved with the order

`);

// Uncomment the line below to run the test (after updating testData)
// testOfferIntegration();
