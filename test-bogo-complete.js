// Complete test script to create and test BOGO offers
const BASE_URL = 'http://localhost:3001';

// Admin token for creating offers (you'll need to get this from your admin login)
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.EiBGewBATRV-aCMG4ssA28rb0HwnZT4MWMurQnV2CYw';

async function apiCall(endpoint, method = 'GET', data = null, useAuth = false) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (useAuth) {
      options.headers.Authorization = `Bearer ${ADMIN_TOKEN}`;
    }

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    return { success: response.ok, data: result, status: response.status };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function createTestBOGOOffer() {
  console.log('📝 Creating test BOGO offer...');
  
  const offerData = {
    name: 'Test Buy 1 Get 1 Free',
    description: 'Buy 1 item, get 1 free - Test offer',
    offerType: 'BOGO',
    discountDetails: {
      buyQuantity: 1,
      getQuantity: 1,
    },
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    isActive: true,
    displayOnApp: true,
    autoApply: true,
    applicableOutlets: [], // Empty means all outlets
    applicableDishes: [], // Empty means all dishes
  };

  const result = await apiCall('/api/v1/admin/offers', 'POST', offerData, true);
  
  if (result.success) {
    console.log('✅ BOGO offer created successfully');
    return result.data.data;
  } else {
    console.log(`❌ Failed to create offer: ${result.data?.message || result.error}`);
    console.log('Status:', result.status);
    return null;
  }
}

async function testBOGOCalculation() {
  console.log('🧪 Testing BOGO calculation with real API...\n');

  // First, create a test offer
  const offer = await createTestBOGOOffer();
  if (!offer) {
    console.log('❌ Cannot proceed without test offer');
    return;
  }

  console.log('');

  // Test 1: Single item should get 50% discount
  console.log('Test 1: Single item BOGO calculation');
  const singleItemOrderData = {
    outletId: '67d46268bde37643fa02bcc',
    foodChainId: '67d3068336223ee92a96456d',
    orderAmount: 100,
    items: [
      {
        dishId: '67d3068336223ee92a96456e',
        dishName: 'Test Pizza',
        quantity: 1,
        price: 100,
      }
    ]
  };

  const singleItemResult = await apiCall('/api/v1/offers/applicable', 'POST', singleItemOrderData);
  
  if (singleItemResult.success) {
    console.log('✅ API call successful');
    const bogoOffers = singleItemResult.data.data.filter(offer => offer.offerType === 'BOGO');
    
    if (bogoOffers.length > 0) {
      const bogoOffer = bogoOffers[0];
      console.log(`  Offer name: ${bogoOffer.name}`);
      console.log(`  Estimated savings: ₹${bogoOffer.estimatedSavings || 0}`);
      console.log(`  Can auto apply: ${bogoOffer.canAutoApply}`);
      
      // For single item Buy 1 Get 1, should get 50% discount (₹50)
      if (bogoOffer.estimatedSavings === 50) {
        console.log('  ✅ Single item BOGO calculation: PASS');
      } else {
        console.log('  ❌ Single item BOGO calculation: FAIL');
        console.log(`     Expected: ₹50, Got: ₹${bogoOffer.estimatedSavings}`);
      }
    } else {
      console.log('  ❌ No BOGO offers found in response');
      console.log('  Available offers:', singleItemResult.data.data.map(o => o.name));
    }
  } else {
    console.log(`❌ API call failed: ${singleItemResult.error || singleItemResult.data?.message}`);
  }

  console.log('');

  // Test 2: Two items should get one free
  console.log('Test 2: Two items BOGO calculation');
  const twoItemOrderData = {
    outletId: '67d46268bde37643fa02bcc',
    foodChainId: '67d3068336223ee92a96456d',
    orderAmount: 200,
    items: [
      {
        dishId: '67d3068336223ee92a96456e',
        dishName: 'Test Pizza',
        quantity: 2,
        price: 100,
      }
    ]
  };

  const twoItemResult = await apiCall('/api/v1/offers/applicable', 'POST', twoItemOrderData);
  
  if (twoItemResult.success) {
    console.log('✅ API call successful');
    const bogoOffers = twoItemResult.data.data.filter(offer => offer.offerType === 'BOGO');
    
    if (bogoOffers.length > 0) {
      const bogoOffer = bogoOffers[0];
      console.log(`  Offer name: ${bogoOffer.name}`);
      console.log(`  Estimated savings: ₹${bogoOffer.estimatedSavings || 0}`);
      console.log(`  Can auto apply: ${bogoOffer.canAutoApply}`);
      
      // For two items Buy 1 Get 1, should get one item free (₹100)
      if (bogoOffer.estimatedSavings === 100) {
        console.log('  ✅ Two items BOGO calculation: PASS');
      } else {
        console.log('  ❌ Two items BOGO calculation: FAIL');
        console.log(`     Expected: ₹100, Got: ₹${bogoOffer.estimatedSavings}`);
      }
    } else {
      console.log('  ❌ No BOGO offers found in response');
    }
  } else {
    console.log(`❌ API call failed: ${twoItemResult.error || twoItemResult.data?.message}`);
  }

  console.log('\n🏁 BOGO test completed!');
  
  // Clean up - delete the test offer
  if (offer && offer._id) {
    console.log('\n🧹 Cleaning up test offer...');
    const deleteResult = await apiCall(`/api/v1/admin/offers/${offer._id}`, 'DELETE', null, true);
    if (deleteResult.success) {
      console.log('✅ Test offer deleted successfully');
    } else {
      console.log('❌ Failed to delete test offer');
    }
  }
}

// Run the test
testBOGOCalculation().catch(console.error);
