// Test script to verify BOGO calculation fixes
import { calculateBOGODiscount } from './butler-web/src/utils/offerCalculations.js';

// Mock offer data
const bogoOffer = {
  _id: 'test-bogo-offer',
  name: 'Buy 1 Get 1 Free',
  offerType: 'BOGO',
  discountDetails: {
    buyQuantity: 1,
    getQuantity: 1
  }
};

// Test cases
const testCases = [
  {
    name: 'Single item - should get 50% discount',
    items: [
      { _id: 'dish1', name: 'Pizza', price: 100, quantity: 1 }
    ],
    expectedDiscount: 50,
    expectedDescription: 'Buy 1 Get 1 Free (50% off single item)'
  },
  {
    name: 'Two items - should get one free',
    items: [
      { _id: 'dish1', name: 'Pizza', price: 100, quantity: 2 }
    ],
    expectedDiscount: 100,
    expectedDescription: 'Buy 1 Get 1 Free (2 sets applied)'
  },
  {
    name: 'Three items - should get one free',
    items: [
      { _id: 'dish1', name: 'Pizza', price: 100, quantity: 3 }
    ],
    expectedDiscount: 200,
    expectedDescription: 'Buy 1 Get 1 Free (2 sets applied)'
  }
];

console.log('🧪 Testing BOGO calculation fixes...\n');

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  
  try {
    const result = calculateBOGODiscount(testCase.items, bogoOffer);
    
    console.log(`  Expected discount: ₹${testCase.expectedDiscount}`);
    console.log(`  Actual discount: ₹${result.discount}`);
    console.log(`  Expected description: ${testCase.expectedDescription}`);
    console.log(`  Actual description: ${result.description}`);
    
    const discountMatch = Math.abs(result.discount - testCase.expectedDiscount) < 0.01;
    const descriptionMatch = result.description.includes('50% off single item') || 
                           result.description.includes('sets applied');
    
    if (discountMatch) {
      console.log('  ✅ Discount calculation: PASS');
    } else {
      console.log('  ❌ Discount calculation: FAIL');
    }
    
    if (descriptionMatch) {
      console.log('  ✅ Description: PASS');
    } else {
      console.log('  ❌ Description: FAIL');
    }
    
  } catch (error) {
    console.log(`  ❌ Error: ${error.message}`);
  }
  
  console.log('');
});

console.log('🏁 Test completed!');
