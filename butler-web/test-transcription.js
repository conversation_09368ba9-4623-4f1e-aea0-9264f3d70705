#!/usr/bin/env node

/**
 * Test script to verify audio transcription service
 * Usage: node test-transcription.js
 */

const BACKEND_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001";

async function testTranscriptionService() {
  console.log("🧪 Testing Audio Transcription Service");
  console.log("🔗 Backend URL:", BACKEND_URL);
  console.log("=" .repeat(50));

  try {
    // Test 1: Check if the service is available
    console.log("\n1️⃣ Testing service availability...");
    
    const testResponse = await fetch(`${BACKEND_URL}/api/transcribe-audio/test`);
    const testResult = await testResponse.json();
    
    if (testResponse.ok) {
      console.log("✅ Service is available");
      console.log("📋 Service info:", testResult);
    } else {
      console.log("❌ Service unavailable:", testResult);
      return;
    }

    // Test 2: Check environment variables
    console.log("\n2️⃣ Checking environment configuration...");
    console.log("🔑 NEXT_PUBLIC_BASE_URL:", process.env.NEXT_PUBLIC_BASE_URL || "Not set");
    console.log("🔑 NEXT_PUBLIC_BACKEND_URL:", process.env.NEXT_PUBLIC_BACKEND_URL || "Not set");

    // Test 3: Test frontend API route
    console.log("\n3️⃣ Testing frontend API route...");
    
    const frontendTestResponse = await fetch("http://localhost:3000/api/transcribe-audio");
    const frontendTestResult = await frontendTestResponse.json();
    
    if (frontendTestResponse.ok) {
      console.log("✅ Frontend API route is working");
      console.log("📋 Frontend response:", frontendTestResult);
    } else {
      console.log("❌ Frontend API route failed:", frontendTestResult);
    }

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    
    if (error.message.includes('fetch')) {
      console.log("\n🔍 Troubleshooting tips:");
      console.log("- Make sure the backend server is running");
      console.log("- Check if the NEXT_PUBLIC_BASE_URL is correct");
      console.log("- Verify network connectivity");
      console.log("- For mobile testing, use the actual server IP instead of localhost");
    }
  }
}

// Run the test
testTranscriptionService().catch(console.error);
