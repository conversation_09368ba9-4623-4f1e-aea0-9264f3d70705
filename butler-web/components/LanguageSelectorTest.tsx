import React from "react";
import LanguageSelector from "./LanguageSelector";
import { useLanguagePreference } from "../hooks/useLanguagePreference";

const LanguageSelectorTest: React.FC = () => {
  const { language, setLanguage } = useLanguagePreference();

  return (
    <div className="p-4 border rounded-lg bg-white shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Language Selector Test</h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Current Language: <span className="font-bold">{language}</span>
          </label>

          <LanguageSelector
            selectedLanguage={language}
            onLanguageChange={setLanguage}
          />
        </div>

        <div className="text-sm text-gray-600">
          <p>Test the language selector:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Click the dropdown to see available languages</li>
            <li>Select a different language</li>
            <li>The selection should persist in localStorage</li>
            <li>The conversation will use your selected language</li>
          </ul>
        </div>

        <div className="bg-gray-50 p-3 rounded">
          <h4 className="font-medium mb-2">Expected Behavior:</h4>
          <div className="text-sm space-y-1">
            <p>
              <strong>English:</strong> AI responds in English
            </p>
            <p>
              <strong>Hindi:</strong> AI responds in Hindi (हिंदी)
            </p>
            <p>
              <strong>Hinglish:</strong> AI responds in Hinglish mix
            </p>
            <p>
              <strong>Keywords:</strong> Always in English for search
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LanguageSelectorTest;
