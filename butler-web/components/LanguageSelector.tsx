import { ChevronDownIcon, Languages } from "lucide-react";
import React, { useState, useEffect } from "react";
import { SupportedLanguage } from "../hooks/useLanguagePreference";
interface LanguageSelectorProps {
  selectedLanguage: string;
  onLanguageChange: (language: SupportedLanguage) => void;
  className?: string;
}

const languages: {
  code: SupportedLanguage;
  name: string;
  flag: string;
  nativeName: string;
}[] = [
  { code: "en", name: "English", flag: "🇺🇸", nativeName: "ENG" },
  { code: "hi", name: "Hindi", flag: "🇮🇳", nativeName: "HI" },
  { code: "hi-en", name: "Hinglish", flag: "🇮🇳", nativeName: "HIN-ENG" },
  { code: "ta", name: "Tamil", flag: "🇮🇳", nativeName: "TA" },
  { code: "te", name: "Telugu", flag: "🇮🇳", nativeName: "TE" },
  { code: "bn", name: "Bengali", flag: "🇮🇳", nativeName: "BN" },
  { code: "mr", name: "Marathi", flag: "🇮🇳", nativeName: "MR" },
  { code: "gu", name: "Gujarati", flag: "🇮🇳", nativeName: "GU" },
];

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  selectedLanguage,
  onLanguageChange,
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const selectedLang =
    languages.find((lang) => lang.code === selectedLanguage) || languages[0];

  const handleLanguageSelect = (languageCode: SupportedLanguage) => {
    onLanguageChange(languageCode);
    setIsOpen(false);

    // Store preference in localStorage
    if (typeof window !== "undefined") {
      localStorage.setItem("butler-conversation-language", languageCode);
    }
  };

  // Don't render until mounted to avoid hydration issues
  if (!mounted) {
    return (
      <div
        className={`inline-flex items-center space-x-2 px-3 py-2 ${className}`}
      >
        <Languages className="h-4 w-4 text-gray-500" />
        <span className="text-sm text-gray-600">Loading...</span>
      </div>
    );
  }

  return (
    <div className={`relative inline-block text-left ${className}`}>
      <div>
        <button
          type="button"
          className="inline-flex items-center justify-center w-full rounded-md border border-gray-300 shadow-sm px-1 py-1 md:px-3 md:py-2 bg-white text-xs md:text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded="true"
          aria-haspopup="true"
        >
          <span className="mr-2 text-lg">{selectedLang.flag}</span>
          <span className="mr-2">{selectedLang.nativeName}</span>
          <ChevronDownIcon
            className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${
              isOpen ? "transform rotate-180" : ""
            }`}
          />
        </button>
      </div>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md h-[60vh] overflow-y-auto shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-20">
            <div className="py-1" role="menu" aria-orientation="vertical">
              <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                Select Language
              </div>

              {languages.map((language) => (
                <button
                  key={language.code}
                  className={`group flex items-center w-full px-4 py-3 text-sm hover:bg-gray-50 transition-colors duration-150 ${
                    selectedLanguage === language.code
                      ? "bg-indigo-50 text-indigo-700"
                      : "text-gray-700"
                  }`}
                  role="menuitem"
                  onClick={() => handleLanguageSelect(language.code)}
                >
                  <span className="mr-3 text-lg">{language.flag}</span>
                  <div className="flex flex-col items-start">
                    <span className="font-medium">{language.nativeName}</span>
                    <span className="text-xs text-gray-500">
                      {language.name}
                    </span>
                  </div>

                  {selectedLanguage === language.code && (
                    <div className="ml-auto">
                      <div className="h-2 w-2 bg-indigo-600 rounded-full"></div>
                    </div>
                  )}
                </button>
              ))}

              <div className="border-t border-gray-100 px-4 py-2">
                <p className="text-xs text-gray-500">
                  Your language preference will be remembered for future
                  conversations.
                </p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LanguageSelector;
