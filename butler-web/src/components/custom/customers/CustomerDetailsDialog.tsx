"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { getCustomerDetails, resetCustomerPassword } from "@/server/admin";
import { Loader2, ShoppingBag, Calendar, AlertTriangle } from "lucide-react";
import { format } from "date-fns";
import { Indian } from "@/lib/currency";

interface CustomerDetailsDialogProps {
  customerId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCustomerUpdated?: () => void;
}

interface CustomerDetails {
  customer: {
    _id: string;
    name: string;
    email: string;
    phone: string;
    address?: string;
    status: "active" | "blocked";
    createdAt: string;
    updatedAt: string;
  };
  recentOrders: Array<{
    _id: string;
    orderNumber: string;
    totalAmount: number;
    status: string;
    createdAt: string;
  }>;
}

export default function CustomerDetailsDialog({
  customerId,
  open,
  onOpenChange,
}: CustomerDetailsDialogProps) {
  const [customerDetails, setCustomerDetails] =
    useState<CustomerDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [showResetPasswordDialog, setShowResetPasswordDialog] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [newPassword, setNewPassword] = useState<string | null>(null);

  const fetchCustomerDetails = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getCustomerDetails(customerId);
      if (response.success) {
        setCustomerDetails(response.data);
      } else {
        toast.error(response.message || "Failed to fetch customer details");
      }
    } catch (error) {
      console.error("Error fetching customer details:", error);
      toast.error("An error occurred while fetching customer details");
    } finally {
      setLoading(false);
    }
  }, [customerId]);

  useEffect(() => {
    if (open && customerId) {
      fetchCustomerDetails();
    }
  }, [open, customerId, fetchCustomerDetails]);

  const handleResetPassword = async () => {
    try {
      setIsResettingPassword(true);
      const response = await resetCustomerPassword(customerId);

      if (response.success) {
        setNewPassword(response.data.password);
        toast.success("Password reset successfully");
      } else {
        toast.error(response.message || "Failed to reset password");
        setShowResetPasswordDialog(false);
      }
    } catch (error) {
      console.error("Error resetting password:", error);
      toast.error("An error occurred while resetting password");
      setShowResetPasswordDialog(false);
    } finally {
      setIsResettingPassword(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        {loading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="mt-2">Loading customer details...</p>
          </div>
        ) : !customerDetails ? (
          <div className="text-center py-8 text-red-500">
            Failed to load customer details
          </div>
        ) : (
          <>
            <DialogHeader>
              <div className="flex justify-between items-center">
                <DialogTitle className="text-xl">Customer Details</DialogTitle>
                <Badge
                  className={
                    customerDetails.customer.status === "active"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }
                >
                  {customerDetails.customer.status.toUpperCase()}
                </Badge>
              </div>
              <DialogDescription>
                View and edit customer information
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-6 py-4">
              <div className="grid gap-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Personal Information</h3>
                </div>
                <Separator />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <div className="p-2 bg-gray-50 rounded-md">
                      {customerDetails.customer.name}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>

                    <div className="p-2 bg-gray-50 rounded-md">
                      {customerDetails.customer.email}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>

                    <div className="p-2 bg-gray-50 rounded-md">
                      {customerDetails.customer.phone || "—"}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>

                    <div className="p-2 bg-gray-50 rounded-md">
                      {customerDetails.customer.address || "—"}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div className="space-y-2">
                    <Label>Customer Since</Label>
                    <div className="p-2 bg-gray-50 rounded-md flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      {format(
                        new Date(customerDetails.customer.createdAt),
                        "PPP"
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Last Updated</Label>
                    <div className="p-2 bg-gray-50 rounded-md">
                      {format(
                        new Date(customerDetails.customer.updatedAt),
                        "PPP"
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {customerDetails.recentOrders.length > 0 && (
                <div className="grid gap-4">
                  <div className="flex items-center">
                    <ShoppingBag className="h-5 w-5 mr-2 text-gray-500" />
                    <h3 className="text-lg font-medium">Recent Orders</h3>
                  </div>
                  <Separator />
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-1 font-medium">
                            Order #
                          </th>
                          <th className="text-left py-2 px-1 font-medium">
                            Date
                          </th>
                          <th className="text-left py-2 px-1 font-medium">
                            Amount
                          </th>
                          <th className="text-left py-2 px-1 font-medium">
                            Status
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {customerDetails.recentOrders.map((order) => (
                          <tr key={order._id} className="border-b">
                            <td className="py-2 px-1">{order.orderNumber}</td>
                            <td className="py-2 px-1">
                              {format(new Date(order.createdAt), "PP")}
                            </td>
                            <td className="py-2 px-1">
                              {Indian(order.totalAmount)}
                            </td>
                            <td className="py-2 px-1">
                              <Badge
                                variant="outline"
                                className={
                                  order.status === "completed"
                                    ? "border-green-500 text-green-600"
                                    : order.status === "cancelled"
                                    ? "border-red-500 text-red-600"
                                    : "border-blue-500 text-blue-600"
                                }
                              >
                                {order.status.toUpperCase()}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>

            <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-between">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="w-full sm:w-auto order-1 sm:order-2"
              >
                Close
              </Button>
            </DialogFooter>

            {/* Reset Password Alert Dialog */}
            <AlertDialog
              open={showResetPasswordDialog}
              onOpenChange={setShowResetPasswordDialog}
            >
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Reset Customer Password</AlertDialogTitle>
                  <AlertDialogDescription>
                    {newPassword ? (
                      <div className="space-y-4">
                        <p>Password has been reset successfully.</p>
                        <div className="bg-yellow-50 p-3 rounded-md border border-yellow-200 flex items-start">
                          <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                          <div>
                            <p className="font-medium text-yellow-800">
                              New temporary password:
                            </p>
                            <p className="font-mono bg-white p-2 rounded mt-1 border text-sm">
                              {newPassword}
                            </p>
                            <p className="text-xs mt-2 text-yellow-700">
                              Please make sure to save this password or share it
                              with the customer. For security reasons, it
                              won&apos;t be displayed again.
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <p>
                          This will reset the customer&apos;s password and
                          generate a new temporary password. Are you sure you
                          want to continue?
                        </p>
                        <p className="mt-2 text-sm text-gray-500">
                          The customer will need to use this temporary password
                          for their next login.
                        </p>
                      </div>
                    )}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  {newPassword ? (
                    <AlertDialogAction
                      onClick={() => {
                        setNewPassword(null);
                        setShowResetPasswordDialog(false);
                      }}
                    >
                      Done
                    </AlertDialogAction>
                  ) : (
                    <>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleResetPassword}
                        disabled={isResettingPassword}
                      >
                        {isResettingPassword ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-1" />
                            Resetting...
                          </>
                        ) : (
                          "Reset Password"
                        )}
                      </AlertDialogAction>
                    </>
                  )}
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
