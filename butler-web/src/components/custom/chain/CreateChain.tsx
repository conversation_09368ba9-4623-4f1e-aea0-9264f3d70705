import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { useState } from "react";
import { create<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/server/super-admin";
import { toast } from "sonner";
import { ThemeConfig } from "@/app/type";
import { Separator } from "@/components/ui/separator";

const initialTheme: ThemeConfig = {
  primaryColor: "#3B82F6",
  secondaryColor: "#1E40AF",
  accentColor: "#EF4444",
  logoUrl: "/default-logo.png",
  chainId: "",
  outletId: "",
  favIcon: "/logos/chain-1-logo.png",
  name: "<PERSON>",
};

const CreateChain = ({ onSuccess }: { onSuccess: () => void }) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    contact: "",
    email: "",
    tagline: "",
    theme: initialTheme,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error("Chain name is required");
      return false;
    }
    if (!formData.contact.trim()) {
      toast.error("Contact number is required");
      return false;
    }
    if (!formData.email.trim()) {
      toast.error("Email is required");
      return false;
    }
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return false;
    }
    return true;
  };

  const handleCreateChain = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      const res = await createNewChain({
        name: formData.name,
        contact: formData.contact,
        theme: formData.theme,
        tagline: formData.tagline,
        email: formData.email,
      });

      if (res.success) {
        toast.success("Chain created successfully");
        setOpen(false);
        onSuccess();
        // Reset form
        setFormData({
          name: "",
          contact: "",
          email: "",
          tagline: "",
          theme: initialTheme,
        });
      } else {
        toast.error(res.message || "Failed to create chain");
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="outline">Create New Chain</Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Create New Chain</SheetTitle>
          <SheetDescription>
            Enter the details for your new food chain.
          </SheetDescription>
        </SheetHeader>
        <div className="grid gap-4 p-4 overflow-y-scroll">
          <div className="grid gap-2">
            <label htmlFor="name">Name</label>
            <Input
              id="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter chain name"
              className="col-span-3"
            />
          </div>
          <div className="grid gap-2">
            <label htmlFor="email">Email</label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter email address"
              className="col-span-3"
            />
          </div>
          <div className="grid gap-2">
            <label htmlFor="contact">Contact</label>
            <Input
              id="contact"
              value={formData.contact}
              onChange={handleInputChange}
              placeholder="Enter contact number"
              className="col-span-3"
            />
          </div>
          <div className="grid gap-2">
            <label htmlFor="tagline">Tagline</label>
            <Input
              id="tagline"
              value={formData.tagline}
              onChange={handleInputChange}
              placeholder="Enter tagline (optional)"
              className="col-span-3"
            />
          </div>
          <Separator />
          <div className="grid gap-2">
            <label className="font-bold">Theme</label>
          </div>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <label>Colors</label>
              <div className="flex gap-4">
                <div className="grid gap-2">
                  <label htmlFor="primaryColor" className="text-sm">
                    Primary
                  </label>
                  <div className="flex gap-2 items-center">
                    <Input
                      id="primaryColor"
                      type="color"
                      value={formData.theme.primaryColor}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          theme: {
                            ...prev.theme,
                            primaryColor: e.target.value,
                          },
                        }))
                      }
                      className="w-12 h-12 p-1 rounded-sm"
                    />
                    <span className="text-sm text-muted-foreground">
                      {formData.theme.primaryColor}
                    </span>
                  </div>
                </div>
              </div>
              <div className="grid gap-2">
                <label htmlFor="secondaryColor" className="text-sm">
                  Secondary
                </label>
                <div className="flex gap-2 items-center">
                  <Input
                    id="secondaryColor"
                    type="color"
                    value={formData.theme.secondaryColor}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        theme: {
                          ...prev.theme,
                          secondaryColor: e.target.value,
                        },
                      }))
                    }
                    className="w-12 h-12 p-1"
                  />
                  <span className="text-sm text-muted-foreground">
                    {formData.theme.secondaryColor}
                  </span>
                </div>
              </div>

              <div className="grid gap-2">
                <label htmlFor="accentColor" className="text-sm">
                  Accent
                </label>
                <div className="flex gap-2 items-center">
                  <Input
                    id="accentColor"
                    type="color"
                    value={formData.theme.accentColor}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        theme: {
                          ...prev.theme,
                          accentColor: e.target.value,
                        },
                      }))
                    }
                    className="w-12 h-12 p-1"
                  />
                  <span className="text-sm text-muted-foreground">
                    {formData.theme.accentColor}
                  </span>
                </div>
              </div>
            </div>

            <div className="grid gap-2">
              <label>Images</label>
              <div className="flex gap-4">
                <div className="grid gap-2 flex-1">
                  <label htmlFor="logoUrl" className="text-sm">
                    Logo URL
                  </label>
                  <Input
                    id="logoUrl"
                    value={formData.theme.logoUrl}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        theme: {
                          ...prev.theme,
                          logoUrl: e.target.value,
                        },
                      }))
                    }
                    placeholder="Enter logo URL"
                  />
                </div>
              </div>
              <div className="grid gap-2 flex-1">
                <label htmlFor="favIcon" className="text-sm">
                  Favicon URL
                </label>
                <Input
                  id="favIcon"
                  value={formData.theme.favIcon}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      theme: {
                        ...prev.theme,
                        favIcon: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter favicon URL"
                />
              </div>
            </div>
          </div>
        </div>
        <SheetFooter>
          <Button type="submit" onClick={handleCreateChain} disabled={loading}>
            {loading ? "Creating..." : "Create Chain"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default CreateChain;
