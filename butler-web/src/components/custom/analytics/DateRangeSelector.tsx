import { useState, useEffect } from "react";
import { Slider } from "@/components/ui/slider";

// This would be passed in from your application

export default function DateRangeSlider({
  creationDate,
  onRangeChange,
}: {
  creationDate: string;
  onRangeChange: (days: number) => void;
}) {
  // Date utility functions
  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      month: "short",
      day: "numeric",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  const subDays = (date: Date, days: number) => {
    const result = new Date(date);
    result.setDate(date.getDate() - days);
    return result;
  };

  const differenceInDays = (dateA: Date, dateB: Date) => {
    const diffTime = dateA.getTime() - dateB.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  };

  const today = new Date();

  // Parse the creation date from ISO format
  const parsedCreationDate = new Date(creationDate);

  // Calculate total days between creation date and today
  const totalDays = differenceInDays(today, parsedCreationDate);

  // Default to showing the last 30 days (or less if creation date is more recent)
  const defaultStartDaysAgo = Math.min(30, totalDays);

  // State for the selected range
  const [selectedRange, setSelectedRange] = useState([
    totalDays - defaultStartDaysAgo,
    totalDays,
  ]);

  // Calculate the actual dates based on the slider values
  const startDate = subDays(today, totalDays - selectedRange[0]);
  const endDate = subDays(today, totalDays - selectedRange[1]);

  // Format dates for display
  const formattedStartDate = formatDate(startDate);
  const formattedEndDate = formatDate(endDate);

  // Call the callback function when range changes
  useEffect(() => {
    onRangeChange(differenceInDays(endDate, startDate) + 1);
  }, [selectedRange, onRangeChange, startDate, endDate]);

  return (
    <div className="w-full mx-auto p-6 space-y-6">
      <div className="mb-6">
        <Slider
          defaultValue={selectedRange}
          min={0}
          max={totalDays}
          step={1}
          value={selectedRange}
          onValueChange={setSelectedRange}
          className="py-4"
        />
      </div>

      <div className="flex justify-between text-sm">
        <div className="text-left">
          <p className="font-semibold text">From</p>
          <p className="text-lg font-bold">{formattedStartDate}</p>
        </div>
        <div className="text-right">
          <p className="font-semibold text-gray-700">To</p>
          <p className="text-lg font-bold">{formattedEndDate}</p>
        </div>
      </div>
    </div>
  );
}
