"use client";
import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useTheme } from "@/contexts/ThemeContext";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetDescription,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { createNewOutlet } from "@/server/admin";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";

const CreateOutletDrawer = ({ onSuccess }: { onSuccess: () => void }) => {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [newOutlet, setNewOutlet] = useState({
    name: String(theme?.name),
    address: "",
    city: "",
    pincode: "",
    contact: "",
    foodChain: "",
    isCloudKitchen: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewOutlet((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setNewOutlet((prev) => ({
      ...prev,
      isCloudKitchen: checked,
    }));
  };

  const validateForm = () => {
    if (!newOutlet.name.trim()) {
      toast("Outlet name is required");
      return false;
    }
    if (!newOutlet.address.trim()) {
      toast("Address is required");
      return false;
    }
    if (!newOutlet.city.trim()) {
      toast("City is required");
      return false;
    }
    if (!newOutlet.pincode.trim()) {
      toast("Pincode is required");
      return false;
    }
    if (!newOutlet.contact.trim()) {
      toast("Contact number is required");
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      const response = await createNewOutlet(newOutlet);

      if (response.success) {
        toast("Outlet created successfully");
        setOpen(false);
        onSuccess();
        // Reset form
        setNewOutlet({
          name: "",
          address: "",
          city: "",
          pincode: "",
          contact: "",
          foodChain: localStorage.getItem("chainId") || "",
          isCloudKitchen: false,
        });
      } else {
        toast(response.message || "Failed to create outlet");
      }
    } catch (error) {
      console.log(error);
      toast("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <div
            className="h-12 rounded-md hover:shadow-2xl cursor-pointer flex justify-between items-center"
            style={{ border: `1px solid ${theme.primaryColor}` }}
          >
            <div className="p-3">Add new</div>
            <div
              //   style={{ background: theme.primaryColor }}
              className="h-full w-10 rounded-r-sm flex justify-center items-center bg-gray-100"
            >
              <Icon
                className="hover:rotate-90 transition-transform duration-300"
                icon="ic:round-add"
                width="24"
                height="24"
                color={theme.primaryColor}
              />
            </div>
          </div>
        </SheetTrigger>
        <SheetContent
          className="sm:max-w-[425px]"
          style={{ color: theme.primaryColor }}
        >
          <SheetHeader>
            <SheetTitle>Add New Outlet</SheetTitle>
            <SheetDescription>
              Fill the details to create a new outlet
            </SheetDescription>
          </SheetHeader>
          <div className="grid gap-4 py-4 p-4">
            <div className="grid gap-2">
              <label htmlFor="name">Outlet Name</label>
              <Input
                id="name"
                name="name"
                value={newOutlet.name}
                onChange={handleInputChange}
                placeholder="Enter outlet name"
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="address">Address</label>
              <Input
                id="address"
                name="address"
                value={newOutlet.address}
                onChange={handleInputChange}
                placeholder="Enter outlet address"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <label htmlFor="city">City</label>
                <Input
                  id="city"
                  name="city"
                  value={newOutlet.city}
                  onChange={handleInputChange}
                  placeholder="Enter city"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="pincode">Pincode</label>
                <Input
                  id="pincode"
                  name="pincode"
                  value={newOutlet.pincode}
                  onChange={handleInputChange}
                  placeholder="Enter pincode"
                />
              </div>
            </div>
            <div className="grid gap-2">
              <label htmlFor="contact">Contact Number</label>
              <Input
                id="contact"
                name="contact"
                value={newOutlet.contact}
                onChange={handleInputChange}
                placeholder="Enter contact number"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isCloudKitchen"
                checked={newOutlet.isCloudKitchen}
                onCheckedChange={handleCheckboxChange}
              />
              <label
                htmlFor="isCloudKitchen"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Is this a cloud kitchen?
              </label>
            </div>
          </div>
          <SheetFooter>
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={loading}
              style={{ backgroundColor: theme.primaryColor }}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <Icon
                    icon="lucide:loader-2"
                    className="w-4 h-4 animate-spin"
                  />
                  <span>Creating...</span>
                </div>
              ) : (
                "Create Outlet"
              )}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default CreateOutletDrawer;
