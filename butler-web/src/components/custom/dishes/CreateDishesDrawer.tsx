"use client";
import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useTheme } from "@/contexts/ThemeContext";
import {
  Sheet,
  She<PERSON><PERSON>rigger,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>ooter,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetDescription,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  createNewCategory,
  createNewDish,
  getAllOutlets,
  getFood<PERSON>hain,
  updateDish,
} from "@/server/admin";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { Category, Dish, Outlet } from "@/app/type";
import { DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { defaultCuisines } from "@/app/helper/dish";
import { X } from "lucide-react";

interface CreateDishesDrawerProps {
  onSuccess: () => void;
  isUpdate: boolean;
  dishToEdit?: Dish;
  onClose: () => void;
  preselectedOutletId?: string;
}

const CreateDishesDrawer: React.FC<CreateDishesDrawerProps> = ({
  onSuccess,
  isUpdate,
  dishToEdit,
  onClose,
  preselectedOutletId,
}) => {
  const [open, setOpen] = useState(isUpdate ? true : false);
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoryMenu, setCategoryMenu] = useState(false);
  const [cuisines] = useState<string[]>(defaultCuisines);

  const [newDish, setNewDish] = useState<Dish>(
    isUpdate
      ? dishToEdit
        ? dishToEdit
        : {
            name: "",
            description: "",
            price: 0,
            image: "",
            category: "",
            cuisine: "",
            foodChain: "",
            outlets: [],
            isAvailable: true,
            isVeg: true,
          }
      : {
          name: "",
          description: "",
          price: 0,
          image: "",
          category: "",
          cuisine: "",
          foodChain: "",
          outlets: [],
          isAvailable: true,
          isVeg: true,
        }
  );

  const [newCategory, setNewCategory] = useState({
    name: "",
    description: "",
  });

  useEffect(() => {
    if (isUpdate) {
      setOpen(true);
      // Make a deep copy of the dish to edit
      const dishCopy = dishToEdit ? { ...dishToEdit } : ({} as Dish);

      // Handle category properly - ensure it's a string ID
      if (
        dishCopy.category &&
        typeof dishCopy.category === "object" &&
        dishCopy.category._id
      ) {
        dishCopy.category = dishCopy.category._id;
      }

      setNewDish(dishCopy);
    } else {
      setOpen(false);
      setNewDish({
        name: "",
        description: "",
        price: 0,
        image: "",
        category: "",
        cuisine: "",
        foodChain: "",
        outlets: [],
        isAvailable: true,
        isVeg: true,
      });
    }
  }, [isUpdate, dishToEdit]);

  const fetchInitialData = async () => {
    try {
      // Fetch outlets
      const outletsResponse = await getAllOutlets();
      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
      }

      // Fetch food chain details for categories
      const chainResponse = await getFoodChain();
      if (chainResponse.success) {
        setCategories(chainResponse.data.categories);
      }
    } catch (error) {
      console.log(error);
      toast("Failed to load initial data");
    }
  };
  useEffect(() => {
    if (open) {
      fetchInitialData();
    }
  }, [open]);

  // Debug log for category
  useEffect(() => {
    if (isUpdate && newDish?.category) {
      console.log("Category in dish:", newDish.category);
      console.log("Categories available:", categories);
    }
  }, [isUpdate, newDish?.category, categories]);

  // Handle preselected outlet ID
  useEffect(() => {
    if (preselectedOutletId && outlets.length > 0) {
      // Add the preselected outlet to the dish outlets if it's not already there
      if (!newDish.outlets.includes(preselectedOutletId)) {
        setNewDish((prev) => ({
          ...prev,
          outlets: [...prev.outlets, preselectedOutletId],
        }));
      }
    }
  }, [preselectedOutletId, outlets, newDish.outlets]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewDish((prev) => ({
      ...prev,
      [name]: name === "price" ? parseFloat(value) || 0 : value,
    }));
  };

  const handleCategoryInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setNewCategory((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleOutletSelection = (outletId: string) => {
    setNewDish((prev) => ({
      ...prev,
      outlets: prev.outlets.includes(outletId)
        ? prev.outlets.filter((id) => id !== outletId)
        : [...prev.outlets, outletId],
    }));
  };

  const validateForm = () => {
    if (!newDish.name.trim()) {
      toast("Dish name is required");
      return false;
    }
    if (!newDish?.description?.trim()) {
      toast("Description is required");
      return false;
    }
    if (!newDish.price || newDish.price <= 0) {
      toast("Valid price is required");
      return false;
    }
    if (!newDish.category) {
      toast("Category is required");
      return false;
    }
    if (!newDish.cuisine) {
      toast("Cuisine is required");
      return false;
    }
    if (newDish.outlets.length === 0) {
      toast("Please select at least one outlet");
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      let response;
      if (isUpdate) {
        response = await updateDish({
          ...newDish,
        });
      } else {
        response = await createNewDish({
          ...newDish,
          foodChain: localStorage.getItem("chainId") || "",
        });
      }

      if (response.success) {
        toast("Dish created successfully");
        setOpen(false);
        onSuccess();
        // Reset form
        setNewDish({
          name: "",
          description: "",
          price: 0,
          image: "",
          category: "",
          cuisine: "",
          foodChain: localStorage.getItem("chainId") || "",
          outlets: [],
          isAvailable: true,
          isVeg: true,
        });
      } else {
        toast(response.message || "Failed to create dish");
      }
    } catch (error) {
      console.log(error);
      toast("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleNewCategory = async () => {
    if (!newCategory.name.trim()) {
      toast("Category name is required");
      return;
    }
    try {
      setLoading(true);
      await createNewCategory(newCategory);
      const chainResponse = await getFoodChain();
      if (chainResponse.success) {
        setCategories(chainResponse.data.categories);
      }
      toast("Category created successfully");
      setCategoryMenu(false);
      setNewCategory({ name: "", description: "" });
    } catch (error) {
      console.log(error);
      toast("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };
  console.log(newDish, "new dish");

  return (
    <div>
      <Sheet
        open={open}
        onOpenChange={(newOpen) => {
          setOpen(newOpen);
          if (!newOpen) {
            onClose();
          }
        }}
      >
        {!isUpdate && (
          <SheetTrigger asChild>
            <div
              className="h-12 rounded-md hover:shadow-2xl cursor-pointer flex justify-between items-center"
              style={{ border: `2px solid ${theme.primaryColor}` }}
            >
              <div className="p-3">Add new</div>
              <div
                //   style={{ background: theme.primaryColor }}
                className="h-full w-10 rounded-r-sm flex justify-center items-center bg-gray-100"
              >
                <Icon
                  className="hover:rotate-90 transition-transform duration-300"
                  icon="ic:round-add"
                  width="24"
                  height="24"
                  color={theme.primaryColor}
                />
              </div>
            </div>
          </SheetTrigger>
        )}

        <SheetContent
          className="sm:max-w-[425px] overflow-y-auto"
          style={{ color: theme.primaryColor }}
        >
          <SheetHeader>
            <SheetTitle>{isUpdate ? "Update" : "Add"} New Dish</SheetTitle>
            <SheetDescription>
              {isUpdate ? "Update" : "Add"} new Dish to the menu
            </SheetDescription>
          </SheetHeader>
          <div className="grid gap-4 py-4 p-4">
            <div className="grid gap-2">
              <label htmlFor="name">Dish Name</label>
              <Input
                id="name"
                name="name"
                value={newDish.name}
                onChange={handleInputChange}
                placeholder="Enter dish name"
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="description">Description</label>
              <Input
                id="description"
                name="description"
                value={newDish.description}
                onChange={handleInputChange}
                placeholder="Enter dish description"
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="price">Price</label>
              <Input
                id="price"
                name="price"
                value={newDish.price}
                type="number"
                onChange={handleInputChange}
                placeholder="Enter dish price"
              />
            </div>
            <div className="grid gap-2">
              <div className="flex gap-2 items-end">
                <div>
                  <label>Category</label>
                  <Select
                    value={
                      newDish?.category ? String(newDish.category) : undefined
                    }
                    onValueChange={(value) =>
                      setNewDish((prev) => ({ ...prev, category: value }))
                    }
                    defaultValue={
                      newDish?.category ? String(newDish.category) : undefined
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem
                          key={category._id}
                          value={String(category._id)}
                        >
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div
                  className="mb-2 cursor-pointer"
                  onClick={() => setCategoryMenu(true)}
                >
                  <Icon icon="formkit:add" width="24" height="24" />
                </div>
              </div>
              <div>
                {categoryMenu && (
                  <div className="w-56 border rounded-md p-2">
                    <div className="flex justify-between items-center">
                      <div>Add Category</div>
                      <div onClick={() => setCategoryMenu(false)}>
                        <X />
                      </div>
                    </div>
                    <div className="grid gap-4 py-4 p-4">
                      <div className="grid gap-2">
                        <label htmlFor="name">Category Name *</label>
                        <Input
                          id="name"
                          name="name"
                          autoFocus={true}
                          value={newCategory.name}
                          onChange={handleCategoryInputChange}
                          placeholder="Enter Category name"
                        />
                      </div>
                      <div className="grid gap-2">
                        <label htmlFor="name">Category Description</label>
                        <Input
                          id="description"
                          name="description"
                          value={newCategory.description}
                          onChange={handleCategoryInputChange}
                          placeholder="Enter Description"
                        />
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                    <div className="flex justify-end">
                      <Button onClick={() => handleNewCategory()}>Add</Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="grid gap-2">
              <label>Cuisine</label>
              <Select
                value={newDish.cuisine}
                onValueChange={(value) =>
                  setNewDish((prev) => ({ ...prev, cuisine: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select cuisine" />
                </SelectTrigger>
                <SelectContent>
                  {cuisines.map((cuisine) => (
                    <SelectItem key={cuisine} value={cuisine}>
                      {cuisine}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {!isUpdate && (
              <div className="grid gap-2">
                <label>Outlets</label>

                <ScrollArea className="h-[200px] border rounded-md p-2">
                  <div className="space-y-2">
                    {outlets.map((outlet) => (
                      <div
                        key={outlet._id}
                        className="flex items-center space-x-2"
                        onClick={() => console.log("hi")}
                      >
                        <Checkbox
                          id={outlet._id}
                          checked={newDish.outlets.includes(outlet._id!)}
                          onCheckedChange={() =>
                            handleOutletSelection(outlet._id!)
                          }
                        />
                        <label
                          htmlFor={outlet._id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {outlet.name}
                          <div className="text-xs font-light truncate">
                            {outlet.address}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isAvailable"
                checked={newDish.isAvailable}
                onCheckedChange={(checked) =>
                  setNewDish((prev) => ({ ...prev, isAvailable: !!checked }))
                }
              />
              <label htmlFor="isAvailable" className="text-sm font-medium">
                Is this dish available?
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isVeg"
                checked={newDish.isVeg}
                onCheckedChange={(checked) =>
                  setNewDish((prev) => ({ ...prev, isVeg: !!checked }))
                }
              />
              <label htmlFor="isVeg" className="text-sm font-medium">
                Is this dish Veg?
              </label>
            </div>
          </div>
          {!isUpdate && (
            <div className="px-4 py-2 mb-4 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700">
              <p className="font-medium mb-1">Ingredients Management</p>
              <p>
                After creating the dish, you can manage its ingredients from the
                dish list page by clicking the{" "}
                <span className="font-bold">Ingredients</span> button. This will
                allow you to link inventory items to the dish for automatic
                inventory tracking.
              </p>
            </div>
          )}

          <SheetFooter>
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={loading}
              style={{ backgroundColor: theme.primaryColor }}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <Icon
                    icon="lucide:loader-2"
                    className="w-4 h-4 animate-spin"
                  />
                  <span>{isUpdate ? "Updating..." : "Creating..."} </span>
                </div>
              ) : isUpdate ? (
                "Update Dish"
              ) : (
                "Create Dish"
              )}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default CreateDishesDrawer;
