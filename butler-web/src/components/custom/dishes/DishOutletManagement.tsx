"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Dish, Outlet, OutletPricing } from "@/app/type";
import { getAllOutlets, updateDish } from "@/server/admin";
import { toast } from "sonner";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Loader2, Store } from "lucide-react";

interface DishOutletManagementProps {
  dish: Dish;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export default function DishOutletManagement({
  dish,
  open,
  onOpenChange,
  onSuccess,
}: DishOutletManagementProps) {
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [selectedOutlets, setSelectedOutlets] = useState<string[]>(
    dish.outlets || []
  );
  const [outletPricing, setOutletPricing] = useState<OutletPricing[]>(
    dish.outletPricing || []
  );

  useEffect(() => {
    if (open) {
      fetchOutlets();
    }
  }, [open]);

  const fetchOutlets = async () => {
    try {
      setLoading(true);
      const response = await getAllOutlets();
      if (response.success) {
        setOutlets(response.data);
      } else {
        toast.error(response.message || "Failed to fetch outlets");
      }
    } catch (error) {
      console.error("Error fetching outlets:", error);
      toast.error("An error occurred while fetching outlets");
    } finally {
      setLoading(false);
    }
  };

  const handleOutletSelection = (outletId: string) => {
    if (selectedOutlets.includes(outletId)) {
      // Remove outlet
      setSelectedOutlets(selectedOutlets.filter((id) => id !== outletId));

      // Remove pricing for this outlet
      setOutletPricing(outletPricing.filter((p) => p.outletId !== outletId));
    } else {
      // Add outlet
      setSelectedOutlets([...selectedOutlets, outletId]);

      // Add default pricing (same as dish price)
      setOutletPricing([...outletPricing, { outletId, price: dish.price }]);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // First, update the dish with new outlet associations and pricing
      const updatedDish = {
        ...dish,
        outlets: selectedOutlets,
        outletPricing,
      };

      const response = await updateDish(updatedDish);

      if (response.success) {
        toast.success("Dish outlets updated successfully");
        if (onSuccess) onSuccess();
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to update dish outlets");
      }
    } catch (error) {
      console.error("Error updating dish outlets:", error);
      toast.error("An error occurred while updating dish outlets");
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Outlets for {dish.name}</DialogTitle>
          <DialogDescription>
            Select which outlets this dish is available at.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : outlets.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No outlets found. Please create outlets first.</p>
          </div>
        ) : (
          <div className="space-y-4 py-4">
            <div className="space-y-4">
              {outlets.map((outlet) => (
                <div key={outlet._id} className="border rounded-md p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        id={outlet._id}
                        checked={selectedOutlets.includes(outlet._id || "")}
                        onCheckedChange={() =>
                          handleOutletSelection(outlet._id || "")
                        }
                      />
                      <div>
                        <Label
                          htmlFor={outlet._id}
                          className="font-medium cursor-pointer"
                        >
                          <div className="flex items-center">
                            <Store className="h-4 w-4 mr-2 text-gray-500" />
                            {outlet.name}
                          </div>
                        </Label>
                        <p className="text-xs text-gray-500 mt-1">
                          {outlet.address}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving || loading}>
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
