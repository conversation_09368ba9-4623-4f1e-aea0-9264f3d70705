"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Dish, TimeAvailability } from "@/app/type";
import { updateDish } from "@/server/admin";
import { toast } from "sonner";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface DishAvailabilitySettingsProps {
  dish: Dish;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export default function DishAvailabilitySettings({
  dish,
  open,
  onOpenChange,
  onSuccess,
}: DishAvailabilitySettingsProps) {
  const [saving, setSaving] = useState(false);
  const [isAvailable, setIsAvailable] = useState<boolean>(
    dish.isAvailable || true
  );
  // Removed seasonal availability as per requirements
  const [timeAvailability, setTimeAvailability] = useState<TimeAvailability>(
    dish.timeAvailability || {
      breakfast: true,
      lunch: true,
      dinner: true,
      customHours: false,
      startTime: "08:00",
      endTime: "22:00",
    }
  );

  const handleSave = async () => {
    try {
      setSaving(true);

      // Validate custom hours if enabled
      if (
        timeAvailability.customHours &&
        (!timeAvailability.startTime || !timeAvailability.endTime)
      ) {
        toast.error("Please set both start and end times for custom hours");
        setSaving(false);
        return;
      }

      // Make sure at least one time slot is selected if not using custom hours
      if (
        !timeAvailability.customHours &&
        !timeAvailability.breakfast &&
        !timeAvailability.lunch &&
        !timeAvailability.dinner
      ) {
        toast.error(
          "Please select at least one time slot (Breakfast, Lunch, or Dinner)"
        );
        setSaving(false);
        return;
      }

      const updatedDish = {
        ...dish,
        isAvailable,
        isSeasonal: false, // Always set to false as we're removing seasonal availability
        seasonalAvailability: undefined, // Remove seasonal availability
        timeAvailability,
      };

      const response = await updateDish(updatedDish);

      if (response.success) {
        toast.success("Dish availability updated successfully");
        if (onSuccess) onSuccess();
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to update dish availability");
      }
    } catch (error) {
      console.error("Error updating dish availability:", error);
      toast.error("An error occurred while updating dish availability");
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Availability Settings for {dish.name}</DialogTitle>
          <DialogDescription>
            Configure when this dish is available to customers.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="time">Time-Based</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="availability" className="text-base">
                    Dish Availability
                  </Label>
                  <p className="text-sm text-gray-500">
                    Enable or disable this dish on the menu
                  </p>
                </div>
                <Switch
                  id="availability"
                  checked={isAvailable}
                  onCheckedChange={setIsAvailable}
                />
              </div>

              {/* Seasonal availability section removed */}
            </div>
          </TabsContent>

          <TabsContent value="time" className="space-y-4 py-4">
            <div className="space-y-4">
              <div>
                <Label className="text-base">Meal Time Availability</Label>
                <p className="text-sm text-gray-500 mb-3">
                  Select which meal times this dish is available for
                </p>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="breakfast"
                      checked={timeAvailability.breakfast}
                      onCheckedChange={(checked) =>
                        setTimeAvailability({
                          ...timeAvailability,
                          breakfast: checked === true,
                        })
                      }
                    />
                    <Label htmlFor="breakfast">Breakfast</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="lunch"
                      checked={timeAvailability.lunch}
                      onCheckedChange={(checked) =>
                        setTimeAvailability({
                          ...timeAvailability,
                          lunch: checked === true,
                        })
                      }
                    />
                    <Label htmlFor="lunch">Lunch</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="dinner"
                      checked={timeAvailability.dinner}
                      onCheckedChange={(checked) =>
                        setTimeAvailability({
                          ...timeAvailability,
                          dinner: checked === true,
                        })
                      }
                    />
                    <Label htmlFor="dinner">Dinner</Label>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="customHours" className="text-base">
                      Custom Hours
                    </Label>
                    <p className="text-sm text-gray-500">
                      Set specific hours when this dish is available
                    </p>
                  </div>
                  <Switch
                    id="customHours"
                    checked={timeAvailability.customHours}
                    onCheckedChange={(checked) =>
                      setTimeAvailability({
                        ...timeAvailability,
                        customHours: checked,
                      })
                    }
                  />
                </div>

                {timeAvailability.customHours && (
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div className="space-y-2">
                      <Label htmlFor="startTime">Start Time</Label>
                      <Input
                        id="startTime"
                        type="time"
                        value={timeAvailability.startTime}
                        onChange={(e) =>
                          setTimeAvailability({
                            ...timeAvailability,
                            startTime: e.target.value,
                          })
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="endTime">End Time</Label>
                      <Input
                        id="endTime"
                        type="time"
                        value={timeAvailability.endTime}
                        onChange={(e) =>
                          setTimeAvailability({
                            ...timeAvailability,
                            endTime: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
