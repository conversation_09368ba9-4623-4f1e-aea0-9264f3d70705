/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { getApplicableOffers } from "@/server/marketing";
import { Offer } from "@/app/type";
import {
  Gift,
  Percent,
  Tag,
  Clock,
  Users,
  ShoppingCart,
  Sparkles,
  CheckCircle,
  Loader2,
} from "lucide-react";

interface ApplicableOffersProps {
  orderData: {
    outletId: string;
    orderAmount: number;
    customerId?: string;
    items: any[];
    foodChainId?: string;
  };
  onOfferApply?: (offer: Offer) => void;
  appliedOffers?: string[]; // Array of applied offer IDs
  className?: string;
}

export default function ApplicableOffers({
  orderData,
  onOfferApply,
  appliedOffers = [],
  className = "",
}: ApplicableOffersProps) {
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(false);
  const [applyingOffer, setApplyingOffer] = useState<string | null>(null);

  const fetchApplicableOffers = useCallback(async () => {
    try {
      setLoading(true);
      console.log("🛒 Fetching applicable offers with order data:", orderData);

      const response = await getApplicableOffers(orderData);

      console.log("🎯 Applicable offers API response:", response);

      if (response.success) {
        console.log("✅ Found applicable offers:", response.data);
        setOffers(response.data || []);
      } else {
        console.error(
          "❌ Failed to fetch applicable offers:",
          response.message
        );
      }
    } catch (error) {
      console.error("💥 Error fetching applicable offers:", error);
    } finally {
      setLoading(false);
    }
  }, [orderData]);

  useEffect(() => {
    if (orderData.orderAmount > 0 && orderData.items.length > 0) {
      fetchApplicableOffers();
    }
  }, [orderData, fetchApplicableOffers]);

  const handleApplyOffer = async (offer: Offer) => {
    if (!onOfferApply || !offer._id) return;

    try {
      setApplyingOffer(offer._id);
      onOfferApply(offer);
      toast.success(`${offer.name} applied successfully!`);
    } catch (error) {
      console.error("Error applying offer:", error);
      toast.error("Failed to apply offer");
    } finally {
      setApplyingOffer(null);
    }
  };

  const getOfferIcon = (offerType: string) => {
    switch (offerType) {
      case "BOGO":
        return <Gift className="h-5 w-5" />;
      case "discount":
      case "minimumAmount":
      case "quantityDiscount":
        return <Percent className="h-5 w-5" />;
      case "freeItem":
        return <Gift className="h-5 w-5" />;
      case "combo":
        return <ShoppingCart className="h-5 w-5" />;
      case "customerTier":
        return <Users className="h-5 w-5" />;
      case "firstTime":
        return <Sparkles className="h-5 w-5" />;
      case "timeBasedSpecial":
      case "dayOfWeek":
        return <Clock className="h-5 w-5" />;
      default:
        return <Tag className="h-5 w-5" />;
    }
  };

  const getOfferTypeColor = (offerType: string) => {
    const typeColors: { [key: string]: string } = {
      BOGO: "bg-green-100 text-green-800 border-green-200",
      combo: "bg-blue-100 text-blue-800 border-blue-200",
      discount: "bg-purple-100 text-purple-800 border-purple-200",
      freeItem: "bg-orange-100 text-orange-800 border-orange-200",
      quantityDiscount: "bg-indigo-100 text-indigo-800 border-indigo-200",
      multiDishType: "bg-pink-100 text-pink-800 border-pink-200",
      minimumAmount: "bg-yellow-100 text-yellow-800 border-yellow-200",
      dayOfWeek: "bg-cyan-100 text-cyan-800 border-cyan-200",
      dateRange: "bg-red-100 text-red-800 border-red-200",
      customerTier: "bg-emerald-100 text-emerald-800 border-emerald-200",
      firstTime: "bg-violet-100 text-violet-800 border-violet-200",
      timeBasedSpecial: "bg-amber-100 text-amber-800 border-amber-200",
    };
    return typeColors[offerType] || "bg-gray-100 text-gray-800 border-gray-200";
  };

  const getOfferDescription = (offer: Offer) => {
    const { offerType, discountDetails } = offer;

    switch (offerType) {
      case "BOGO":
        return `Buy ${discountDetails.buyQuantity}, Get ${discountDetails.getQuantity} Free`;
      case "discount":
        return discountDetails.discountType === "percentage"
          ? `${discountDetails.discountValue}% Off`
          : `₹${discountDetails.discountValue} Off`;
      case "minimumAmount":
        return `${
          discountDetails.discountType === "percentage"
            ? `${discountDetails.discountValue}% Off`
            : `₹${discountDetails.discountValue} Off`
        } on orders above ₹${discountDetails.minimumOrderValue}`;
      case "quantityDiscount":
        return `${
          discountDetails.discountType === "percentage"
            ? `${discountDetails.discountValue}% Off`
            : `₹${discountDetails.discountValue} Off`
        } when you buy ${discountDetails.buyQuantity}+ items`;
      case "freeItem":
        return `Get Free ${discountDetails.freeItemId?.name || "Item"}`;
      case "combo":
        return `Combo Deal - ₹${discountDetails.comboPrice}`;
      case "firstTime":
        return `First Time Customer Special - ${
          discountDetails.discountType === "percentage"
            ? `${discountDetails.discountValue}% Off`
            : `₹${discountDetails.discountValue} Off`
        }`;
      default:
        return offer.description || "Special Offer";
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Available Offers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Loading offers...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (offers.length === 0) {
    return null; // Don't show the component if no offers are available
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Tag className="h-5 w-5" />
          Available Offers ({offers.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {offers.map((offer, index) => {
          const isApplied = appliedOffers.includes(offer._id || "");
          const isApplying = applyingOffer === offer._id;

          return (
            <div key={offer._id || index}>
              <div className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-start gap-3 flex-1">
                  <div
                    className={`p-2 rounded-lg ${getOfferTypeColor(
                      offer.offerType
                    )}`}
                  >
                    {getOfferIcon(offer.offerType)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-gray-900 truncate">
                        {offer.name}
                      </h4>
                      {offer.autoApply && (
                        <Badge variant="outline" className="text-xs">
                          Auto Apply
                        </Badge>
                      )}
                    </div>

                    <p className="text-sm text-gray-600 mb-2">
                      {getOfferDescription(offer)}
                    </p>

                    {(offer as any).estimatedSavings > 0 && (
                      <p className="text-sm font-medium text-green-600">
                        You save: ₹{(offer as any).estimatedSavings}
                      </p>
                    )}

                    {offer.termsAndConditions && (
                      <p className="text-xs text-gray-500 mt-1">
                        {offer.termsAndConditions}
                      </p>
                    )}
                  </div>
                </div>

                <div className="ml-4">
                  {isApplied ? (
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">Applied</span>
                    </div>
                  ) : (
                    <Button
                      size="sm"
                      onClick={() => handleApplyOffer(offer)}
                      disabled={isApplying}
                      className="bg-primary hover:bg-primary/90"
                    >
                      {isApplying ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        "Apply"
                      )}
                    </Button>
                  )}
                </div>
              </div>

              {index < offers.length - 1 && <Separator className="my-2" />}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
