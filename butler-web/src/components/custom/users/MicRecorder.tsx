"use client";

import { Icon } from "@iconify/react/dist/iconify.js";
import { useEffect, useState, useCallback } from "react";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import { SupportedLanguage } from "../../../../hooks/useLanguagePreference";

interface MicRecorderProps {
  setUserMessage: (message: string) => void;
  language?: SupportedLanguage;
  disabled?: boolean;
  className?: string;
}

// Language mapping for speech recognition
const getRecognitionLanguage = (language: SupportedLanguage): string => {
  const languageMap: Record<SupportedLanguage, string> = {
    en: "en-US",
    hi: "hi-IN",
    "hi-en": "hi-IN", // Will fallback to Hindi, but can understand English too
    ta: "ta-IN",
    te: "te-IN",
    bn: "bn-IN",
    mr: "mr-IN",
    gu: "gu-IN",
    es: "es-ES",
    fr: "fr-FR",
    de: "de-DE",
    it: "it-IT",
    pt: "pt-BR",
    ru: "ru-RU",
    ja: "ja-JP",
    ko: "ko-KR",
    zh: "zh-CN",
    ar: "ar-SA",
  };
  return languageMap[language] || "en-US";
};

const MicRecorder = ({
  setUserMessage,
  language = "en",
  disabled = false,
  className = "",
}: MicRecorderProps) => {
  const [error, setError] = useState<string | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
    isMicrophoneAvailable,
  } = useSpeechRecognition();

  // Handle transcript changes - when user finishes speaking
  useEffect(() => {
    if (transcript && transcript.trim() && !listening) {
      setIsProcessing(true);
      setUserMessage(transcript);
      resetTranscript();

      // Clear processing state after a short delay
      setTimeout(() => {
        setIsProcessing(false);
      }, 500);
    }
  }, [transcript, listening, setUserMessage, resetTranscript]);

  // Check microphone availability
  useEffect(() => {
    if (!isMicrophoneAvailable) {
      setError("Microphone not available");
      setTimeout(() => setError(null), 3000);
    }
  }, [isMicrophoneAvailable]);

  const startListening = useCallback(async () => {
    if (disabled || listening) return;

    try {
      // Clear any previous errors and transcript
      setError(null);
      resetTranscript();
      setIsProcessing(false);

      // Request microphone permission explicitly for mobile
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          });
          // Stop the stream immediately as we only needed permission
          stream.getTracks().forEach((track) => track.stop());
        } catch (permissionError) {
          console.error("Microphone permission denied:", permissionError);
          setError("Microphone access denied");
          setTimeout(() => setError(null), 3000);
          return;
        }
      }

      // Start speech recognition with language and mobile-optimized settings
      SpeechRecognition.startListening({
        language: getRecognitionLanguage(language),
        continuous: true, // Stop after user finishes speaking
        interimResults: false, // Better for mobile performance and accuracy
      });
    } catch (err) {
      console.error("Failed to start speech recognition:", err);
      setError("Failed to start recording");
      setTimeout(() => setError(null), 3000);
    }
  }, [disabled, language, resetTranscript, listening]);

  const stopListening = useCallback(() => {
    if (listening) {
      SpeechRecognition.stopListening();
    }
  }, [listening]);

  const toggleListening = useCallback(() => {
    if (listening) {
      stopListening();
    } else {
      startListening();
    }
  }, [listening, startListening, stopListening]);

  // Don't render if not supported
  if (!browserSupportsSpeechRecognition) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={toggleListening}
        disabled={disabled || isProcessing}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className={`h-12 w-12 md:h-14 md:w-14 rounded-2xl shadow-lg transition-all duration-200 flex items-center justify-center touch-manipulation ${
          disabled || isProcessing
            ? "opacity-50 cursor-not-allowed bg-gray-100"
            : listening
            ? "bg-red-50 border-2 border-red-200 hover:bg-red-100 active:bg-red-200"
            : "bg-gray-50 border-2 border-gray-200 hover:bg-gray-100 active:bg-gray-200"
        }`}
        type="button"
        aria-label={listening ? "Stop recording" : "Start voice recording"}
        style={{
          WebkitTapHighlightColor: "transparent", // Remove tap highlight on iOS
          userSelect: "none", // Prevent text selection
        }}
      >
        {listening ? (
          <div className="flex items-center justify-center">
            <div className="w-6 h-6 rounded-full bg-red-500 animate-pulse relative">
              <div className="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-75" />
            </div>
          </div>
        ) : isProcessing ? (
          <div className="animate-spin">
            <Icon
              icon="eos-icons:loading"
              width="24"
              height="24"
              className="text-blue-500"
            />
          </div>
        ) : (
          <Icon
            icon="fluent:mic-20-regular"
            width="24"
            height="24"
            className={error ? "text-red-500" : "text-gray-600"}
          />
        )}
      </button>

      {/* Error tooltip */}
      {error && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-red-500 text-white text-xs rounded-lg whitespace-nowrap z-50 shadow-lg">
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-500" />
          {error === "not-allowed"
            ? "Microphone access denied"
            : error === "no-speech"
            ? "No speech detected"
            : error === "network"
            ? "Network error"
            : error === "audio-capture"
            ? "Microphone not available"
            : error === "aborted"
            ? "Recording cancelled"
            : "Recording failed"}
        </div>
      )}

      {/* Listening indicator */}
      {listening && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
        </div>
      )}

      {/* Help tooltip */}
      {showTooltip && !listening && !error && !isProcessing && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg whitespace-nowrap z-50 shadow-lg">
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800" />
          Click to speak your message
        </div>
      )}

      {/* Processing indicator */}
      {isProcessing && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-blue-500 text-white text-xs rounded-lg whitespace-nowrap z-50 shadow-lg">
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-blue-500" />
          Processing speech...
        </div>
      )}
    </div>
  );
};

export default MicRecorder;
