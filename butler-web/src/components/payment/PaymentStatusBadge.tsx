"use client";

interface PaymentStatusBadgeProps {
  status: string;
}

export default function PaymentStatusBadge({ status }: PaymentStatusBadgeProps) {
  const getStatusColor = () => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800";
      case "requested":
        return "bg-blue-100 text-blue-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "pending":
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <span
      className={`${getStatusColor()} px-2 py-1 rounded-full text-xs font-medium`}
    >
      {status.toUpperCase()}
    </span>
  );
}
