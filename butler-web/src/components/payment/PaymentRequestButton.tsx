"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Payment } from "@/types/order";
import { IndianRupee } from "lucide-react";

interface PaymentRequestButtonProps {
  orderId: string;
  paymentStatus: string;
  onPaymentRequested: (payment: Payment) => void;
}

export default function PaymentRequestButton({
  orderId,
  paymentStatus,
  onPaymentRequested,
}: PaymentRequestButtonProps) {
  const [loading, setLoading] = useState(false);

  const requestPayment = async () => {
    setLoading(true);
    try {
      console.log(`Requesting payment for order ID: ${orderId}`);

      if (!orderId) {
        console.error("Order ID is missing or invalid");
        toast.error("Cannot request payment: Order ID is missing");
        setLoading(false);
        return;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/${orderId}/payment-request`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      const data = await response.json();
      console.log("Payment request response:", data);

      if (data.success) {
        toast.success("Payment request sent successfully");
        onPaymentRequested(data.data);
      } else {
        console.error("Payment request failed:", data.message);
        toast.error(data.message || "Failed to send payment request");
      }
    } catch (error) {
      console.error("Error requesting payment:", error);
      toast.error("Failed to send payment request");
    } finally {
      setLoading(false);
    }
  };

  // Determine button state based on payment status
  const isDisabled = paymentStatus === "paid" || paymentStatus === "requested";
  const buttonText =
    paymentStatus === "paid"
      ? "Payment Completed"
      : paymentStatus === "requested"
      ? "Payment Requested"
      : "Request Payment";

  return (
    <Button
      onClick={requestPayment}
      disabled={isDisabled || loading}
      variant={paymentStatus === "paid" ? "outline" : "default"}
      className={`w-full ${
        paymentStatus === "paid"
          ? "bg-green-100 text-green-800 hover:bg-green-100"
          : paymentStatus === "requested"
          ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
          : ""
      }`}
    >
      {!loading && <IndianRupee className="w-4 h-4 mr-2" />}
      {loading ? "Processing..." : buttonText}
    </Button>
  );
}
