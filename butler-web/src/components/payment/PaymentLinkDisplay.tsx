"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Payment } from "@/types/order";
import { Co<PERSON>, ExternalLink, Check, CreditCard, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Indian } from "@/lib/currency";

interface PaymentLinkDisplayProps {
  payment: Payment;
  isAdmin?: boolean;
}

export default function PaymentLinkDisplay({
  payment,
  isAdmin = false,
}: PaymentLinkDisplayProps) {
  const [copied, setCopied] = useState(false);
  const [paymentDetails, setPaymentDetails] = useState<Payment>(payment);

  // Poll for payment status updates if payment is not completed
  useEffect(() => {
    if (
      payment.status !== "paid" &&
      payment.status !== "failed" &&
      payment.status !== "cancelled"
    ) {
      const interval = setInterval(async () => {
        try {
          const endpoint = isAdmin
            ? `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/${payment.orderId}/payment`
            : `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/orders/${payment.orderId}/payment`;

          const response = await fetch(endpoint, {
            headers: {
              Authorization: `Bearer ${localStorage.getItem(
                isAdmin ? "auth-token" : "user-token"
              )}`,
            },
          });

          const data = await response.json();
          if (data.success && data.data.payment) {
            setPaymentDetails(data.data.payment);

            // If payment is completed, stop polling
            if (
              data.data.payment.status === "paid" ||
              data.data.payment.status === "failed" ||
              data.data.payment.status === "cancelled"
            ) {
              clearInterval(interval);
            }
          }
        } catch (error) {
          console.error("Error fetching payment status:", error);
        }
      }, 5000); // Poll every 5 seconds

      return () => clearInterval(interval);
    }
  }, [payment.orderId, payment.status, isAdmin]);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(paymentDetails.paymentLink);
    setCopied(true);
    toast.success("Payment link copied to clipboard");
    setTimeout(() => setCopied(false), 2000);
  };

  const getStatusBadgeColor = () => {
    switch (paymentDetails.status) {
      case "paid":
        return "bg-green-100 text-green-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  // Determine if we should show the refresh button
  const showRefreshButton =
    paymentDetails.status !== "paid" &&
    paymentDetails.status !== "failed" &&
    paymentDetails.status !== "cancelled";

  // Function to manually refresh payment status
  const refreshPaymentStatus = async () => {
    try {
      const endpoint = isAdmin
        ? `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/${payment.orderId}/payment`
        : `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/orders/${payment.orderId}/payment`;

      toast.info("Checking payment status...");

      const response = await fetch(endpoint, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem(
            isAdmin ? "auth-token" : "user-token"
          )}`,
        },
      });

      const data = await response.json();
      if (data.success && data.data.payment) {
        setPaymentDetails(data.data.payment);
        toast.success("Payment status updated");
      }
    } catch (error) {
      console.error("Error fetching payment status:", error);
      toast.error("Failed to update payment status");
    }
  };

  return (
    <Card className="shadow-md border-0">
      <CardHeader
        className={`${
          paymentDetails.status === "paid"
            ? "bg-green-50"
            : paymentDetails.status === "failed"
            ? "bg-red-50"
            : "bg-blue-50"
        } rounded-t-lg`}
      >
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <CreditCard
              className={`${
                paymentDetails.status === "paid"
                  ? "text-green-600"
                  : paymentDetails.status === "failed"
                  ? "text-red-600"
                  : "text-blue-600"
              }`}
            />
            <CardTitle className="text-lg">Payment Link</CardTitle>
          </div>
          <Badge className={`${getStatusBadgeColor()}`}>
            {paymentDetails.status.toUpperCase()}
          </Badge>
        </div>
        <CardDescription>
          {paymentDetails.status === "paid"
            ? "Payment has been completed successfully"
            : paymentDetails.status === "failed"
            ? "Payment attempt failed"
            : paymentDetails.status === "cancelled"
            ? "Payment was cancelled"
            : "Share this link with the customer to complete payment"}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <div className="flex-1 truncate border p-2 rounded bg-gray-50 text-sm">
              {paymentDetails.paymentLink}
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={copyToClipboard}
              className="flex-shrink-0"
              title="Copy to clipboard"
            >
              {copied ? <Check size={16} /> : <Copy size={16} />}
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              className="w-full py-5"
              onClick={() => window.open(paymentDetails.paymentLink, "_blank")}
              variant={paymentDetails.status === "paid" ? "outline" : "default"}
            >
              Open Payment Link <ExternalLink size={16} className="ml-2" />
            </Button>

            {showRefreshButton && (
              <Button
                variant="outline"
                className="flex-shrink-0"
                onClick={refreshPaymentStatus}
                title="Refresh payment status"
              >
                <RefreshCw size={16} />
              </Button>
            )}
          </div>

          <div className="bg-gray-50 p-3 rounded-lg text-sm">
            <div className="flex justify-between mb-1">
              <span className="text-gray-600">Amount:</span>
              <span className="font-medium">
                {Indian(paymentDetails.amount)}
              </span>
            </div>
            <div className="flex justify-between mb-1">
              <span className="text-gray-600">Status:</span>
              <span
                className={`font-medium ${
                  paymentDetails.status === "paid"
                    ? "text-green-600"
                    : paymentDetails.status === "failed"
                    ? "text-red-600"
                    : "text-blue-600"
                }`}
              >
                {paymentDetails.status.charAt(0).toUpperCase() +
                  paymentDetails.status.slice(1)}
              </span>
            </div>
            {isAdmin && (
              <>
                <Separator className="my-2" />
                <div className="flex justify-between mb-1">
                  <span className="text-gray-600">Payment ID:</span>
                  <span className="font-medium text-xs">
                    {paymentDetails.razorpayPaymentLinkId.substring(0, 12)}...
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span className="font-medium">
                    {new Date(paymentDetails.createdAt).toLocaleString()}
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
