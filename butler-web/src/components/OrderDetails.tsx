import { format } from "date-fns";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "./ui/card";
import { Badge } from "./ui/badge";

interface OrderDetailsProps {
  order: {
    _id: string;
    orderNumber: string;
    items: Array<{
      dishId: {
        name: string;
        price: number;
      };
      quantity: number;
      price: number;
    }>;
    totalAmount: number;
    couponCode?: string;
    couponDiscount?: number;
    finalAmount?: number;
    status: string;
    createdAt: string;
    outletId: {
      name: string;
    };
    specialInstructions?: string;
  };
}

export const OrderDetails = ({ order }: OrderDetailsProps) => {
  const getStatusColor = (status: string) => {
    const colors = {
      pending: "bg-yellow-100 text-yellow-800",
      confirmed: "bg-blue-100 text-blue-800",
      preparing: "bg-purple-100 text-purple-800",
      ready: "bg-green-100 text-green-800",
      completed: "bg-gray-100 text-gray-800",
      cancelled: "bg-red-100 text-red-800",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Order #{order.orderNumber}</CardTitle>
            <p className="text-sm text-gray-500">
              {format(new Date(order.createdAt), "PPP")}
            </p>
          </div>
          <Badge className={getStatusColor(order.status)}>
            {order.status.toUpperCase()}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <p className="text-sm font-medium">Outlet</p>
            <p className="text-sm text-gray-500">{order?.outletId?.name}</p>
          </div>
          <div>
            <p className="text-sm font-medium mb-2">Items</p>
            <div className="space-y-2">
              {order.items.map((item, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>
                    {item.dishId.name} x {item.quantity}
                  </span>
                  <span>₹{item.price}</span>
                </div>
              ))}
            </div>
          </div>
          {order.specialInstructions && (
            <div>
              <p className="text-sm font-medium">Special Instructions</p>
              <p className="text-sm text-gray-500">
                {order.specialInstructions}
              </p>
            </div>
          )}
          <div className="pt-4 border-t space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">Subtotal</span>
              <span className="font-medium">₹{order.totalAmount}</span>
            </div>

            {order.couponCode &&
              order.couponDiscount &&
              order.couponDiscount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span className="font-medium">
                    Coupon Discount ({order.couponCode})
                  </span>
                  <span className="font-medium">-₹{order.couponDiscount}</span>
                </div>
              )}

            <div className="flex justify-between text-lg font-bold">
              <span>Total</span>
              <span>₹{order.finalAmount || order.totalAmount}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
