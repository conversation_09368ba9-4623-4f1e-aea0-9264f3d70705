"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Indian } from "@/lib/currency";
import { format } from "date-fns";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface FundTransfer {
  _id: string;
  amount: number;
  status: "initiated" | "processing" | "completed" | "failed" | "reversed";
  reference: string;
  description: string;
  transferMethod: string;
  createdAt: string;
  updatedAt: string;
  transferredBy: {
    name: string;
    email: string;
  };
}

interface FundTransfersListProps {
  foodChainId: string;
}

export default function FundTransfersList({
  foodChainId,
}: FundTransfersListProps) {
  const [transfers, setTransfers] = useState<FundTransfer[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchTransfers = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/food-chain/${foodChainId}/transfers?page=${page}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        setTransfers(data.data.transfers);
        setTotalPages(data.data.totalPages);
      }
    } catch (error) {
      console.error("Error fetching transfers:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransfers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [foodChainId, page]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "processing":
        return <Badge className="bg-blue-100 text-blue-800">Processing</Badge>;
      case "initiated":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">Initiated</Badge>
        );
      case "failed":
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      case "reversed":
        return (
          <Badge className="bg-purple-100 text-purple-800">Reversed</Badge>
        );
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  if (loading && transfers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Fund Transfers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">Loading transfers...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fund Transfers</CardTitle>
      </CardHeader>
      <CardContent>
        {transfers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No fund transfers found
          </div>
        ) : (
          <div className="space-y-4">
            {transfers.map((transfer) => (
              <div
                key={transfer._id}
                className="border rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <p className="font-medium">{Indian(transfer.amount)}</p>
                    <p className="text-sm text-gray-500">
                      {format(new Date(transfer.createdAt), "PPP")}
                    </p>
                  </div>
                  {getStatusBadge(transfer.status)}
                </div>

                <div className="text-sm text-gray-600 mb-2">
                  {transfer.description}
                </div>

                <div className="flex justify-between text-xs text-gray-500">
                  <span>Ref: {transfer.reference}</span>
                  <span>Method: {transfer.transferMethod}</span>
                </div>

                <div className="mt-2 text-xs text-gray-500">
                  Transferred by: {transfer.transferredBy?.name || "System"}
                </div>
              </div>
            ))}

            {totalPages > 1 && (
              <div className="flex justify-between items-center mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage((p) => Math.max(1, p - 1))}
                  disabled={page === 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" /> Previous
                </Button>
                <span className="text-sm text-gray-500">
                  Page {page} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages}
                >
                  Next <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
