"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import {
  getRazorpaySetupStatus,
  updateFoodChainAndCreateRazorpayAccount,
  addStakeholder,
  requestRouteConfiguration,
} from "@/server/razorpay";

// Define types for the component
interface FoodChain {
  _id: string;
  name: string;
  email: string;
  phone: string;
  contactPerson?: string;
  businessType?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  legalInfo?: {
    pan?: string;
    gst?: string;
  };
  razorpayAccountId?: string;
  razorpayAccountStatus?: string;
  razorpayRouteEnabled?: boolean;
  razorpayStakeholders?: string[];
}

interface RazorpaySetupProps {
  foodChainId: string;
  onSuccess?: () => void;
}

// Steps in the setup process
const STEPS = {
  BUSINESS: "business",
  ADDRESS: "address",
  LEGAL: "legal",
  FINAL: "final",
  STAKEHOLDER: "stakeholder",
  ACTIVATION: "activation",
  COMPLETED: "completed",
};

export default function RazorpaySetup({
  foodChainId,
  onSuccess,
}: RazorpaySetupProps) {
  const [currentStep, setCurrentStep] = useState<string>(STEPS.BUSINESS);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [foodChain, setFoodChain] = useState<FoodChain | null>(null);

  // Business information state
  const [businessInfo, setBusinessInfo] = useState({
    name: "",
    email: "",
    phone: "",
    contactPerson: "",
    businessType: "individual",
    subcategory: "restaurant",
  });

  // Address information state
  const [addressInfo, setAddressInfo] = useState({
    street: "",
    street2: "",
    city: "",
    state: "",
    postalCode: "",
    country: "IN",
  });

  // Legal information state
  const [legalInfo, setLegalInfo] = useState({
    pan: "",
    gst: "",
  });

  // Stakeholder information state
  const [stakeholderInfo, setStakeholderInfo] = useState({
    name: "",
    email: "",
    pan: "",
    street: "",
    city: "",
    state: "",
    postalCode: "",
    country: "IN",
  });

  // Activation state
  const [acceptTerms, setAcceptTerms] = useState(false);

  // Product ID state
  const [productId, setProductId] = useState("");

  // Fetch the current setup status
  const fetchSetupStatus = async () => {
    setLoading(true);
    try {
      const response = await getRazorpaySetupStatus(foodChainId);

      if (response.success) {
        const { foodChain, setupStatus } = response.data;
        setFoodChain(foodChain);
        setCurrentStep(setupStatus.currentStep);

        // Initialize form data from food chain
        if (foodChain) {
          setBusinessInfo({
            name: foodChain.name || "",
            email: foodChain.email || "",
            phone: foodChain.phone || "",
            contactPerson: foodChain.contactPerson || "",
            businessType: foodChain.businessType || "individual",
            subcategory: foodChain.subcategory || "restaurant",
          });

          if (foodChain.address) {
            setAddressInfo({
              street: foodChain.address.street || "",
              street2: foodChain.address.street2 || "",
              city: foodChain.address.city || "",
              state: foodChain.address.state || "",
              postalCode: foodChain.address.postalCode || "",
              country: foodChain.address.country || "IN",
            });
          }

          if (foodChain.legalInfo) {
            setLegalInfo({
              pan: foodChain.legalInfo.pan || "",
              gst: foodChain.legalInfo.gst || "",
            });
          }

          // Bank account details will be handled manually on Razorpay platform
        }
      }
    } catch (error) {
      console.error("Error fetching Razorpay setup status:", error);
      toast.error("Failed to fetch Razorpay setup status");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSetupStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [foodChainId]);

  // Handle business information form submission
  const handleBusinessInfoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const response = await updateFoodChainAndCreateRazorpayAccount(
        foodChainId,
        {
          name: businessInfo.name,
          email: businessInfo.email,
          phone: businessInfo.phone,
          contactPerson: businessInfo.contactPerson,
          businessType: businessInfo.businessType,
          subcategory: businessInfo.subcategory,
        },
        STEPS.BUSINESS
      );

      if (response.success) {
        toast.success("Business information saved successfully");
        setCurrentStep(response.data.nextStep);
        fetchSetupStatus();
      } else {
        toast.error(response.message || "Failed to save business information");
      }
    } catch (error) {
      console.error("Error saving business information:", error);
      toast.error("Failed to save business information");
    } finally {
      setSubmitting(false);
    }
  };

  // Handle address information form submission
  const handleAddressInfoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const response = await updateFoodChainAndCreateRazorpayAccount(
        foodChainId,
        {
          address: addressInfo,
        },
        STEPS.ADDRESS
      );

      if (response.success) {
        toast.success("Address information saved successfully");
        setCurrentStep(response.data.nextStep);
        fetchSetupStatus();
      } else {
        toast.error(response.message || "Failed to save address information");
      }
    } catch (error) {
      console.error("Error saving address information:", error);
      toast.error("Failed to save address information");
    } finally {
      setSubmitting(false);
    }
  };

  // Handle legal information form submission
  const handleLegalInfoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const response = await updateFoodChainAndCreateRazorpayAccount(
        foodChainId,
        {
          legalInfo: {
            pan: legalInfo.pan,
            gst: legalInfo.gst,
          },
        },
        STEPS.LEGAL
      );

      if (response.success) {
        toast.success("Legal information saved successfully");
        setCurrentStep(response.data.nextStep);
        fetchSetupStatus();
      } else {
        toast.error(response.message || "Failed to save legal information");
      }
    } catch (error) {
      console.error("Error saving legal information:", error);
      toast.error("Failed to save legal information");
    } finally {
      setSubmitting(false);
    }
  };

  // Handle final review and account creation
  const handleCreateAccount = async () => {
    setSubmitting(true);

    try {
      const response = await updateFoodChainAndCreateRazorpayAccount(
        foodChainId,
        {},
        STEPS.FINAL,
        true // Complete setup
      );

      if (response.success) {
        toast.success("Razorpay account created successfully");
        setCurrentStep(response.data.nextStep);
        fetchSetupStatus();
      } else {
        toast.error(response.message || "Failed to create Razorpay account");
      }
    } catch (error) {
      console.error("Error creating Razorpay account:", error);
      toast.error("Failed to create Razorpay account");
    } finally {
      setSubmitting(false);
    }
  };

  // Handle stakeholder information form submission
  const handleStakeholderSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const stakeholderData = {
        name: stakeholderInfo.name,
        email: stakeholderInfo.email,
        addresses: {
          residential: {
            street: stakeholderInfo.street,
            city: stakeholderInfo.city,
            state: stakeholderInfo.state,
            postal_code: stakeholderInfo.postalCode,
            country: stakeholderInfo.country,
          },
        },
        kyc: {
          pan: stakeholderInfo.pan,
        },
        notes: {
          added_by: "Butler App",
        },
      };

      const response = await addStakeholder(foodChainId, stakeholderData);

      if (response.success) {
        toast.success("Stakeholder added successfully");
        setCurrentStep(response.data.nextStep);
        fetchSetupStatus();
      } else {
        toast.error(response.message || "Failed to add stakeholder");
      }
    } catch (error) {
      console.error("Error adding stakeholder:", error);
      toast.error("Failed to add stakeholder");
    } finally {
      setSubmitting(false);
    }
  };

  // Handle route activation
  const handleRouteActivation = async () => {
    if (!acceptTerms) {
      toast.error("You must accept the terms and conditions to activate Route");
      return;
    }

    setSubmitting(true);

    try {
      const response = await requestRouteConfiguration(
        foodChainId,
        acceptTerms
      );

      if (response.success) {
        toast.success("Route activated successfully");
        // Store the product ID for reference
        if (response.data.productConfig && response.data.productConfig.id) {
          setProductId(response.data.productConfig.id);
        }
        setCurrentStep(STEPS.COMPLETED);
        fetchSetupStatus();
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast.error(response.message || "Failed to activate Route");
      }
    } catch (error) {
      console.error("Error activating Route:", error);
      toast.error("Failed to activate Route");
    } finally {
      setSubmitting(false);
    }
  };

  // Handle form input changes
  const handleBusinessInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBusinessInfo((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddressInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setAddressInfo((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleLegalInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLegalInfo((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Bank account details will be handled manually on Razorpay platform

  const handleStakeholderInfoChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setStakeholderInfo((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Render the appropriate step form
  const renderStepForm = () => {
    switch (currentStep) {
      case STEPS.BUSINESS:
        return (
          <form onSubmit={handleBusinessInfoSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Business Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={businessInfo.name}
                  onChange={handleBusinessInfoChange}
                  placeholder="Business Name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Business Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={businessInfo.email}
                  onChange={handleBusinessInfoChange}
                  placeholder="Business Email"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Business Phone</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={businessInfo.phone}
                  onChange={handleBusinessInfoChange}
                  placeholder="Business Phone"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactPerson">Contact Person</Label>
                <Input
                  id="contactPerson"
                  name="contactPerson"
                  value={businessInfo.contactPerson}
                  onChange={handleBusinessInfoChange}
                  placeholder="Contact Person"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessType">Business Type</Label>
                <Select
                  value={businessInfo.businessType}
                  onValueChange={(value) =>
                    setBusinessInfo((prev) => ({
                      ...prev,
                      businessType: value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="individual">Individual</SelectItem>
                    <SelectItem value="proprietorship">
                      Proprietorship
                    </SelectItem>
                    <SelectItem value="partnership">Partnership</SelectItem>
                    <SelectItem value="llp">LLP</SelectItem>
                    <SelectItem value="private_limited">
                      Private Limited
                    </SelectItem>
                    <SelectItem value="public_limited">
                      Public Limited
                    </SelectItem>
                    <SelectItem value="trust">Trust</SelectItem>
                    <SelectItem value="society">Society</SelectItem>
                    <SelectItem value="ngo">NGO</SelectItem>
                    <SelectItem value="educational_institutes">
                      Educational Institute
                    </SelectItem>
                    <SelectItem value="not_yet_registered">
                      Not Yet Registered
                    </SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subcategory">Food Business Type</Label>
                <Select
                  value={businessInfo.subcategory}
                  onValueChange={(value) =>
                    setBusinessInfo((prev) => ({
                      ...prev,
                      subcategory: value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select food business type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="restaurant">Restaurant</SelectItem>
                    <SelectItem value="online_food_ordering">
                      Online Food Ordering
                    </SelectItem>
                    <SelectItem value="food_court">Food Court</SelectItem>
                    <SelectItem value="catering">Catering</SelectItem>
                    <SelectItem value="alcohol">Alcohol</SelectItem>
                    <SelectItem value="restaurant_search_and_booking">
                      Restaurant Search & Booking
                    </SelectItem>
                    <SelectItem value="dairy_products">
                      Dairy Products
                    </SelectItem>
                    <SelectItem value="bakeries">Bakeries</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                className="w-1/3"
                onClick={() => fetchSetupStatus()}
                disabled={submitting}
              >
                Reset
              </Button>
              <Button type="submit" className="w-2/3" disabled={submitting}>
                {submitting ? "Saving..." : "Save & Continue"}
              </Button>
            </div>
          </form>
        );

      case STEPS.ADDRESS:
        return (
          <form onSubmit={handleAddressInfoSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="street">Street Address Line 1</Label>
                <Input
                  id="street"
                  name="street"
                  value={addressInfo.street}
                  onChange={handleAddressInfoChange}
                  placeholder="Street Address Line 1"
                  required
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="street2">Street Address Line 2</Label>
                <Input
                  id="street2"
                  name="street2"
                  value={addressInfo.street2}
                  onChange={handleAddressInfoChange}
                  placeholder="Street Address Line 2"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  name="city"
                  value={addressInfo.city}
                  onChange={handleAddressInfoChange}
                  placeholder="City"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  name="state"
                  value={addressInfo.state}
                  onChange={handleAddressInfoChange}
                  placeholder="State"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  name="postalCode"
                  value={addressInfo.postalCode}
                  onChange={handleAddressInfoChange}
                  placeholder="Postal Code"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  name="country"
                  value={addressInfo.country}
                  onChange={handleAddressInfoChange}
                  placeholder="Country"
                  disabled
                />
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                className="w-1/3"
                onClick={() => handleNavigateToStep(STEPS.BUSINESS)}
                disabled={submitting}
              >
                Back
              </Button>
              <Button type="submit" className="w-2/3" disabled={submitting}>
                {submitting ? "Saving..." : "Save & Continue"}
              </Button>
            </div>
          </form>
        );

      case STEPS.LEGAL:
        return (
          <form onSubmit={handleLegalInfoSubmit} className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Legal Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="pan">PAN Number</Label>
                  <Input
                    id="pan"
                    name="pan"
                    value={legalInfo.pan}
                    onChange={handleLegalInfoChange}
                    placeholder="PAN Number"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gst">GST Number (Optional)</Label>
                  <Input
                    id="gst"
                    name="gst"
                    value={legalInfo.gst}
                    onChange={handleLegalInfoChange}
                    placeholder="GST Number"
                  />
                </div>
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                className="w-1/3"
                onClick={() => handleNavigateToStep(STEPS.ADDRESS)}
                disabled={submitting}
              >
                Back
              </Button>
              <Button type="submit" className="w-2/3" disabled={submitting}>
                {submitting ? "Saving..." : "Save & Continue"}
              </Button>
            </div>
          </form>
        );

      case STEPS.FINAL:
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Business Information</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="font-medium">Business Name:</div>
                <div>{businessInfo.name}</div>
                <div className="font-medium">Email:</div>
                <div>{businessInfo.email}</div>
                <div className="font-medium">Phone:</div>
                <div>{businessInfo.phone}</div>
                <div className="font-medium">Contact Person:</div>
                <div>{businessInfo.contactPerson}</div>
                <div className="font-medium">Business Type:</div>
                <div>{businessInfo.businessType}</div>
                <div className="font-medium">Food Business Type:</div>
                <div>{businessInfo.subcategory}</div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Address Information</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="font-medium">Street Line 1:</div>
                <div>{addressInfo.street}</div>
                <div className="font-medium">Street Line 2:</div>
                <div>{addressInfo.street2}</div>
                <div className="font-medium">City:</div>
                <div>{addressInfo.city}</div>
                <div className="font-medium">State:</div>
                <div>{addressInfo.state}</div>
                <div className="font-medium">Postal Code:</div>
                <div>{addressInfo.postalCode}</div>
                <div className="font-medium">Country:</div>
                <div>{addressInfo.country}</div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Legal Information</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="font-medium">PAN:</div>
                <div>{legalInfo.pan}</div>
                {legalInfo.gst && (
                  <>
                    <div className="font-medium">GST:</div>
                    <div>{legalInfo.gst}</div>
                  </>
                )}
              </div>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <p className="text-yellow-800 text-sm">
                Please review the information above carefully. Once you create
                your Razorpay account, some details cannot be changed.
              </p>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                className="w-1/3"
                onClick={() => handleNavigateToStep(STEPS.LEGAL)}
                disabled={submitting}
              >
                Back
              </Button>
              <Button
                onClick={handleCreateAccount}
                className="w-2/3"
                disabled={submitting}
              >
                {submitting ? "Creating Account..." : "Create Razorpay Account"}
              </Button>
            </div>
          </div>
        );

      case STEPS.STAKEHOLDER:
        return (
          <form onSubmit={handleStakeholderSubmit} className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg mb-4">
              <p className="text-blue-800 text-sm">
                A stakeholder is a person who has ownership or significant
                control over your business. You need to add at least one
                stakeholder to proceed.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="stakeholderName">Stakeholder Name</Label>
                <Input
                  id="stakeholderName"
                  name="name"
                  value={stakeholderInfo.name}
                  onChange={handleStakeholderInfoChange}
                  placeholder="Stakeholder Name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stakeholderEmail">Stakeholder Email</Label>
                <Input
                  id="stakeholderEmail"
                  name="email"
                  type="email"
                  value={stakeholderInfo.email}
                  onChange={handleStakeholderInfoChange}
                  placeholder="Stakeholder Email"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stakeholderPan">Stakeholder PAN</Label>
                <Input
                  id="stakeholderPan"
                  name="pan"
                  value={stakeholderInfo.pan}
                  onChange={handleStakeholderInfoChange}
                  placeholder="Stakeholder PAN"
                  required
                />
              </div>
            </div>

            <h3 className="text-lg font-medium mt-4">Residential Address</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="stakeholderStreet">Street Address</Label>
                <Input
                  id="stakeholderStreet"
                  name="street"
                  value={stakeholderInfo.street}
                  onChange={handleStakeholderInfoChange}
                  placeholder="Street Address"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stakeholderCity">City</Label>
                <Input
                  id="stakeholderCity"
                  name="city"
                  value={stakeholderInfo.city}
                  onChange={handleStakeholderInfoChange}
                  placeholder="City"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stakeholderState">State</Label>
                <Input
                  id="stakeholderState"
                  name="state"
                  value={stakeholderInfo.state}
                  onChange={handleStakeholderInfoChange}
                  placeholder="State"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stakeholderPostalCode">Postal Code</Label>
                <Input
                  id="stakeholderPostalCode"
                  name="postalCode"
                  value={stakeholderInfo.postalCode}
                  onChange={handleStakeholderInfoChange}
                  placeholder="Postal Code"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stakeholderCountry">Country</Label>
                <Input
                  id="stakeholderCountry"
                  name="country"
                  value={stakeholderInfo.country}
                  onChange={handleStakeholderInfoChange}
                  placeholder="Country"
                  disabled
                />
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                className="w-1/3"
                onClick={() => handleNavigateToStep(STEPS.FINAL)}
                disabled={submitting}
              >
                Back
              </Button>
              <Button type="submit" className="w-2/3" disabled={submitting}>
                {submitting ? "Adding Stakeholder..." : "Add Stakeholder"}
              </Button>
            </div>
          </form>
        );

      case STEPS.ACTIVATION:
        return (
          <div className="space-y-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-blue-800 text-sm">
                You&apos;re almost done! The next step is to activate Razorpay
                Route for your account. This will allow you to receive payments
                directly from customers.
              </p>
            </div>

            {foodChain?.razorpayAccountId && (
              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-green-800 font-medium">
                  Razorpay Account Created
                </p>
                <p className="text-sm text-green-700 mt-1">
                  Account ID: {foodChain.razorpayAccountId}
                </p>
                <p className="text-sm text-green-700">
                  Status: {foodChain.razorpayAccountStatus}
                </p>
              </div>
            )}

            {foodChain?.razorpayStakeholders &&
              foodChain.razorpayStakeholders.length > 0 && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-green-800 font-medium">
                    Stakeholders Added: {foodChain.razorpayStakeholders.length}
                  </p>
                </div>
              )}

            <div className="flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={acceptTerms}
                onCheckedChange={(checked) =>
                  setAcceptTerms(checked as boolean)
                }
              />
              <Label htmlFor="terms" className="text-sm">
                I accept the Razorpay Route terms and conditions
              </Label>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                className="w-1/3"
                onClick={() => handleNavigateToStep(STEPS.STAKEHOLDER)}
                disabled={submitting}
              >
                Back
              </Button>
              <Button
                onClick={handleRouteActivation}
                className="w-2/3"
                disabled={submitting || !acceptTerms}
              >
                {submitting ? "Activating Route..." : "Activate Route"}
              </Button>
            </div>
          </div>
        );

      case STEPS.COMPLETED:
        return (
          <div className="space-y-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <p className="text-green-800 font-medium">
                Congratulations! Your Razorpay Route setup is complete.
              </p>
              <p className="text-sm text-green-700 mt-1">
                You can now receive payments directly from customers.
              </p>
            </div>

            {foodChain?.razorpayAccountId && (
              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-green-800 font-medium">
                  Razorpay Account Details
                </p>
                <p className="text-sm text-green-700 mt-1">
                  Account ID: {foodChain.razorpayAccountId}
                </p>
                <p className="text-sm text-green-700">
                  Status: {foodChain.razorpayAccountStatus}
                </p>
                <p className="text-sm text-green-700">
                  Route Enabled: {foodChain.razorpayRouteEnabled ? "Yes" : "No"}
                </p>
              </div>
            )}

            {productId && (
              <div className="bg-yellow-50 p-4 rounded-lg">
                <p className="text-yellow-800 font-medium">
                  Important: Bank Account Setup Required
                </p>
                <p className="text-sm text-yellow-700 mt-1">
                  Please log in to your Razorpay dashboard to complete the bank
                  account setup for your Route product.
                </p>
                <p className="text-sm text-yellow-700 mt-1">
                  Product ID: {productId}
                </p>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            Loading setup status...
          </div>
        );
    }
  };

  // Handle navigation to a specific step
  const handleNavigateToStep = (stepKey: string) => {
    // Only allow navigation to steps that have been completed or the current step
    const steps = [
      STEPS.BUSINESS,
      STEPS.ADDRESS,
      STEPS.LEGAL,
      STEPS.FINAL,
      STEPS.STAKEHOLDER,
      STEPS.ACTIVATION,
      STEPS.COMPLETED,
    ];

    const currentIndex = steps.indexOf(currentStep);
    const targetIndex = steps.indexOf(stepKey);

    // Only allow going back to previous steps or staying at current step
    if (targetIndex <= currentIndex && stepKey !== STEPS.COMPLETED) {
      setCurrentStep(stepKey);
    }
  };

  // Render the progress indicator
  const renderProgressIndicator = () => {
    const steps = [
      { key: STEPS.BUSINESS, label: "Business Info" },
      { key: STEPS.ADDRESS, label: "Address" },
      { key: STEPS.LEGAL, label: "Legal Info" },
      { key: STEPS.FINAL, label: "Review" },
      { key: STEPS.STAKEHOLDER, label: "Stakeholder" },
      { key: STEPS.ACTIVATION, label: "Activation" },
      { key: STEPS.COMPLETED, label: "Completed" },
    ];

    const currentIndex = steps.findIndex((step) => step.key === currentStep);

    return (
      <div className="flex justify-between mb-8">
        {steps.map((step, index) => {
          const isClickable =
            index <= currentIndex && step.key !== STEPS.COMPLETED;
          return (
            <div
              key={step.key}
              className="flex flex-col items-center"
              onClick={() => isClickable && handleNavigateToStep(step.key)}
              style={{ cursor: isClickable ? "pointer" : "default" }}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                  index <= currentIndex
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                {index + 1}
              </div>
              <div className="text-xs mt-1 text-center">{step.label}</div>
            </div>
          );
        })}
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Razorpay Route Setup</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
          <div className="text-center text-gray-500">
            Loading Razorpay setup status...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Razorpay Route Setup</CardTitle>
      </CardHeader>
      <CardContent>
        {renderProgressIndicator()}
        {renderStepForm()}
      </CardContent>
    </Card>
  );
}
