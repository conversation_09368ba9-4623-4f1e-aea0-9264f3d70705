"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

interface BankDetails {
  accountName: string;
  accountNumber: string;
  ifscCode: string;
  bankName: string;
  branchName: string;
  upiId: string;
}

interface BankDetailsFormProps {
  foodChainId: string;
  initialBankDetails?: BankDetails;
  onSuccess?: (bankDetails: BankDetails) => void;
}

export default function BankDetailsForm({
  foodChainId,
  initialBankDetails,
  onSuccess,
}: BankDetailsFormProps) {
  const [bankDetails, setBankDetails] = useState<BankDetails>({
    accountName: "",
    accountNumber: "",
    ifscCode: "",
    bankName: "",
    branchName: "",
    upiId: "",
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (initialBankDetails) {
      setBankDetails(initialBankDetails);
    }
  }, [initialBankDetails]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBankDetails((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/food-chain/${foodChainId}/bank-details`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
          body: JSON.stringify({ bankDetails }),
        }
      );

      const data = await response.json();

      if (data.success) {
        toast.success("Bank details updated successfully");
        if (onSuccess) {
          onSuccess(bankDetails);
        }
      } else {
        toast.error(data.message || "Failed to update bank details");
      }
    } catch (error) {
      console.error("Error updating bank details:", error);
      toast.error("Failed to update bank details");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Bank Account Details</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="accountName">Account Holder Name</Label>
              <Input
                id="accountName"
                name="accountName"
                value={bankDetails.accountName}
                onChange={handleChange}
                placeholder="Account Holder Name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="accountNumber">Account Number</Label>
              <Input
                id="accountNumber"
                name="accountNumber"
                value={bankDetails.accountNumber}
                onChange={handleChange}
                placeholder="Account Number"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="ifscCode">IFSC Code</Label>
              <Input
                id="ifscCode"
                name="ifscCode"
                value={bankDetails.ifscCode}
                onChange={handleChange}
                placeholder="IFSC Code"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="bankName">Bank Name</Label>
              <Input
                id="bankName"
                name="bankName"
                value={bankDetails.bankName}
                onChange={handleChange}
                placeholder="Bank Name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="branchName">Branch Name</Label>
              <Input
                id="branchName"
                name="branchName"
                value={bankDetails.branchName}
                onChange={handleChange}
                placeholder="Branch Name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="upiId">UPI ID (Optional)</Label>
              <Input
                id="upiId"
                name="upiId"
                value={bankDetails.upiId}
                onChange={handleChange}
                placeholder="UPI ID"
              />
            </div>
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? "Saving..." : "Save Bank Details"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
