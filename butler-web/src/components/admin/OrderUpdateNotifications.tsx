"use client";

import { useState, useEffect } from "react";
import { useSocket } from "@/contexts/SocketContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Bell,
  X,
  ExternalLink,
  Clock,
  User,
  MapPin,
  DollarSign,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { playNotificationSound } from "@/utils/notificationSound";

interface OrderUpdateNotification {
  orderId: string;
  orderNumber: string;
  customerName: string;
  outletName: string;
  totalAmount: number;
  timestamp: string;
  message: string;
  id: string; // Unique identifier for the notification
}

interface OrderUpdateNotificationsProps {
  onNavigateToOrder?: (orderId: string) => void;
  onRefreshOrders?: () => void;
}

export default function OrderUpdateNotifications({
  onNavigateToOrder,
  onRefreshOrders,
}: OrderUpdateNotificationsProps) {
  const { adminSocket } = useSocket();
  const [notifications, setNotifications] = useState<OrderUpdateNotification[]>(
    []
  );
  const [isExpanded, setIsExpanded] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [notificationQueue, setNotificationQueue] = useState<
    OrderUpdateNotification[]
  >([]);
  const [isProcessingQueue, setIsProcessingQueue] = useState(false);

  // Process notification queue to handle rapid updates
  useEffect(() => {
    if (notificationQueue.length > 0 && !isProcessingQueue) {
      setIsProcessingQueue(true);

      // Process notifications in batches to avoid overwhelming the UI
      const processNextBatch = () => {
        setNotificationQueue((queue) => {
          if (queue.length === 0) {
            setIsProcessingQueue(false);
            return queue;
          }

          // Take the first notification from queue
          const [nextNotification, ...remainingQueue] = queue;

          // Add it to notifications
          setNotifications((prev) => {
            // Check for duplicates
            const recentNotification = prev.find(
              (n) =>
                n.orderId === nextNotification.orderId &&
                Date.now() - new Date(n.timestamp).getTime() < 30000
            );

            if (recentNotification) {
              return prev.map((n) =>
                n.id === recentNotification.id
                  ? { ...nextNotification, id: recentNotification.id }
                  : n
              );
            }

            const newNotifications = [nextNotification, ...prev];
            return newNotifications.slice(0, 50);
          });

          setUnreadCount((prev) => prev + 1);

          // Schedule next batch processing
          if (remainingQueue.length > 0) {
            setTimeout(processNextBatch, 500); // 500ms delay between notifications
          } else {
            setIsProcessingQueue(false);
          }

          return remainingQueue;
        });
      };

      processNextBatch();
    }
  }, [notificationQueue, isProcessingQueue]);

  useEffect(() => {
    if (!adminSocket) return;

    // Listen for order update notifications
    const handleOrderUpdateNotification = (data: {
      type: string;
      data: Omit<OrderUpdateNotification, "id">;
    }) => {
      console.log("Received order update notification:", data);

      const notification: OrderUpdateNotification = {
        ...data.data,
        id: `${data.data.orderId}-${Date.now()}`, // Create unique ID
      };

      // Add to queue for processing instead of directly to notifications
      setNotificationQueue((prev) => [...prev, notification]);

      // Play notification sound
      playNotificationSound("order-update");

      // Show toast notification
      toast.info(`Order #${notification.orderNumber} updated`, {
        description: `${notification.customerName} modified their order`,
        action: {
          label: "View",
          onClick: () => {
            if (onNavigateToOrder) {
              onNavigateToOrder(notification.orderId);
            }
          },
        },
      });

      // Show browser notification if permission granted
      if (window.Notification && Notification.permission === "granted") {
        new Notification(`Order #${notification.orderNumber} Updated`, {
          body: `${notification.customerName} has modified their order`,
          icon: "/favicon.ico",
          tag: `order-update-${notification.orderId}`, // Prevent duplicate notifications
        });
      }

      // Trigger orders refresh to ensure UI is updated - IMMEDIATE
      if (onRefreshOrders) {
        console.log(
          "🔄 [NOTIFICATION-COMPONENT] Triggering immediate orders refresh"
        );
        onRefreshOrders();

        // Also trigger a delayed refresh to catch any delayed updates
        setTimeout(() => {
          console.log(
            "🔄 [NOTIFICATION-COMPONENT] Triggering delayed orders refresh"
          );
          onRefreshOrders();
        }, 2000);
      }
    };

    adminSocket.on("order-update-notification", handleOrderUpdateNotification);

    return () => {
      adminSocket.off(
        "order-update-notification",
        handleOrderUpdateNotification
      );
    };
  }, [adminSocket, onNavigateToOrder, onRefreshOrders]);

  const handleNotificationClick = (notification: OrderUpdateNotification) => {
    if (onNavigateToOrder) {
      onNavigateToOrder(notification.orderId);
    }
  };

  const handleDismissNotification = (
    notificationId: string,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();
    setNotifications((prev) => prev.filter((n) => n.id !== notificationId));
  };

  const handleClearAll = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      // Mark all as read when expanding
      setUnreadCount(0);
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return "Just now";
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}d ago`;
    }
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)]">
      {/* Notification Header */}
      <Card className="mb-2 shadow-lg border-orange-200 bg-orange-50">
        <CardContent className="p-3">
          <div
            className="flex items-center justify-between cursor-pointer"
            onClick={handleToggleExpanded}
          >
            <div className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-orange-600" />
              <span className="font-medium text-orange-800">Order Updates</span>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {unreadCount}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              {notifications.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClearAll();
                  }}
                  className="text-xs text-orange-600 hover:text-orange-800"
                >
                  Clear All
                </Button>
              )}
              {isExpanded ? (
                <ChevronUp className="h-4 w-4 text-orange-600" />
              ) : (
                <ChevronDown className="h-4 w-4 text-orange-600" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notifications List */}
      {isExpanded && (
        <div className="max-h-96 overflow-y-auto space-y-2">
          {notifications.map((notification) => (
            <Card
              key={notification.id}
              className="cursor-pointer hover:shadow-md transition-shadow border-blue-200 bg-blue-50"
              onClick={() => handleNotificationClick(notification)}
            >
              <CardContent className="p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        #{notification.orderNumber}
                      </Badge>
                      <span className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatTimeAgo(notification.timestamp)}
                      </span>
                    </div>

                    <p className="text-sm font-medium text-blue-800 mb-2">
                      {notification.message}
                    </p>

                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="truncate">
                          {notification.customerName}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        <span className="truncate">
                          {notification.outletName}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        <span>₹{notification.totalAmount.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-1 ml-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleNotificationClick(notification);
                      }}
                      title="View Order"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                      onClick={(e) =>
                        handleDismissNotification(notification.id, e)
                      }
                      title="Dismiss"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
