"use client";
import React, { useState } from "react";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";

interface Route {
  path: string;
  name: string;
  icon: string;
}

interface ResponsiveSidebarProps {
  routes: Route[];
  title: string;
  backgroundColor?: string;
  textColor?: string;
  children?: React.ReactNode;
}

export default function ResponsiveSidebar({
  routes,
  title,
  backgroundColor = "#1f2937", // Default to gray-800
  textColor = "#ffffff", // Default to white
  children,
}: ResponsiveSidebarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <div className="relative">
      {/* Mobile menu button - only visible on small screens */}
      <div
        className="flex justify-between items-center p-4 sm:hidden"
        style={{ backgroundColor, color: textColor }}
      >
        <h1 className="font-bold text-xl">{title}</h1>
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleMenu}
          className="text-white hover:bg-opacity-20 hover:bg-white"
        >
          {isMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </Button>
      </div>

      {/* Sidebar - responsive behavior */}
      <div
        className={cn(
          "p-4 min-h-screen",
          isMenuOpen ? "block" : "hidden",
          "sm:block sm:w-64 md:w-72",
          "fixed sm:relative w-full z-20"
        )}
        style={{ backgroundColor, color: textColor }}
      >
        {/* Desktop header - hidden on mobile */}
        <div className="hidden sm:block">
          <h2 className="text-xl font-bold mb-6">{title}</h2>
        </div>
        {/* Navigation links */}
        <ul className="space-y-2">
          {routes.map((route, index) => (
            <li key={index}>
              <Link
                href={route.path}
                className={cn(
                  "flex items-center gap-3 py-2 px-3 rounded-md transition-colors",
                  "hover:underline hover:bg-opacity-10"
                )}
                onClick={() => setIsMenuOpen(false)}
              >
                <Icon icon={route.icon} width="20" height="20" />
                <span>{route.name}</span>
              </Link>
            </li>
          ))}
        </ul>
        {/* Additional content if provided */}
        {children && <div className="mt-6">{children}</div>}
        {isMenuOpen && (
          <div
            className=" md:hidden absolute z-50 bottom-20 p-4 flex justify-center items-center w-full"
            onClick={() => setIsMenuOpen(false)}
          >
            <Button variant="ghost" size="icon" className="bg-white">
              <X className="h-12 w-12 text-black" />
            </Button>
          </div>
        )}
      </div>

      {/* Overlay for mobile - closes menu when clicking outside */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 sm:hidden"
          onClick={() => setIsMenuOpen(false)}
        />
      )}
    </div>
  );
}
