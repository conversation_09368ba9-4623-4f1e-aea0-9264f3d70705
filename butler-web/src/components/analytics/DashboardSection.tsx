"use client";
import React, { useState } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ChevronDown, ChevronUp, Maximize2, Minimize2 } from "lucide-react";
import { useTheme } from "@/contexts/ThemeContext";

interface DashboardSectionProps {
  title: string;
  children: React.ReactNode;
  defaultCollapsed?: boolean;
  collapsible?: boolean;
  expandable?: boolean;
  className?: string;
  headerActions?: React.ReactNode;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
  gridArea?: string;
}

const DashboardSection: React.FC<DashboardSectionProps> = ({
  title,
  children,
  defaultCollapsed = false,
  collapsible = true,
  expandable = false,
  className = "",
  headerActions,
  loading = false,
  error,
  onRefresh,
  gridArea,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [isExpanded, setIsExpanded] = useState(false);
  const { theme = false } = useTheme();
  // const isDark = theme === "dark";
  const isDark = theme;

  const handleToggleCollapse = () => {
    if (collapsible) {
      setIsCollapsed(!isCollapsed);
    }
  };

  const handleToggleExpand = () => {
    if (expandable) {
      setIsExpanded(!isExpanded);
    }
  };

  const cardStyle = gridArea ? { gridArea } : {};

  return (
    <Card
      className={`transition-all duration-300 bg-white border-gray-200 ${
        isExpanded ? "fixed inset-4 z-50" : ""
      }${className}`}
      style={cardStyle}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <h3
              className={`text-lg font-semibold ${
                isDark ? "text-white" : "text-gray-900"
              }`}
            >
              {title}
            </h3>
            {loading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {headerActions}

            {expandable && (
              <button
                onClick={handleToggleExpand}
                className={`p-1 rounded hover:bg-gray-100 ${
                  isDark ? "hover:bg-gray-700 text-gray-300" : "text-gray-600"
                }`}
              >
                {isExpanded ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </button>
            )}

            {collapsible && (
              <button
                onClick={handleToggleCollapse}
                className={`p-1 rounded hover:bg-gray-100 ${
                  isDark ? "hover:bg-gray-700 text-gray-300" : "text-gray-600"
                }`}
              >
                {isCollapsed ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronUp className="h-4 w-4" />
                )}
              </button>
            )}
          </div>
        </div>
      </CardHeader>

      {!isCollapsed && (
        <CardContent className="pt-0">
          {error ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className={`text-red-500 mb-2`}>Error loading data</div>
              <div
                className={`text-sm ${
                  isDark ? "text-gray-400" : "text-gray-600"
                } mb-4`}
              >
                {error}
              </div>
              {onRefresh && (
                <button
                  onClick={onRefresh}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  Retry
                </button>
              )}
            </div>
          ) : loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            children
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default DashboardSection;
