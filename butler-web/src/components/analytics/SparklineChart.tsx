/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React from "react";
import { LineChart, Line, ResponsiveContainer } from "recharts";

interface SparklineChartProps {
  data: any[];
  dataKey: string;
  color?: string;
  width?: number;
  height?: number;
  strokeWidth?: number;
  showDots?: boolean;
}

const SparklineChart: React.FC<SparklineChartProps> = ({
  data,
  dataKey,
  color = "#8884d8",
  width = 100,
  height = 30,
  strokeWidth = 2,
  showDots = false,
}) => {
  return (
    <div style={{ width, height }}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <Line
            type="monotone"
            dataKey={dataKey}
            stroke={color}
            strokeWidth={strokeWidth}
            dot={showDots ? { fill: color, strokeWidth: 0, r: 2 } : false}
            activeDot={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default SparklineChart;
