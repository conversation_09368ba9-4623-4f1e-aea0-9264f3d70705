/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React, { useState } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Calendar, Clock, Mail, Plus, Trash2, Send } from "lucide-react";
import { useTheme } from "@/contexts/ThemeContext";

interface ScheduledReport {
  id: string;
  name: string;
  frequency: "daily" | "weekly" | "monthly";
  format: "pdf" | "excel";
  recipients: string[];
  enabled: boolean;
  lastSent?: Date;
  nextSend?: Date;
}

interface ScheduledReportsProps {
  reports: ScheduledReport[];
  onCreateReport: (report: Omit<ScheduledReport, "id">) => void;
  onUpdateReport: (id: string, report: Partial<ScheduledReport>) => void;
  onDeleteReport: (id: string) => void;
  onSendNow: (id: string) => void;
}

const ScheduledReports: React.FC<ScheduledReportsProps> = ({
  reports,
  onCreateReport,
  onUpdateReport,
  onDeleteReport,
  onSendNow,
}) => {
  const { theme = false } = useTheme();
  // const isDark = theme === "dark";
  const isDark = theme;

  const [isCreating, setIsCreating] = useState(false);
  const [newReport, setNewReport] = useState({
    name: "",
    frequency: "weekly" as const,
    format: "pdf" as const,
    recipients: [""],
    enabled: true,
  });

  const handleCreateReport = () => {
    if (
      newReport.name &&
      newReport.recipients.filter((r) => r.trim()).length > 0
    ) {
      onCreateReport({
        ...newReport,
        recipients: newReport.recipients.filter((r) => r.trim()),
      });
      setNewReport({
        name: "",
        frequency: "weekly",
        format: "pdf",
        recipients: [""],
        enabled: true,
      });
      setIsCreating(false);
    }
  };

  const addRecipient = () => {
    setNewReport((prev) => ({
      ...prev,
      recipients: [...prev.recipients, ""],
    }));
  };

  const updateRecipient = (index: number, email: string) => {
    setNewReport((prev) => ({
      ...prev,
      recipients: prev.recipients.map((r, i) => (i === index ? email : r)),
    }));
  };

  const removeRecipient = (index: number) => {
    setNewReport((prev) => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index),
    }));
  };

  return (
    <Card
      className={`${
        isDark ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"
      }`}
    >
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Scheduled Reports</h3>
          </div>
          <Button
            size="sm"
            onClick={() => setIsCreating(true)}
            disabled={isCreating}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Report
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Create New Report Form */}
        {isCreating && (
          <Card className="border-2 border-dashed">
            <CardContent className="p-4 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reportName">Report Name</Label>
                <Input
                  id="reportName"
                  placeholder="e.g., Weekly Sales Report"
                  value={newReport.name}
                  onChange={(e) =>
                    setNewReport((prev) => ({ ...prev, name: e.target.value }))
                  }
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Frequency</Label>
                  <Select
                    value={newReport.frequency}
                    onValueChange={(value: any) =>
                      setNewReport((prev) => ({ ...prev, frequency: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Format</Label>
                  <Select
                    value={newReport.format}
                    onValueChange={(value: any) =>
                      setNewReport((prev) => ({ ...prev, format: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Recipients</Label>
                {newReport.recipients.map((recipient, index) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      placeholder="<EMAIL>"
                      value={recipient}
                      onChange={(e) => updateRecipient(index, e.target.value)}
                    />
                    {newReport.recipients.length > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeRecipient(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button variant="outline" size="sm" onClick={addRecipient}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Recipient
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={newReport.enabled}
                  onCheckedChange={(checked) =>
                    setNewReport((prev) => ({ ...prev, enabled: checked }))
                  }
                />
                <Label>Enable immediately</Label>
              </div>

              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateReport}>Create Report</Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Existing Reports */}
        <div className="space-y-3">
          {reports.map((report) => (
            <Card key={report.id} className="border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{report.name}</h4>
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          report.enabled
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {report.enabled ? "Active" : "Disabled"}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{report.frequency}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Mail className="h-3 w-3" />
                        <span>{report.recipients.length} recipients</span>
                      </div>
                      {report.lastSent && (
                        <span>
                          Last sent: {report.lastSent.toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={report.enabled}
                      onCheckedChange={(checked) =>
                        onUpdateReport(report.id, { enabled: checked })
                      }
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onSendNow(report.id)}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDeleteReport(report.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {reports.length === 0 && !isCreating && (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No scheduled reports configured</p>
            <p className="text-sm">
              Create your first automated report to get started
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ScheduledReports;
