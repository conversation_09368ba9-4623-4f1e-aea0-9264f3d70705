/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React from "react";
import {
  AreaChart as RechartsAreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON>ontainer,
  Legend,
} from "recharts";
import { useTheme } from "@/contexts/ThemeContext";

interface AreaChartProps {
  data: any[];
  xKey: string;
  yKeys: { key: string; name: string; color: string }[];
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  formatXAxis?: (value: any) => string;
  formatYAxis?: (value: any) => string;
  formatTooltip?: (value: any, name: string) => [string, string];
  gradient?: boolean;
}

const AreaChart: React.FC<AreaChartProps> = ({
  data,
  xKey,
  yKeys,
  height = 300,
  showGrid = true,
  showLegend = true,
  formatXAxis,
  formatYAxis,
  formatTooltip,
  gradient = true,
}) => {
  const { theme = false } = useTheme();
  // const isDark = theme === "dark";
  const isDark = theme;

  const gridColor = isDark ? "#374151" : "#e5e7eb";
  const textColor = isDark ? "#d1d5db" : "#374151";

  return (
    <div style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsAreaChart
          data={data}
          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
        >
          {gradient && (
            <defs>
              {yKeys.map((yKey, index) => (
                <linearGradient
                  key={index}
                  id={`gradient-${index}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor={yKey.color} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={yKey.color} stopOpacity={0.1} />
                </linearGradient>
              ))}
            </defs>
          )}

          {showGrid && (
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
          )}

          <XAxis
            dataKey={xKey}
            tickFormatter={formatXAxis}
            tick={{ fill: textColor, fontSize: 12 }}
            axisLine={{ stroke: gridColor }}
            tickLine={{ stroke: gridColor }}
          />

          <YAxis
            tickFormatter={formatYAxis}
            tick={{ fill: textColor, fontSize: 12 }}
            axisLine={{ stroke: gridColor }}
            tickLine={{ stroke: gridColor }}
          />

          <Tooltip
            formatter={formatTooltip}
            contentStyle={{
              backgroundColor: isDark ? "#1f2937" : "#ffffff",
              border: `1px solid ${gridColor}`,
              borderRadius: "8px",
              color: textColor,
            }}
          />

          {showLegend && <Legend />}

          {yKeys.map((yKey, index) => (
            <Area
              key={yKey.key}
              type="monotone"
              dataKey={yKey.key}
              stroke={yKey.color}
              fill={gradient ? `url(#gradient-${index})` : yKey.color}
              fillOpacity={gradient ? 1 : 0.3}
              strokeWidth={2}
              name={yKey.name}
            />
          ))}
        </RechartsAreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default AreaChart;
