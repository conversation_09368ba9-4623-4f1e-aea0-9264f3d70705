"use client";
import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Settings, Eye, EyeOff, Save, RotateCcw, Layout } from "lucide-react";
import { useTheme } from "@/contexts/ThemeContext";

interface DashboardWidget {
  id: string;
  name: string;
  description: string;
  visible: boolean;
  order: number;
}

interface DashboardCustomizerProps {
  widgets: DashboardWidget[];
  onWidgetToggle: (widgetId: string, visible: boolean) => void;
  onSaveLayout: () => void;
  onResetLayout: () => void;
}

const DashboardCustomizer: React.FC<DashboardCustomizerProps> = ({
  widgets,
  onWidgetToggle,
  onSaveLayout,
  onResetLayout,
}) => {
  const { theme = false } = useTheme();
  // const isDark = theme === "dark";
  const isDark = theme;
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Customizer Toggle Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 right-4 z-50 shadow-lg"
      >
        <Settings className="h-4 w-4 mr-2" />
        Customize
      </Button>

      {/* Customizer Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <Card
            className={`w-full max-w-md max-h-[80vh] overflow-y-auto ${
              isDark
                ? "bg-gray-800 border-gray-700"
                : "bg-white border-gray-200"
            }`}
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Layout className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Dashboard Layout</h3>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  ×
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Customize which widgets are visible on your dashboard.
              </p>

              {/* Widget Visibility Controls */}
              <div className="space-y-3">
                {widgets.map((widget) => (
                  <div
                    key={widget.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        {widget.visible ? (
                          <Eye className="h-4 w-4 text-green-500" />
                        ) : (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        )}
                        <Label htmlFor={widget.id} className="font-medium">
                          {widget.name}
                        </Label>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {widget.description}
                      </p>
                    </div>
                    <Switch
                      id={widget.id}
                      checked={widget.visible}
                      onCheckedChange={(checked) =>
                        onWidgetToggle(widget.id, checked)
                      }
                    />
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2 pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onResetLayout}
                  className="flex-1"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    onSaveLayout();
                    setIsOpen(false);
                  }}
                  className="flex-1"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
};

export default DashboardCustomizer;
