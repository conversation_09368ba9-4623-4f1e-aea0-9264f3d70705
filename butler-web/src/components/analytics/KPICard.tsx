/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";
import { SparklineChart } from "./index";
import { useTheme } from "@/contexts/ThemeContext";

interface KPICardProps {
  title: string;
  value: string | number;
  previousValue?: string | number;
  change?: number;
  changeLabel?: string;
  sparklineData?: any[];
  sparklineKey?: string;
  icon?: React.ReactNode;
  color?: string;
  subtitle?: string;
  onClick?: () => void;
  loading?: boolean;
  formatValue?: (value: any) => string;
  showSparkline?: boolean;
  trend?: "up" | "down" | "neutral";
  size?: "sm" | "md" | "lg";
}

const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  previousValue,
  change,
  changeLabel = "vs. previous period",
  sparklineData,
  sparklineKey = "value",
  icon,
  color = "#8884d8",
  subtitle,
  onClick,
  loading = false,
  formatValue,
  showSparkline = true,
  trend,
  size = "md",
}) => {
  const { theme = false } = useTheme();
  // const isDark = theme === "dark";
  const isDark = theme;
  console.log(isDark);
  const getTrendIcon = () => {
    if (trend === "up" || (change && change > 0)) {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (trend === "down" || (change && change < 0)) {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    } else {
      return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = () => {
    if (trend === "up" || (change && change > 0)) {
      return "text-green-500";
    } else if (trend === "down" || (change && change < 0)) {
      return "text-red-500";
    } else {
      return "text-gray-500";
    }
  };

  const sizeClasses = {
    sm: "p-4",
    md: "p-6",
    lg: "p-8",
  };

  const valueSizeClasses = {
    sm: "text-xl",
    md: "text-2xl",
    lg: "text-3xl",
  };

  const displayValue = formatValue ? formatValue(value) : value;

  return (
    <Card
      className={`transition-all duration-200 hover:shadow-lg ${
        onClick ? "cursor-pointer hover:scale-105" : ""
      } bg-white border-gray-200`}
      onClick={onClick}
    >
      <CardContent className={sizeClasses[size]}>
        {loading ? (
          <div className="animate-pulse">
            <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-300 rounded w-1/3"></div>
          </div>
        ) : (
          <>
            {/* Header */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                {icon && (
                  <div
                    className="p-2 rounded-lg"
                    style={{ backgroundColor: `${color}20` }}
                  >
                    <div style={{ color }}>{icon}</div>
                  </div>
                )}
                <div>
                  <h3 className={`text-sm font-medium text-gray-600 `}>
                    {title}
                  </h3>
                  {subtitle && (
                    <p className={`text-xs text-gray-500`}>{subtitle}</p>
                  )}
                </div>
              </div>

              {showSparkline && sparklineData && sparklineData.length > 0 && (
                <div className="flex-shrink-0">
                  <SparklineChart
                    data={sparklineData}
                    dataKey={sparklineKey}
                    color={color}
                    width={80}
                    height={30}
                  />
                </div>
              )}
            </div>

            {/* Main Value */}
            <div className="mb-2">
              <div
                className={`font-bold ${valueSizeClasses[size]} text-gray-900`}
              >
                {displayValue}
              </div>
            </div>

            {/* Change Indicator */}
            {(change !== undefined || trend) && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  {getTrendIcon()}
                  <span className={`text-sm font-medium ${getTrendColor()}`}>
                    {change !== undefined
                      ? `${Math.abs(change).toFixed(1)}%`
                      : ""}
                  </span>
                </div>

                <div className={`text-xs text-gray-500`}>{changeLabel}</div>
              </div>
            )}

            {/* Previous Value Comparison */}
            {previousValue !== undefined && (
              <div className={`text-xs mt-1 text-gray-500`}>
                Previous:{" "}
                {formatValue ? formatValue(previousValue) : previousValue}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default KPICard;
