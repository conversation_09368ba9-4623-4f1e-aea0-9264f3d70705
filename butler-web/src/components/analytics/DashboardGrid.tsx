"use client";
import React from "react";
import { useTheme } from "@/contexts/ThemeContext";

interface GridItem {
  id: string;
  component: React.ReactNode;
  gridArea?: string;
  className?: string;
}

interface DashboardGridProps {
  items: GridItem[];
  columns?: number;
  gap?: number;
  className?: string;
  responsive?: boolean;
}

const DashboardGrid: React.FC<DashboardGridProps> = ({
  items,
  columns = 12,
  gap = 4,
  className = "",
  responsive = true,
}) => {
  const { theme = false } = useTheme();
  // const isDark = theme === "dark";
  const isDark = theme;
  console.log(isDark);
  const gridStyle = {
    display: "grid",
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: `${gap * 0.25}rem`,
  };

  const responsiveClasses = responsive
    ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6"
    : "";

  return (
    <div
      className={`w-full ${responsiveClasses} ${className}`}
      style={responsive ? undefined : gridStyle}
    >
      {items.map((item) => (
        <div
          key={item.id}
          className={`${item.className || ""}`}
          style={item.gridArea ? { gridArea: item.gridArea } : undefined}
        >
          {item.component}
        </div>
      ))}
    </div>
  );
};

export default DashboardGrid;
