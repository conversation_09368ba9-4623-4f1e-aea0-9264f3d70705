/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React from "react";
import {
  ComposedChart,
  Line,
  Bar,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { useTheme } from "@/contexts/ThemeContext";

interface ChartSeries {
  key: string;
  name: string;
  type: "line" | "bar";
  color: string;
  yAxisId?: "left" | "right";
}

interface MultiAxisChartProps {
  data: any[];
  xKey: string;
  series: ChartSeries[];
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  formatXAxis?: (value: any) => string;
  formatLeftYAxis?: (value: any) => string;
  formatRightYAxis?: (value: any) => string;
  formatTooltip?: (value: any, name: string) => [string, string];
  leftYAxisLabel?: string;
  rightYAxisLabel?: string;
}

const MultiAxisChart: React.FC<MultiAxisChartProps> = ({
  data,
  xKey,
  series,
  height = 300,
  showGrid = true,
  showLegend = true,
  formatXAxis,
  formatLeftYAxis,
  formatRightYAxis,
  formatTooltip,
  leftYAxisLabel,
  rightYAxisLabel,
}) => {
  const { theme = false } = useTheme();
  // const isDark = theme === "dark";
  const isDark = theme;

  const gridColor = isDark ? "#374151" : "#e5e7eb";
  const textColor = isDark ? "#d1d5db" : "#374151";

  const hasRightAxis = series.some((s) => s.yAxisId === "right");

  return (
    <div style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={data}
          margin={{ top: 20, right: 30, bottom: 20, left: 20 }}
        >
          {showGrid && (
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
          )}

          <XAxis
            dataKey={xKey}
            tickFormatter={formatXAxis}
            tick={{ fill: textColor, fontSize: 12 }}
            axisLine={{ stroke: gridColor }}
            tickLine={{ stroke: gridColor }}
          />

          <YAxis
            yAxisId="left"
            tickFormatter={formatLeftYAxis}
            tick={{ fill: textColor, fontSize: 12 }}
            axisLine={{ stroke: gridColor }}
            tickLine={{ stroke: gridColor }}
            label={
              leftYAxisLabel
                ? {
                    value: leftYAxisLabel,
                    angle: -90,
                    position: "insideLeft",
                    style: { textAnchor: "middle", fill: textColor },
                  }
                : undefined
            }
          />

          {hasRightAxis && (
            <YAxis
              yAxisId="right"
              orientation="right"
              tickFormatter={formatRightYAxis}
              tick={{ fill: textColor, fontSize: 12 }}
              axisLine={{ stroke: gridColor }}
              tickLine={{ stroke: gridColor }}
              label={
                rightYAxisLabel
                  ? {
                      value: rightYAxisLabel,
                      angle: 90,
                      position: "insideRight",
                      style: { textAnchor: "middle", fill: textColor },
                    }
                  : undefined
              }
            />
          )}

          <Tooltip
            formatter={formatTooltip}
            contentStyle={{
              backgroundColor: isDark ? "#1f2937" : "#ffffff",
              border: `1px solid ${gridColor}`,
              borderRadius: "8px",
              color: textColor,
            }}
          />

          {showLegend && <Legend />}

          {series.map((s) => {
            if (s.type === "bar") {
              return (
                <Bar
                  key={s.key}
                  yAxisId={s.yAxisId || "left"}
                  dataKey={s.key}
                  fill={s.color}
                  name={s.name}
                />
              );
            } else {
              return (
                <Line
                  key={s.key}
                  yAxisId={s.yAxisId || "left"}
                  type="monotone"
                  dataKey={s.key}
                  stroke={s.color}
                  strokeWidth={2}
                  dot={{ fill: s.color, strokeWidth: 2, r: 4 }}
                  name={s.name}
                />
              );
            }
          })}
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default MultiAxisChart;
