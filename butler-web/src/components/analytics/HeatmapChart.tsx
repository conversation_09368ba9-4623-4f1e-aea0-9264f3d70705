"use client";
import React from "react";
import { useTheme } from "next-themes";

interface HeatmapData {
  day: number;
  hour: number;
  value: number;
  label?: string;
}

interface HeatmapChartProps {
  data: HeatmapData[];
  width?: number;
  height?: number;
  colorScale?: string[];
  showLabels?: boolean;
  formatValue?: (value: number) => string;
  onCellClick?: (data: HeatmapData) => void;
}

const HeatmapChart: React.FC<HeatmapChartProps> = ({
  data,
  width = 600,
  height = 200,
  colorScale = ["#f0f9ff", "#0ea5e9", "#0369a1"],
  showLabels = false,
  formatValue,
  onCellClick,
}) => {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const hours = Array.from({ length: 24 }, (_, i) => i);

  // Find min and max values for color scaling
  const values = data.map((d) => d.value);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);

  const getColor = (value: number) => {
    if (maxValue === minValue) return colorScale[0];

    const normalized = (value - minValue) / (maxValue - minValue);

    if (normalized <= 0.33) {
      return colorScale[0];
    } else if (normalized <= 0.66) {
      return colorScale[1];
    } else {
      return colorScale[2];
    }
  };

  const getCellData = (day: number, hour: number) => {
    return (
      data.find((d) => d.day === day && d.hour === hour) || {
        day,
        hour,
        value: 0,
      }
    );
  };

  const cellWidth = (width - 60) / 24;
  const cellHeight = (height - 40) / 7;

  return (
    <div className="w-full">
      <svg width={width} height={height} className="overflow-visible">
        {/* Hour labels */}
        {hours.map((hour) => (
          <text
            key={`hour-${hour}`}
            x={60 + hour * cellWidth + cellWidth / 2}
            y={15}
            textAnchor="middle"
            className={`text-xs ${isDark ? "fill-gray-300" : "fill-gray-600"}`}
          >
            {hour === 0
              ? "12AM"
              : hour === 12
              ? "12PM"
              : hour < 12
              ? `${hour}AM`
              : `${hour - 12}PM`}
          </text>
        ))}

        {/* Day labels */}
        {days.map((day, dayIndex) => (
          <text
            key={`day-${dayIndex}`}
            x={50}
            y={30 + dayIndex * cellHeight + cellHeight / 2}
            textAnchor="end"
            dominantBaseline="middle"
            className={`text-xs ${isDark ? "fill-gray-300" : "fill-gray-600"}`}
          >
            {day}
          </text>
        ))}

        {/* Heatmap cells */}
        {days.map((_, dayIndex) =>
          hours.map((hour) => {
            const cellData = getCellData(dayIndex + 1, hour);
            const color = getColor(cellData.value);

            return (
              <g key={`cell-${dayIndex}-${hour}`}>
                <rect
                  x={60 + hour * cellWidth}
                  y={25 + dayIndex * cellHeight}
                  width={cellWidth - 1}
                  height={cellHeight - 1}
                  fill={color}
                  stroke={isDark ? "#374151" : "#e5e7eb"}
                  strokeWidth={0.5}
                  className="cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={() => onCellClick?.(cellData)}
                />

                {showLabels && cellData.value > 0 && (
                  <text
                    x={60 + hour * cellWidth + cellWidth / 2}
                    y={25 + dayIndex * cellHeight + cellHeight / 2}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    className={`text-xs font-medium ${
                      cellData.value > maxValue * 0.5
                        ? "fill-white"
                        : isDark
                        ? "fill-gray-300"
                        : "fill-gray-700"
                    }`}
                  >
                    {formatValue ? formatValue(cellData.value) : cellData.value}
                  </text>
                )}
              </g>
            );
          })
        )}
      </svg>

      {/* Legend */}
      <div className="flex items-center justify-center mt-4 space-x-4">
        <span
          className={`text-xs ${isDark ? "text-gray-300" : "text-gray-600"}`}
        >
          Low
        </span>
        <div className="flex space-x-1">
          {colorScale.map((color, index) => (
            <div
              key={index}
              className="w-4 h-4 rounded"
              style={{ backgroundColor: color }}
            />
          ))}
        </div>
        <span
          className={`text-xs ${isDark ? "text-gray-300" : "text-gray-600"}`}
        >
          High
        </span>
      </div>
    </div>
  );
};

export default HeatmapChart;
