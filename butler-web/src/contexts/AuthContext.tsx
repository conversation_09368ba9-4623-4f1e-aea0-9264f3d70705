/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { createContext, useContext, useState, useEffect } from "react";
import { login as adminLogin } from "@/server/admin";
import { superAdminLogin } from "@/server/super-admin";
import {
  userLogin,
  userLoginWithPhone,
  userRegisterWithPhone,
  verifyGoogleToken,
} from "@/server/user";
import { toast } from "sonner";

interface User {
  id?: string;
  _id?: string;
  name: string;
  email?: string;
  role: string;
  foodChain?: string;
  phone?: string;
  twoFactorEnabled?: boolean;
  isFirstTimeLogin?: boolean;
  createdBy?: string;
}

interface LoginResult {
  success: boolean;
  isFirstTimeLogin?: boolean;
  userType?: "admin" | "super-admin" | "user";
  user?: {
    id: string;
    name: string;
    email?: string; // Make email optional for phone-based login
    role: string;
  };
}

interface AuthContextType {
  user: User | null;
  login: (
    email: string,
    password: string,
    role: "admin" | "super-admin" | "user"
  ) => Promise<LoginResult>;
  loginWithPhone: (phone: string) => Promise<LoginResult>;
  loginWithGoogle: (token: string, userData: any) => Promise<LoginResult>;
  registerWithPhone: (
    phone: string,
    name: string,
    address?: string
  ) => Promise<LoginResult>;
  logout: () => Promise<void>;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  isUser: boolean;
  isLoading: boolean;
  checkAuth: () => Promise<void>;
  updateUserProfile: (userData: Partial<User>) => void;
  requestPasswordReset: (email: string, role: string) => Promise<boolean>;
  resetPassword: (token: string, newPassword: string) => Promise<boolean>;
  enable2FA: () => Promise<boolean>;
  disable2FA: () => Promise<boolean>;
  verify2FA: (code: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [isUser, setIsUser] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in on mount
    checkAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkAuth = async () => {
    setIsLoading(true);
    try {
      // Check for tokens in localStorage
      const adminToken = localStorage.getItem("auth-token");
      const superAdminToken = localStorage.getItem("super-auth-token");
      const userToken = localStorage.getItem("user-token");

      if (adminToken) {
        // Decode the JWT token to get user info
        const userData = parseJwt(adminToken);
        if (userData) {
          setUser({
            id: userData.userId,
            name: userData.name || "",
            email: userData.email || "",
            role: "admin",
            foodChain: userData.foodChain,
          });
          setIsAdmin(true);
          setIsSuperAdmin(false);
          setIsUser(false);
        }
      } else if (superAdminToken) {
        // Decode the JWT token to get user info
        const userData = parseJwt(superAdminToken);
        if (userData) {
          setUser({
            id: userData.userId,
            name: userData.name || "",
            email: userData.email || "",
            role: "super_admin",
          });
          setIsAdmin(false);
          setIsSuperAdmin(true);
          setIsUser(false);
        }
      } else if (userToken) {
        // Decode the JWT token to get user info
        const userData = parseJwt(userToken);
        if (userData) {
          setUser({
            id: userData.userId,
            name: userData.name || "",
            email: userData.email || "",
            role: "user",
          });
          setIsAdmin(false);
          setIsSuperAdmin(false);
          setIsUser(true);
        }
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      // Clear potentially corrupted tokens
      localStorage.removeItem("auth-token");
      localStorage.removeItem("super-auth-token");
      localStorage.removeItem("user-token");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to parse JWT token
  const parseJwt = (token: string) => {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map((c) => {
            return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join("")
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error parsing JWT:", error);
      return null;
    }
  };

  const login = async (
    email: string,
    password: string,
    role: "admin" | "super-admin" | "user"
  ) => {
    try {
      let response;

      if (role === "admin") {
        response = await adminLogin(email, password);
        if (response.success) {
          localStorage.setItem("auth-token", response.data.token);
          localStorage.setItem("chainId", response.data.user.foodChain);
          // Set cookie with max-age of 10 years (315360000 seconds) for long-lasting token
          document.cookie = `auth-token=${response.data.token}; path=/; max-age=315360000; secure; samesite=strict`;

          const userData = {
            id: response.data.user.id || response.data.user._id,
            name: response.data.user.name,
            email: response.data.user.email,
            role: response.data.user.role,
            foodChain: response.data.user.foodChain,
            twoFactorEnabled: response.data.user.twoFactorEnabled || false,
            isFirstTimeLogin: response.data.user.isFirstTimeLogin || false,
            createdBy: response.data.user.createdBy || "self",
          };

          setUser(userData);
          setIsAdmin(true);
          setIsSuperAdmin(false);
          setIsUser(false);

          // Check if this is a first-time login
          if (userData.isFirstTimeLogin) {
            // Return a special value to indicate first-time login
            return {
              success: true,
              isFirstTimeLogin: true,
              userType: "admin" as const,
            };
          }

          return { success: true, isFirstTimeLogin: false };
        }
      } else if (role === "super-admin") {
        response = await superAdminLogin(email, password);
        if (response.success) {
          localStorage.setItem("super-auth-token", response.data.token);
          // Set cookie with max-age of 10 years (315360000 seconds) for long-lasting token
          document.cookie = `super-auth-token=${response.data.token}; path=/; max-age=315360000; secure; samesite=strict`;

          const userData = {
            id: response.data.user.id || response.data.user._id,
            name: response.data.user.name,
            email: response.data.user.email,
            role: response.data.user.role,
            twoFactorEnabled: response.data.user.twoFactorEnabled || false,
            isFirstTimeLogin: response.data.user.isFirstTimeLogin || false,
            createdBy: response.data.user.createdBy || "self",
          };

          setUser(userData);
          setIsAdmin(false);
          setIsSuperAdmin(true);
          setIsUser(false);

          // Check if this is a first-time login
          if (userData.isFirstTimeLogin) {
            // Return a special value to indicate first-time login
            return {
              success: true,
              isFirstTimeLogin: true,
              userType: "super-admin" as const,
            };
          }

          return { success: true, isFirstTimeLogin: false };
        }
      } else if (role === "user") {
        response = await userLogin(email, password);
        if (response.success) {
          localStorage.setItem("user-token", response.data.token);
          localStorage.setItem(
            "userId",
            response.data.user.id || response.data.user._id
          );
          document.cookie = `user-token=${response.data.token}; path=/; max-age=86400; secure; samesite=strict`;

          const userData = {
            id: response.data.user.id || response.data.user._id,
            name: response.data.user.name,
            email: response.data.user.email,
            role: response.data.user.role,
            phone: response.data.user.phone,
            twoFactorEnabled: response.data.user.twoFactorEnabled || false,
            isFirstTimeLogin: response.data.user.isFirstTimeLogin || false,
            createdBy: response.data.user.createdBy || "self",
          };

          setUser(userData);
          setIsAdmin(false);
          setIsSuperAdmin(false);
          setIsUser(true);

          // Check if this is a first-time login
          if (userData.isFirstTimeLogin) {
            // Return a special value to indicate first-time login
            return {
              success: true,
              isFirstTimeLogin: true,
              userType: "user" as const,
              user: {
                id: userData.id,
                name: userData.name,
                email: userData.email,
                role: userData.role,
              },
            };
          }

          return {
            success: true,
            isFirstTimeLogin: false,
            user: {
              id: userData.id,
              name: userData.name,
              email: userData.email,
              role: userData.role,
            },
          };
        }
      }

      toast.error(response?.message || "Login failed");
      return { success: false };
    } catch (error) {
      console.error("Login failed:", error);
      toast.error("Login failed. Please try again.");
      return { success: false };
    }
  };

  const logout = async () => {
    try {
      // Clear localStorage
      localStorage.removeItem("auth-token");
      localStorage.removeItem("super-auth-token");
      localStorage.removeItem("user-token");
      localStorage.removeItem("chainId");
      localStorage.removeItem("userId");

      // Clear cookies
      document.cookie =
        "auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      document.cookie =
        "super-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      document.cookie =
        "user-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

      // Reset user state
      setUser(null);
      setIsAdmin(false);
      setIsSuperAdmin(false);
      setIsUser(false);

      // Redirect based on role
      if (isAdmin) {
        window.location.href = "/login";
      } else if (isSuperAdmin) {
        window.location.href = "/super-admin-login";
      } else {
        window.location.href = "/";
      }
    } catch (error) {
      console.error("Logout failed:", error);
      toast.error("Logout failed. Please try again.");
    }
  };

  const updateUserProfile = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  };

  const loginWithPhone = async (phone: string): Promise<LoginResult> => {
    try {
      const response = await userLoginWithPhone(phone);
      if (response.success) {
        localStorage.setItem("user-token", response.data.token);
        localStorage.setItem(
          "userId",
          response.data.user.id || response.data.user._id
        );
        document.cookie = `user-token=${response.data.token}; path=/; max-age=86400; secure; samesite=strict`;

        const userData = {
          id: response.data.user.id || response.data.user._id,
          name: response.data.user.name,
          email: response.data.user.email || "", // Add email field, default to empty string
          phone: response.data.user.phone,
          role: response.data.user.role,
          createdBy: response.data.user.createdBy || "self",
        };

        setUser(userData);
        setIsAdmin(false);
        setIsSuperAdmin(false);
        setIsUser(true);

        return {
          success: true,
          isFirstTimeLogin: false,
          userType: "user",
          user: userData,
        };
      } else {
        return {
          success: false,
          isFirstTimeLogin: false,
          userType: "user",
        };
      }
    } catch (error) {
      console.error("Phone login error:", error);
      return {
        success: false,
        isFirstTimeLogin: false,
        userType: "user",
      };
    }
  };

  const registerWithPhone = async (
    phone: string,
    name: string,
    address?: string
  ): Promise<LoginResult> => {
    try {
      const response = await userRegisterWithPhone(phone, name, address);
      if (response.success) {
        localStorage.setItem("user-token", response.data.token);
        localStorage.setItem(
          "userId",
          response.data.user.id || response.data.user._id
        );
        document.cookie = `user-token=${response.data.token}; path=/; max-age=86400; secure; samesite=strict`;

        const userData = {
          id: response.data.user.id || response.data.user._id,
          name: response.data.user.name,
          email: response.data.user.email || "", // Add email field, default to empty string
          phone: response.data.user.phone,
          role: response.data.user.role,
          createdBy: response.data.user.createdBy || "self",
        };

        setUser(userData);
        setIsAdmin(false);
        setIsSuperAdmin(false);
        setIsUser(true);

        return {
          success: true,
          isFirstTimeLogin: false,
          userType: "user",
          user: userData,
        };
      } else {
        return {
          success: false,
          isFirstTimeLogin: false,
          userType: "user",
        };
      }
    } catch (error) {
      console.error("Phone registration error:", error);
      return {
        success: false,
        isFirstTimeLogin: false,
        userType: "user",
      };
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const requestPasswordReset = async (email: string, role: string) => {
    try {
      // Implementation will be added when we create the server endpoint
      toast.success("Password reset link sent to your email");
      return true;
    } catch (error) {
      console.error("Password reset request failed:", error);
      toast.error("Failed to send password reset link");
      return false;
    }
  };

  const resetPassword = async (token: string, newPassword: string) => {
    try {
      // Implementation will be added when we create the server endpoint
      console.log(token, newPassword);
      toast.success("Password reset successful");
      return true;
    } catch (error) {
      console.error("Password reset failed:", error);
      toast.error("Failed to reset password");
      return false;
    }
  };

  const enable2FA = async () => {
    try {
      // Implementation will be added when we create the server endpoint
      toast.success("Two-factor authentication enabled");
      if (user) {
        setUser({ ...user, twoFactorEnabled: true });
      }
      return true;
    } catch (error) {
      console.error("Failed to enable 2FA:", error);
      toast.error("Failed to enable two-factor authentication");
      return false;
    }
  };

  const disable2FA = async () => {
    try {
      // Implementation will be added when we create the server endpoint
      toast.success("Two-factor authentication disabled");
      if (user) {
        setUser({ ...user, twoFactorEnabled: false });
      }
      return true;
    } catch (error) {
      console.error("Failed to disable 2FA:", error);
      toast.error("Failed to disable two-factor authentication");
      return false;
    }
  };

  const verify2FA = async (code: string) => {
    try {
      // Implementation will be added when we create the server endpoint
      console.log(code);
      toast.success("Two-factor authentication verified");
      return true;
    } catch (error) {
      console.error("2FA verification failed:", error);
      toast.error("Failed to verify two-factor authentication code");
      return false;
    }
  };

  const loginWithGoogle = async (
    token: string,
    userData: any
  ): Promise<LoginResult> => {
    try {
      const response = await verifyGoogleToken(token, userData);

      if (response.success) {
        localStorage.setItem("user-token", response.data.token);
        localStorage.setItem(
          "userId",
          response.data.user.id || response.data.user._id
        );
        document.cookie = `user-token=${response.data.token}; path=/; max-age=86400; secure; samesite=strict`;

        const userDataForState = {
          id: response.data.user.id || response.data.user._id,
          name: response.data.user.name,
          email: response.data.user.email || "",
          phone: response.data.user.phone || "",
          role: response.data.user.role,
          createdBy: response.data.user.createdBy || "google",
        };

        setUser(userDataForState);
        setIsLoading(false);

        return {
          success: true,
          isFirstTimeLogin: false,
          userType: "user",
          user: {
            id: userDataForState.id,
            name: userDataForState.name,
            email: userDataForState.email,
            role: userDataForState.role,
          },
        };
      } else {
        return {
          success: false,
          isFirstTimeLogin: false,
          userType: "user",
        };
      }
    } catch (error) {
      console.error("Google login error:", error);
      return {
        success: false,
        isFirstTimeLogin: false,
        userType: "user",
      };
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        loginWithPhone,
        loginWithGoogle,
        registerWithPhone,
        logout,
        isAdmin,
        isSuperAdmin,
        isUser,
        isLoading,
        checkAuth,
        updateUserProfile,
        requestPasswordReset,
        resetPassword,
        enable2FA,
        disable2FA,
        verify2FA,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
