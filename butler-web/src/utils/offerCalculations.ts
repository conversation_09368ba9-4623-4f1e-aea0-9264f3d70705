import { Dish, Offer } from "@/app/type";

export interface CartItem extends Dish {
  quantity: number;
}

export interface OfferCalculationResult {
  totalDiscount: number;
  discountedTotal: number;
  originalTotal: number;
  appliedOffers: AppliedOfferDetails[];
  modifiedItems: CartItem[];
}

export interface AppliedOfferDetails {
  offerId: string;
  offerName: string;
  offerType: string;
  discount: number;
  description: string;
}

// Calculate BOGO discount
export const calculateBOGODiscount = (
  items: CartItem[],
  offer: Offer
): { discount: number; description: string } => {
  const { buyQuantity, getQuantity } = offer.discountDetails;

  if (!buyQuantity || !getQuantity) {
    return { discount: 0, description: "Invalid BOGO configuration" };
  }

  let totalDiscount = 0;
  let totalSets = 0;

  // Group items by dish ID and calculate BOGO for each
  const itemGroups = items.reduce((acc, item) => {
    const key = item._id || "";
    if (!acc[key]) {
      acc[key] = { item, totalQuantity: 0 };
    }
    acc[key].totalQuantity += item.quantity;
    return acc;
  }, {} as Record<string, { item: CartItem; totalQuantity: number }>);

  Object.values(itemGroups).forEach(({ item, totalQuantity }) => {
    // For BOGO, customer needs at least (buyQuantity + getQuantity) items to get any discount
    // For Buy 1 Get 1, they need at least 2 items to get 1 free
    const minimumForDiscount = buyQuantity + getQuantity;

    if (totalQuantity < minimumForDiscount) {
      // Not enough items to qualify for any discount
      return;
    }

    // Calculate how many complete BOGO sets we can make
    const sets = Math.floor(totalQuantity / minimumForDiscount);
    totalSets += sets;

    // Calculate discount: for each set, customer gets 'getQuantity' items free
    const discountPerSet = item.price * getQuantity;
    totalDiscount += sets * discountPerSet;
  });

  let description: string;
  if (totalSets >= 1) {
    description = `Buy ${buyQuantity} Get ${getQuantity} Free (${Math.floor(
      totalSets
    )} sets applied)`;
  } else {
    const minimumForDiscount = buyQuantity + getQuantity;
    description = `Buy ${buyQuantity} Get ${getQuantity} Free (minimum ${minimumForDiscount} items required)`;
  }

  return { discount: totalDiscount, description };
};

// Calculate percentage/fixed discount
export const calculateSimpleDiscount = (
  subtotal: number,
  offer: Offer
): { discount: number; description: string } => {
  const { discountType, discountValue, maxDiscount, minimumOrderValue } =
    offer.discountDetails;

  // Check minimum order value
  if (minimumOrderValue && subtotal < minimumOrderValue) {
    return {
      discount: 0,
      description: `Minimum order value ₹${minimumOrderValue} required`,
    };
  }

  let discount = 0;
  let description = "";

  if (discountType === "percentage") {
    discount = (subtotal * (discountValue || 1)) / 100;

    // Apply max discount limit
    if (maxDiscount && discount > maxDiscount) {
      discount = maxDiscount;
      description = `${discountValue}% off (max ₹${maxDiscount})`;
    } else {
      description = `${discountValue}% off`;
    }
  } else if (discountType === "fixed") {
    discount = Math.min(discountValue || 0, subtotal);
    description = `₹${discountValue} off`;
  }

  return { discount, description };
};

// Calculate free item discount
export const calculateFreeItemDiscount = (
  items: CartItem[],
  offer: Offer
): { discount: number; description: string } => {
  const { freeItemId, freeItemQuantity = 1 } = offer.discountDetails;

  if (!freeItemId) {
    return { discount: 0, description: "No free item specified" };
  }

  // Find the free item in cart
  const freeItem = items.find(
    (item) => (item?._id as string) === String(freeItemId)
  );

  if (!freeItem) {
    return { discount: 0, description: "Free item not in cart" };
  }

  // Calculate discount for free quantity (up to what's in cart)
  const applicableQuantity = Math.min(freeItemQuantity, freeItem.quantity);
  const discount = freeItem.price * applicableQuantity;

  return {
    discount,
    description: `${applicableQuantity} x ${freeItem.name} free`,
  };
};

// Calculate combo discount
export const calculateComboDiscount = (
  items: CartItem[],
  offer: Offer
): { discount: number; description: string } => {
  const { comboItems, comboPrice } = offer.discountDetails;

  if (!comboItems || !comboPrice) {
    return { discount: 0, description: "Invalid combo configuration" };
  }

  // Check if all combo items are in cart with required quantities
  let canApplyCombo = true;
  let originalComboPrice = 0;
  const requiredItems: string[] = [];

  for (const comboItem of comboItems) {
    const cartItem = items.find((item) => item._id === comboItem.dishId);
    if (!cartItem || cartItem.quantity < comboItem.quantity) {
      canApplyCombo = false;
      break;
    }
    originalComboPrice += cartItem.price * comboItem.quantity;
    requiredItems.push(`${comboItem.quantity}x ${cartItem.name}`);
  }

  if (!canApplyCombo) {
    return {
      discount: 0,
      description: `Combo requires: ${requiredItems.join(", ")}`,
    };
  }

  const discount = Math.max(0, originalComboPrice - comboPrice);

  return {
    discount,
    description: `Combo Deal: ${requiredItems.join(" + ")} for ₹${comboPrice}`,
  };
};

// Main function to calculate offer discounts
export const calculateOfferDiscount = (
  items: CartItem[],
  offer: Offer,
  subtotal: number
): { discount: number; description: string } => {
  switch (offer.offerType) {
    case "BOGO":
      return calculateBOGODiscount(items, offer);

    case "discount":
    case "minimumAmount":
    case "quantityDiscount":
    case "dateRange":
    case "customerTier":
    case "firstTime":
    case "timeBasedSpecial":
    case "dayOfWeek":
    case "multiDishType":
      return calculateSimpleDiscount(subtotal, offer);

    case "freeItem":
      return calculateFreeItemDiscount(items, offer);

    case "combo":
      return calculateComboDiscount(items, offer);

    default:
      return { discount: 0, description: "Unknown offer type" };
  }
};

// Calculate total with multiple offers
export const calculateTotalWithOffers = (
  items: CartItem[],
  appliedOffers: Offer[]
): OfferCalculationResult => {
  const originalTotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  let totalDiscount = 0;
  const appliedOfferDetails: AppliedOfferDetails[] = [];

  // Apply each offer and calculate discount
  appliedOffers.forEach((offer) => {
    const { discount, description } = calculateOfferDiscount(
      items,
      offer,
      originalTotal
    );

    if (discount > 0) {
      totalDiscount += discount;
      appliedOfferDetails.push({
        offerId: offer._id || "",
        offerName: offer.name,
        offerType: offer.offerType,
        discount,
        description,
      });
    }
  });

  const discountedTotal = Math.max(0, originalTotal - totalDiscount);

  return {
    totalDiscount,
    discountedTotal,
    originalTotal,
    appliedOffers: appliedOfferDetails,
    modifiedItems: items, // For now, return original items
  };
};

// Helper function to format discount display
export const formatDiscountDisplay = (discount: number): string => {
  return `₹${discount.toFixed(2)}`;
};

// Helper function to check if offer is applicable to current cart
export const isOfferApplicableToCart = (
  items: CartItem[],
  offer: Offer,
  subtotal: number
): boolean => {
  const { discount } = calculateOfferDiscount(items, offer, subtotal);
  return discount > 0;
};
