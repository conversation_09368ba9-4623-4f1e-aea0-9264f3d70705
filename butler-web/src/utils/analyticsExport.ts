/* eslint-disable @typescript-eslint/no-explicit-any */
import { Indian } from "@/lib/currency";

export interface ExportData {
  summary: any;
  dailyRevenue: any[];
  topItems: any[];
  outletPerformance: any[];
  customerSegmentation: any[];
  categoryPerformance: any[];
  [key: string]: any;
}

export const exportToCSV = (data: any[], filename: string) => {
  if (!data || data.length === 0) return;

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(","),
    ...data.map((row) =>
      headers
        .map((header) => {
          const value = row[header];
          // Handle nested objects and arrays
          if (typeof value === "object" && value !== null) {
            return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
          }
          // Escape commas and quotes in strings
          if (typeof value === "string") {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        })
        .join(",")
    ),
  ].join("\n");

  downloadFile(csvContent, `${filename}.csv`, "text/csv");
};

export const exportAnalyticsToExcel = (analyticsData: ExportData) => {
  // Create a comprehensive Excel-like CSV with multiple sheets data
  const sheets = [
    {
      name: "Summary",
      data: [
        {
          Metric: "Total Revenue",
          Value: Indian(analyticsData.summary?.totalRevenue || 0),
          Growth: `${(analyticsData.summary?.growth?.revenue || 0).toFixed(
            1
          )}%`,
        },
        {
          Metric: "Total Orders",
          Value: analyticsData.summary?.totalOrders || 0,
          Growth: `${(analyticsData.summary?.growth?.orders || 0).toFixed(1)}%`,
        },
        {
          Metric: "Average Order Value",
          Value: Indian(analyticsData.summary?.averageOrderValue || 0),
          Growth: `${(
            analyticsData.summary?.growth?.averageOrderValue || 0
          ).toFixed(1)}%`,
        },
        {
          Metric: "Unique Customers",
          Value: analyticsData.summary?.uniqueCustomers || 0,
          Growth: `${(
            analyticsData.summary?.growth?.uniqueCustomers || 0
          ).toFixed(1)}%`,
        },
      ],
    },
    {
      name: "Daily Revenue",
      data:
        analyticsData.dailyRevenue?.map((item) => ({
          Date: item._id,
          Revenue: item.revenue,
          "Order Count": item.orderCount || 0,
          "Average Order Value": item.averageOrderValue || 0,
        })) || [],
    },
    {
      name: "Top Items",
      data:
        analyticsData.topItems?.map((item) => ({
          "Item Name": item.dishDetails?.[0]?.name || "Unknown",
          "Quantity Sold": item.totalQuantity,
          "Total Revenue": item.totalRevenue,
          "Average Price": (item.totalRevenue / item.totalQuantity).toFixed(2),
        })) || [],
    },
    {
      name: "Outlet Performance",
      data:
        analyticsData.outletPerformance?.map((outlet) => ({
          "Outlet Name":
            outlet.displayName || outlet.outletDetails?.[0]?.name || "Unknown",
          "Total Revenue": outlet.totalRevenue,
          "Total Orders": outlet.totalOrders,
          "Average Order Value": outlet.averageOrderValue,
        })) || [],
    },
    {
      name: "Customer Segments",
      data:
        analyticsData.customerSegmentation?.map((segment) => ({
          Segment: segment._id,
          "Customer Count": segment.customerCount,
          "Total Revenue": segment.totalRevenue,
          "Average Spending": segment.avgSpending,
        })) || [],
    },
    {
      name: "Category Performance",
      data:
        analyticsData.categoryPerformance?.map((category) => ({
          "Category Name": category.categoryName,
          "Total Quantity": category.totalQuantity,
          "Total Revenue": category.totalRevenue,
          "Average Price": category.avgPrice,
          "Order Count": category.orderCount,
        })) || [],
    },
  ];

  // Export each sheet as a separate CSV (simulating Excel workbook)
  sheets.forEach((sheet) => {
    if (sheet.data.length > 0) {
      exportToCSV(
        sheet.data,
        `analytics_${sheet.name.toLowerCase().replace(" ", "_")}`
      );
    }
  });
};

export const exportAnalyticsToPDF = async (analyticsData: ExportData) => {
  // Create a comprehensive PDF report
  const reportContent = generatePDFContent(analyticsData);

  // For now, we'll create an HTML version that can be printed as PDF
  const printWindow = window.open("", "_blank");
  if (printWindow) {
    printWindow.document.write(reportContent);
    printWindow.document.close();
    printWindow.focus();

    // Trigger print dialog
    setTimeout(() => {
      printWindow.print();
    }, 250);
  }
};

const generatePDFContent = (data: ExportData): string => {
  const currentDate = new Date().toLocaleDateString();

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Analytics Report - ${currentDate}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #2563eb; border-bottom: 1px solid #e5e7eb; padding-bottom: 10px; }
        .kpi-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .kpi-card { border: 1px solid #e5e7eb; padding: 15px; border-radius: 8px; text-align: center; }
        .kpi-value { font-size: 24px; font-weight: bold; color: #1f2937; }
        .kpi-label { font-size: 14px; color: #6b7280; margin-top: 5px; }
        .kpi-growth { font-size: 12px; margin-top: 5px; }
        .growth-positive { color: #10b981; }
        .growth-negative { color: #ef4444; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { border: 1px solid #e5e7eb; padding: 8px; text-align: left; }
        th { background-color: #f9fafb; font-weight: bold; }
        .text-right { text-align: right; }
        @media print { body { margin: 0; } .no-print { display: none; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Analytics Report</h1>
        <p>Generated on ${currentDate}</p>
      </div>

      <div class="section">
        <h2>Key Performance Indicators</h2>
        <div class="kpi-grid">
          <div class="kpi-card">
            <div class="kpi-value">${Indian(
              data.summary?.totalRevenue || 0
            )}</div>
            <div class="kpi-label">Total Revenue</div>
            <div class="kpi-growth ${
              (data.summary?.growth?.revenue || 0) >= 0
                ? "growth-positive"
                : "growth-negative"
            }">
              ${(data.summary?.growth?.revenue || 0).toFixed(
                1
              )}% vs previous period
            </div>
          </div>
          <div class="kpi-card">
            <div class="kpi-value">${data.summary?.totalOrders || 0}</div>
            <div class="kpi-label">Total Orders</div>
            <div class="kpi-growth ${
              (data.summary?.growth?.orders || 0) >= 0
                ? "growth-positive"
                : "growth-negative"
            }">
              ${(data.summary?.growth?.orders || 0).toFixed(
                1
              )}% vs previous period
            </div>
          </div>
          <div class="kpi-card">
            <div class="kpi-value">${Indian(
              data.summary?.averageOrderValue || 0
            )}</div>
            <div class="kpi-label">Average Order Value</div>
            <div class="kpi-growth ${
              (data.summary?.growth?.averageOrderValue || 0) >= 0
                ? "growth-positive"
                : "growth-negative"
            }">
              ${(data.summary?.growth?.averageOrderValue || 0).toFixed(
                1
              )}% vs previous period
            </div>
          </div>
          <div class="kpi-card">
            <div class="kpi-value">${data.summary?.uniqueCustomers || 0}</div>
            <div class="kpi-label">Unique Customers</div>
          </div>
        </div>
      </div>

      <div class="section">
        <h2>Top Selling Items</h2>
        <table>
          <thead>
            <tr>
              <th>Item Name</th>
              <th class="text-right">Quantity Sold</th>
              <th class="text-right">Total Revenue</th>
            </tr>
          </thead>
          <tbody>
            ${
              data.topItems
                ?.slice(0, 10)
                .map(
                  (item) => `
              <tr>
                <td>${item.dishDetails?.[0]?.name || "Unknown"}</td>
                <td class="text-right">${item.totalQuantity}</td>
                <td class="text-right">${Indian(item.totalRevenue)}</td>
              </tr>
            `
                )
                .join("") || '<tr><td colspan="3">No data available</td></tr>'
            }
          </tbody>
        </table>
      </div>

      <div class="section">
        <h2>Customer Segmentation</h2>
        <table>
          <thead>
            <tr>
              <th>Segment</th>
              <th class="text-right">Customer Count</th>
              <th class="text-right">Total Revenue</th>
              <th class="text-right">Avg Spending</th>
            </tr>
          </thead>
          <tbody>
            ${
              data.customerSegmentation
                ?.map(
                  (segment) => `
              <tr>
                <td>${segment._id}</td>
                <td class="text-right">${segment.customerCount}</td>
                <td class="text-right">${Indian(segment.totalRevenue)}</td>
                <td class="text-right">${Indian(segment.avgSpending)}</td>
              </tr>
            `
                )
                .join("") || '<tr><td colspan="4">No data available</td></tr>'
            }
          </tbody>
        </table>
      </div>
    </body>
    </html>
  `;
};

const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};
