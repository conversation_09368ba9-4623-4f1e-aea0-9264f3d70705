import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { Order } from "@/types/order";
import { format } from "date-fns";

/**
 * Convert oklch colors to rgb format for html2canvas compatibility
 * @param cssText The CSS text to sanitize
 * @returns Sanitized CSS text with oklch colors converted to rgb
 */

/**
 * Sanitize an element's styles for PDF generation
 * @param element The HTML element to sanitize
 */
const sanitizeElementStyles = (element: HTMLElement): void => {
  // First, remove all existing stylesheets to prevent oklch conflicts
  const stylesheets = Array.from(document.styleSheets);
  const disabledSheets: CSSStyleSheet[] = [];

  try {
    // Temporarily disable all stylesheets
    stylesheets.forEach((sheet) => {
      if (!sheet.disabled) {
        sheet.disabled = true;
        disabledSheets.push(sheet);
      }
    });

    // Apply basic styles directly to ensure proper rendering
    const basicStyles = `
      * {
        color: #000000 !important;
        background-color: transparent !important;
        border-color: #e5e7eb !important;
      }
      body {
        background-color: #ffffff !important;
        color: #000000 !important;
        font-family: Arial, sans-serif !important;
      }
      table {
        border-collapse: collapse !important;
        width: 100% !important;
      }
      th, td {
        border: 1px solid #e5e7eb !important;
        padding: 8px !important;
        text-align: left !important;
      }
      h1, h2, h3 {
        color: #000000 !important;
        margin: 10px 0 !important;
      }
      .text-center {
        text-align: center !important;
      }
      .text-right {
        text-align: right !important;
      }
      .font-bold {
        font-weight: bold !important;
      }
    `;

    // Create and inject the basic stylesheet
    const style = document.createElement("style");
    style.textContent = basicStyles;
    document.head.appendChild(style);

    // Get all elements including the root element
    const allElements = [
      element,
      ...element.querySelectorAll("*"),
    ] as HTMLElement[];

    allElements.forEach((el) => {
      // Force basic styles on each element
      el.style.setProperty("color", "#000000", "important");
      el.style.setProperty("background-color", "transparent", "important");
      el.style.setProperty("border-color", "#e5e7eb", "important");

      // Handle specific element types
      if (el.tagName === "BODY" || el === element) {
        el.style.setProperty("background-color", "#ffffff", "important");
      }

      if (el.tagName === "H1" || el.tagName === "H2" || el.tagName === "H3") {
        el.style.setProperty("color", "#000000", "important");
        el.style.setProperty("font-weight", "bold", "important");
      }

      if (el.tagName === "TABLE") {
        el.style.setProperty("border-collapse", "collapse", "important");
        el.style.setProperty("width", "100%", "important");
      }

      if (el.tagName === "TH" || el.tagName === "TD") {
        el.style.setProperty("border", "1px solid #e5e7eb", "important");
        el.style.setProperty("padding", "8px", "important");
      }

      // Remove any class-based styling that might contain oklch
      if (el.className) {
        const classes = el.className.split(" ");
        classes.forEach((className) => {
          if (
            className.includes("bg-") ||
            className.includes("text-") ||
            className.includes("border-")
          ) {
            el.classList.remove(className);
          }
        });
      }
    });

    // Clean up the injected style
    document.head.removeChild(style);
  } finally {
    // Re-enable the stylesheets
    disabledSheets.forEach((sheet) => {
      sheet.disabled = false;
    });
  }
};

/**
 * Generate a PDF bill for an order
 * @param order The order object
 * @param elementId The ID of the HTML element to convert to PDF
 */
export const generateOrderPDF = async (
  order: Order,
  elementId: string
): Promise<void> => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`Element with ID ${elementId} not found`);
    }

    // Create a clone of the element to avoid modifying the original
    const clone = element.cloneNode(true) as HTMLElement;
    clone.style.padding = "20px";
    clone.style.backgroundColor = "white";

    // Temporarily append to body but make it invisible
    clone.style.position = "absolute";
    clone.style.left = "-9999px";
    document.body.appendChild(clone);

    // Sanitize the element styles to remove oklch colors
    sanitizeElementStyles(clone);

    // Use html2canvas to convert the element to a canvas
    const canvas = await html2canvas(clone, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      logging: false,
      backgroundColor: "#ffffff",
      // Additional options to handle CSS issues
      allowTaint: true,
      foreignObjectRendering: false,
    });

    // Remove the clone from the DOM
    document.body.removeChild(clone);

    // Create a new PDF document
    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Calculate dimensions to fit the canvas in the PDF
    const imgWidth = 210; // A4 width in mm (210mm)
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    // Add the canvas as an image to the PDF
    const imgData = canvas.toDataURL("image/png");
    pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);

    // Save the PDF
    const fileName = `Order_${order.orderNumber}_${format(
      new Date(order.createdAt),
      "yyyyMMdd"
    )}.pdf`;
    pdf.save(fileName);
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }
};

/**
 * Create a printable HTML representation of an order
 * @param order The order object
 * @returns HTML string for the order bill
 */
export const createOrderBillHTML = (order: Order): string => {
  const orderDate = format(new Date(order.createdAt), "PPP p");

  // Generate items HTML
  const itemsHTML = order.items
    .map(
      (item) => `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${
        item.dishId?.name || item.dishName || "Deleted Dish"
      }</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${
        item.quantity
      }</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">₹${item?.dishId?.price?.toFixed(
        2
      )}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">₹${(
        (item?.dishId?.price || 1) * item?.quantity
      ).toFixed(2)}</td>
    </tr>
  `
    )
    .join("");

  // Generate coupon HTML if applicable
  const couponHTML = order.couponCode
    ? `
    <tr>
      <td colspan="3" style="padding: 8px; text-align: right; font-weight: bold;">Coupon (${
        order.couponCode
      }):</td>
      <td style="padding: 8px; text-align: right; color: #e53e3e;">-₹${
        order.couponDiscount?.toFixed(2) || "0.00"
      }</td>
    </tr>
  `
    : "";

  return `
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333; margin-bottom: 5px;">${order.outletId.name}</h1>
        <p style="color: #666; margin-top: 0;">${order.outletId.address}</p>
      </div>

      <div style="margin-bottom: 30px;">
        <h2 style="color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">Order Invoice</h2>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 5px;"><strong>Order Number:</strong></td>
            <td style="padding: 5px;">${order.orderNumber}</td>
            <td style="padding: 5px; text-align: right;"><strong>Date:</strong></td>
            <td style="padding: 5px; text-align: right;">${orderDate}</td>
          </tr>
          <tr>
            <td style="padding: 5px;"><strong>Customer:</strong></td>
            <td style="padding: 5px;">${order.userId.name}</td>
            <td style="padding: 5px; text-align: right;"><strong>Payment Method:</strong></td>
            <td style="padding: 5px; text-align: right;">${
              order.paymentMethod === "cash" ? "Cash" : "Online"
            }</td>
          </tr>
          <tr>
            <td style="padding: 5px;"><strong>Status:</strong></td>
            <td style="padding: 5px;">${
              order.status.charAt(0).toUpperCase() + order.status.slice(1)
            }</td>
            <td style="padding: 5px;"></td>
            <td style="padding: 5px;"></td>
          </tr>
          ${
            order.tableNumber
              ? `
          <tr>
            <td style="padding: 5px;"><strong>Table Number:</strong></td>
            <td style="padding: 5px;">${order.tableNumber}</td>
            <td style="padding: 5px;"></td>
            <td style="padding: 5px;"></td>
          </tr>
          `
              : ""
          }
        </table>
      </div>

      <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">Order Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background-color: #f8f9fa;">
              <th style="padding: 8px; text-align: left; border-bottom: 2px solid #eee;">Item</th>
              <th style="padding: 8px; text-align: center; border-bottom: 2px solid #eee;">Quantity</th>
              <th style="padding: 8px; text-align: right; border-bottom: 2px solid #eee;">Price</th>
              <th style="padding: 8px; text-align: right; border-bottom: 2px solid #eee;">Total</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHTML}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="3" style="padding: 8px; text-align: right; font-weight: bold;">Subtotal:</td>
              <td style="padding: 8px; text-align: right;">₹${order.totalAmount.toFixed(
                2
              )}</td>
            </tr>
            ${couponHTML}
            <tr>
              <td colspan="3" style="padding: 8px; text-align: right; font-weight: bold; font-size: 1.1em;">Total:</td>
              <td style="padding: 8px; text-align: right; font-weight: bold; font-size: 1.1em;">₹${
                order.finalAmount
                  ? order.finalAmount.toFixed(2)
                  : order.totalAmount.toFixed(2)
              }</td>
            </tr>
          </tfoot>
        </table>
      </div>

      ${
        order.specialInstructions
          ? `
      <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">Special Instructions</h3>
        <p style="padding: 10px; background-color: #f8f9fa; border-radius: 5px;">${order.specialInstructions}</p>
      </div>
      `
          : ""
      }

      <div style="margin-top: 40px; text-align: center; color: #666; font-size: 0.9em;">
        <p>Thank you for your order!</p>
        <p>&copy; ${new Date().getFullYear()}</p>
      </div>
    </div>
  `;
};

/**
 * Alternative PDF generation using a simpler approach
 * @param order The order object
 */
export const downloadOrderBillSimple = (order: Order): void => {
  try {
    // Create PDF directly without html2canvas to avoid oklch issues
    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Set font
    pdf.setFont("helvetica");

    let yPosition = 20;
    const pageWidth = 210;
    const margin = 20;

    // Header
    pdf.setFontSize(20);
    pdf.setFont("helvetica", "bold");
    pdf.text(order.outletId.name, pageWidth / 2, yPosition, {
      align: "center",
    });
    yPosition += 10;

    pdf.setFontSize(12);
    pdf.setFont("helvetica", "normal");
    pdf.text(order.outletId.address, pageWidth / 2, yPosition, {
      align: "center",
    });
    yPosition += 20;

    // Order details
    pdf.setFontSize(16);
    pdf.setFont("helvetica", "bold");
    pdf.text("Order Invoice", margin, yPosition);
    yPosition += 15;

    pdf.setFontSize(10);
    pdf.setFont("helvetica", "normal");

    // Order info
    const orderDate = format(new Date(order.createdAt), "PPP p");
    pdf.text(`Order Number: ${order.orderNumber}`, margin, yPosition);
    pdf.text(`Date: ${orderDate}`, margin + 100, yPosition);
    yPosition += 8;

    pdf.text(`Customer: ${order.userId.name}`, margin, yPosition);
    pdf.text(
      `Payment: ${order.paymentMethod === "cash" ? "Cash" : "Online"}`,
      margin + 100,
      yPosition
    );
    yPosition += 8;

    pdf.text(
      `Status: ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}`,
      margin,
      yPosition
    );
    if (order.tableNumber) {
      pdf.text(`Table: ${order.tableNumber}`, margin + 100, yPosition);
    }
    yPosition += 15;

    // Items table header
    pdf.setFont("helvetica", "bold");
    pdf.text("Item", margin, yPosition);
    pdf.text("Qty", margin + 100, yPosition);
    pdf.text("Price", margin + 130, yPosition);
    pdf.text("Total", margin + 160, yPosition);
    yPosition += 5;

    // Draw line under header
    pdf.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 8;

    // Items
    pdf.setFont("helvetica", "normal");
    order.items.forEach((item) => {
      const itemTotal = (item.dishId?.price || 1) * item.quantity;
      pdf.text(
        item.dishId?.name || item.dishName || "Deleted Dish",
        margin,
        yPosition
      );
      pdf.text(item.quantity.toString(), margin + 100, yPosition);
      pdf.text(
        `₹${item.dishId?.price?.toFixed(2) || "1.00"}`,
        margin + 130,
        yPosition
      );
      pdf.text(`₹${itemTotal.toFixed(2)}`, margin + 160, yPosition);
      yPosition += 8;
    });

    yPosition += 5;
    pdf.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 8;

    // Totals
    pdf.setFont("helvetica", "bold");
    pdf.text(
      `Subtotal: ₹${order.totalAmount.toFixed(2)}`,
      margin + 120,
      yPosition
    );
    yPosition += 8;

    if (order.couponCode && order.couponDiscount) {
      pdf.text(
        `Coupon (${order.couponCode}): -₹${order.couponDiscount.toFixed(2)}`,
        margin + 120,
        yPosition
      );
      yPosition += 8;
    }

    const finalAmount = order.finalAmount || order.totalAmount;
    pdf.setFontSize(12);
    pdf.text(`Total: ₹${finalAmount?.toFixed(2)}`, margin + 120, yPosition);
    yPosition += 15;

    // Special instructions
    if (order.specialInstructions) {
      pdf.setFontSize(10);
      pdf.setFont("helvetica", "bold");
      pdf.text("Special Instructions:", margin, yPosition);
      yPosition += 8;
      pdf.setFont("helvetica", "normal");
      const instructions = pdf.splitTextToSize(order.specialInstructions, 170);
      pdf.text(instructions, margin, yPosition);
      yPosition += instructions.length * 5;
    }

    // Footer
    yPosition += 20;
    pdf.setFontSize(10);
    pdf.setFont("helvetica", "normal");
    pdf.text("Thank you for your order!", pageWidth / 2, yPosition, {
      align: "center",
    });
    yPosition += 8;
    pdf.text(`© ${new Date().getFullYear()}`, pageWidth / 2, yPosition, {
      align: "center",
    });

    // Save the PDF
    const fileName = `Order_${order.orderNumber}_${format(
      new Date(order.createdAt),
      "yyyyMMdd"
    )}.pdf`;
    pdf.save(fileName);
  } catch (error) {
    console.error("Error generating simple PDF:", error);
    alert("Failed to generate bill. Please try again.");
  }
};

/**
 * Generate and download a PDF bill for an order using HTML template
 * @param order The order object
 */
export const downloadOrderBillHTML = (order: Order): void => {
  try {
    // Create a temporary div to hold the bill HTML
    const tempDiv = document.createElement("div");
    tempDiv.id = "temp-bill-container";
    tempDiv.innerHTML = createOrderBillHTML(order);
    tempDiv.style.position = "absolute";
    tempDiv.style.left = "-9999px";
    document.body.appendChild(tempDiv);

    // Generate and download the PDF
    generateOrderPDF(order, "temp-bill-container")
      .catch((error) => {
        console.error("Error generating PDF:", error);
        // Fallback to simple PDF generation
        downloadOrderBillSimple(order);
      })
      .finally(() => {
        // Clean up the temporary div
        if (document.body.contains(tempDiv)) {
          document.body.removeChild(tempDiv);
        }
      });
  } catch (error) {
    console.error("Error in downloadOrderBillHTML:", error);
    // Fallback to simple PDF generation
    downloadOrderBillSimple(order);
  }
};

/**
 * Send order invoice via email
 * @param order The order object
 */
export const emailOrderInvoice = async (order: Order): Promise<boolean> => {
  try {
    if (!order.userId.email) {
      throw new Error("Customer email not available");
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/${order._id}/send-invoice`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Failed to send email");
    }

    return data.success;
  } catch (error) {
    console.error("Error sending email invoice:", error);
    throw error;
  }
};

/**
 * Generate and download a PDF bill for an order
 * @param order The order object
 */
export const downloadOrderBill = (order: Order): void => {
  // Use the HTML template method for consistent formatting
  downloadOrderBillHTML(order);
};
