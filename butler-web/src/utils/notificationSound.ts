// Utility for playing notification sounds

// Cache audio objects to prevent recreating them on each play
const audioCache: Record<string, HTMLAudioElement> = {};

/**
 * Play a notification sound
 * @param soundName The name of the sound to play (without extension)
 */
export const playNotificationSound = (soundName: string = 'notification') => {
  try {
    // Check if sound is already cached
    if (!audioCache[soundName]) {
      // Create a new audio object
      audioCache[soundName] = new Audio(`/sounds/${soundName}.mp3`);
    }
    
    // Reset the audio to the beginning (in case it was already playing)
    audioCache[soundName].currentTime = 0;
    
    // Play the sound
    audioCache[soundName].play().catch(error => {
      console.error(`Error playing notification sound: ${error.message}`);
    });
  } catch (error) {
    console.error('Error with notification sound:', error);
  }
};
