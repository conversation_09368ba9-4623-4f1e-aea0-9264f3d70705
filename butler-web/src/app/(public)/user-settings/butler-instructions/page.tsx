"use client";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useEffect, useState } from "react";
import React from "react";
import { getProfile, updateUserProfile } from "@/server/user";

const ButlerInstructions = () => {
  const [instructions, setInstructions] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    getUserProfileFunc();
  }, []);

  const getUserProfileFunc = async () => {
    const profile = await getProfile();
    setInstructions(profile.data.aiMessage || "");
  };

  const handleSave = async () => {
    if (!instructions) return;
    setIsSaving(true);
    await updateUserProfile({ aiMessage: instructions });
    setIsSaving(false);
  };

  const handleClear = () => {
    setInstructions("");
  };

  return (
    <Card className="w-full mx-auto shadow-md">
      <CardHeader className="bg-gray-50 pb-2">
        <CardTitle className="font-bold text-xl flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          Butler Instructions
        </CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="pt-4">
        <div className="space-y-4">
          <div className="flex flex-col">
            <label
              htmlFor="instructions"
              className="text-sm font-medium text-gray-700 mb-1"
            >
              These instructions will be used throughout the different Outlets
              by default :
            </label>
            <textarea
              id="instructions"
              placeholder="Enter detailed instructions for your butler..."
              className="border rounded-md w-full min-h-32 p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200"
              value={instructions}
              onChange={(e) => setInstructions(e.target.value)}
            />
          </div>

          <div className="flex justify-between items-center pt-2">
            <div className="text-sm text-gray-500">
              {instructions.length} characters
            </div>
            <div className="space-x-2">
              <Button
                variant="outline"
                onClick={handleClear}
                className="text-gray-600 hover:text-gray-800"
              >
                Clear
              </Button>
              <Button
                onClick={handleSave}
                disabled={isSaving || instructions.trim().length === 0}
                className="bg-blue-600 text-white hover:bg-blue-700"
              >
                {isSaving ? "Saving..." : "Save Instructions"}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ButlerInstructions;
