"use client";
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { getProfile, updateUserProfile } from "@/server/user";
import NewPasswordDialog from "@/components/custom/auth/NewPasswordDialog";
import { Pencil, Save, X } from "lucide-react";

interface UserProfile {
  _id: string;
  email: string;
  name: string;
  phone: string;
  address: string;
  foodChain: {
    _id: string;
    name: string;
  } | null;
  role: "user" | "admin" | "super_admin";
  createdAt: string;
  updatedAt: string;
  aiMessage: string;
}

const UserProfilePage = () => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [newPassword, setNewPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [editedUser, setEditedUser] = useState<Partial<UserProfile>>({});

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedUser(user || {});
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setEditedUser((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    const res = await updateUserProfile(editedUser);
    if (res.success) {
      setUser(editedUser as UserProfile);
      setIsEditing(false);
      setIsLoading(false);
    }
  };

  const getUserProfileFunc = async () => {
    setIsLoading(true);
    const res = await getProfile();
    setUser(res.data);
    setEditedUser(res.data);
    setIsLoading(false);
  };

  useEffect(() => {
    getUserProfileFunc();
  }, []);

  if (isLoading && !user) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto md:py-8 px-4">
      <h1 className="md:text-2xl text-md font-bold mb-2 md:mb-6">
        User Profile
      </h1>

      <Tabs defaultValue="details">
        <TabsList className="mb-4">
          <TabsTrigger value="details">Profile Details</TabsTrigger>
          {/* <TabsTrigger value="preferences">Preferences</TabsTrigger> */}
          <TabsTrigger value="account">Account Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 md:pb-2">
              <CardTitle className="md:text-xl text-md font-bold">
                Personal Information
              </CardTitle>
              {!isEditing ? (
                <Button onClick={handleEdit}>
                  <div className="hidden md:block">Edit Profile</div>
                  <Pencil />
                </Button>
              ) : (
                <div className="space-x-2">
                  <Button variant="outline" onClick={handleCancel}>
                    <div className="hidden md:block">Cancel</div>
                    <X />
                  </Button>
                  <Button onClick={handleSave}>
                    <div className="hidden md:block"> Save Changes</div>
                    <Save />
                  </Button>
                </div>
              )}
            </CardHeader>
            <Separator />
            <CardContent className="md:pt-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex flex-col items-center md:w-1/4">
                  <Avatar className="md:h-24 md:w-24 h-12 w-12 mb-1 md:mb-4">
                    <div className="bg-gray-200 md:h-24 md:w-24 h-12 w-12 rounded-full flex items-center justify-center">
                      <span className="md:text-3xl text-md">
                        {user?.name.charAt(0)}
                      </span>
                    </div>
                  </Avatar>
                  <div className="text-center">
                    <h2 className="font-medium text-lg">{user?.name}</h2>
                    <p className="text-sm text-gray-500 capitalize md:block hidden">
                      {user?.role.replace("_", " ")}
                    </p>
                  </div>
                </div>

                <div className="md:w-3/4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      {isEditing ? (
                        <Input
                          id="name"
                          name="name"
                          value={editedUser?.name || ""}
                          onChange={handleChange}
                        />
                      ) : (
                        <p className="text-gray-700 pt-1">{user?.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      {isEditing ? (
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={editedUser?.email || ""}
                          onChange={handleChange}
                        />
                      ) : (
                        <p className="text-gray-700 pt-1">{user?.email}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      {isEditing ? (
                        <Input
                          id="phone"
                          name="phone"
                          value={editedUser?.phone || ""}
                          onChange={handleChange}
                        />
                      ) : (
                        <p className="text-gray-700 pt-1">
                          {user?.phone || "Not provided"}
                        </p>
                      )}
                    </div>

                    {/* <div className="space-y-2">
                      <Label htmlFor="foodChain">Associated Restaurant</Label>
                      <p className="text-gray-700 pt-1">
                        {user?.foodChain?.name || "None"}
                      </p>
                    </div> */}

                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="address">Address</Label>
                      {isEditing ? (
                        <Textarea
                          id="address"
                          name="address"
                          value={editedUser?.address || ""}
                          onChange={handleChange}
                          rows={3}
                        />
                      ) : (
                        <p className="text-gray-700 pt-1">
                          {user?.address || "Not provided"}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Preferences</CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="aiMessage">Special Instructions</Label>
                  {isEditing ? (
                    <Textarea
                      id="aiMessage"
                      name="aiMessage"
                      value={editedUser?.aiMessage || ""}
                      onChange={handleChange}
                      rows={4}
                      placeholder="Enter any special instructions or preferences..."
                    />
                  ) : (
                    <p className="text-gray-700 p-3 bg-gray-50 rounded-md">
                      {user?.aiMessage || "No special instructions provided."}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent> */}

        <TabsContent value="account">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className=" md:pt-6">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Account Created</Label>
                    <p className="text-gray-700">
                      {new Date(user?.createdAt || "").toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        }
                      )}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>Last Updated</Label>
                    <p className="text-gray-700">
                      {new Date(user?.updatedAt || "").toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        }
                      )}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>User Role</Label>
                    <p className="text-gray-700 capitalize">
                      {user?.role.replace("_", " ")}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>User ID</Label>
                    <p className="text-gray-700 font-mono text-sm">
                      {user?._id}
                    </p>
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    variant="outline"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => setNewPassword(true)}
                  >
                    Change Password
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      <NewPasswordDialog open={newPassword} setOpen={setNewPassword} />
    </div>
  );
};

export default UserProfilePage;
