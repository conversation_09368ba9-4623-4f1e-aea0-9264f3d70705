"use client";
import { getRoutes } from "@/routes";
import React from "react";
import { UserButton } from "@/components/UserButton";
import { Toaster } from "sonner";
import ResponsiveSidebar from "@/components/layouts/ResponsiveSidebar";

const Layout = ({ children }: { children: React.ReactNode }) => {
  const routes = getRoutes("user");

  // Add icons to user routes for consistency
  const routesWithIcons = routes.map((route) => ({
    ...route,
    icon: route.icon || "material-symbols:settings-outline", // Default icon if none provided
  }));

  return (
    <div className="min-h-screen flex flex-col sm:flex-row">
      {/* User settings sidebar with custom content */}
      <ResponsiveSidebar
        routes={routesWithIcons}
        title="Settings"
        backgroundColor="#1f2937" // gray-800
        textColor="#ffffff"
      >
        {/* Back to conversations button - only visible on desktop */}
      </ResponsiveSidebar>

      {/* Main content */}
      <div className="flex-1 flex flex-col min-h-screen overflow-y-auto">
        <header className="md:p-4 p-2 flex justify-end items-center border-b">
          <UserButton />
        </header>
        <div className="md:p-4 p-1 flex-1">{children}</div>
      </div>
      <Toaster />
    </div>
  );
};

export default Layout;
