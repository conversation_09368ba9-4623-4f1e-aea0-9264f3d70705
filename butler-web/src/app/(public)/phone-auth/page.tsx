"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import PhoneAuth from "@/components/auth/PhoneAuth";

export default function PhoneAuthPage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  useEffect(() => {
    // Redirect if already authenticated
    if (!isLoading && user) {
      router.push("/chat");
    }
  }, [user, isLoading, router]);

  const handleAuthSuccess = () => {
    router.push("/chat");
  };

  const handleCancel = () => {
    router.push("/");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Welcome to Butler
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Quick and easy authentication with your phone number
          </p>
        </div>
        
        <PhoneAuth 
          onSuccess={handleAuthSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
}
