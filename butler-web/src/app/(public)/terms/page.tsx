"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { FileText, Scale, AlertTriangle, CheckCircle } from "lucide-react";
import Image from "next/image";

export default function TermsOfServicePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 flex justify-between items-center px-6 py-4 bg-white/80 backdrop-blur-sm shadow-sm">
        <div className="flex items-center gap-2">
          <Image
            src="/logos/butler.png"
            alt="Butler Logo"
            width={32}
            height={32}
            className="w-8 h-8"
          />
          <div className="text-2xl font-bold text-blue-600">Butler</div>
        </div>
        <div className="space-x-4">
          <Button variant="ghost" onClick={() => router.push("/")}>
            Home
          </Button>
          <Button variant="ghost" onClick={() => router.push("/about")}>
            About
          </Button>
          <Button variant="default" onClick={() => router.push("/login")}>
            Login
          </Button>
        </div>
      </nav>

      {/* Header */}
      <section className="bg-gradient-to-b from-blue-50 to-white py-16">
        <div className="container mx-auto px-6 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <FileText className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Terms of Service
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Please read these terms carefully before using our platform. By using Butler, you agree to these terms.
          </p>
          <p className="text-sm text-gray-500 mt-4">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>
      </section>

      {/* Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6 max-w-4xl">
          <div className="prose prose-lg max-w-none">
            {/* Acceptance */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                <CheckCircle className="w-6 h-6 text-blue-600" />
                Acceptance of Terms
              </h2>
              <p className="text-gray-600 leading-relaxed">
                By accessing and using Butler&apos;s platform, mobile application, or services, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
              </p>
            </div>

            {/* Service Description */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Service Description</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                Butler provides an AI-powered restaurant management and food ordering platform that includes:
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-3">For Customers</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>AI-powered food ordering system</li>
                    <li>Restaurant discovery and recommendations</li>
                    <li>Order tracking and history</li>
                    <li>Payment processing</li>
                    <li>Customer support</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-3">For Restaurants</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Order management system</li>
                    <li>Menu and inventory management</li>
                    <li>Analytics and reporting</li>
                    <li>Staff management tools</li>
                    <li>Payment processing</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* User Accounts */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">User Accounts</h2>
              <div className="space-y-4">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Account Creation</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>You must provide accurate and complete information</li>
                    <li>You are responsible for maintaining account security</li>
                    <li>You must be at least 18 years old to create an account</li>
                    <li>One person or entity may not maintain multiple accounts</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Account Responsibilities</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Keep your login credentials confidential</li>
                    <li>Notify us immediately of any unauthorized access</li>
                    <li>You are liable for all activities under your account</li>
                    <li>Update your information when it changes</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Acceptable Use */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                <Scale className="w-6 h-6 text-blue-600" />
                Acceptable Use Policy
              </h2>
              <div className="bg-green-50 p-6 rounded-lg mb-6">
                <h3 className="text-lg font-semibold mb-3 text-green-800">You May:</h3>
                <ul className="list-disc list-inside text-green-700 space-y-2">
                  <li>Use the platform for legitimate food ordering and restaurant management</li>
                  <li>Provide honest reviews and feedback</li>
                  <li>Contact customer support for assistance</li>
                  <li>Share your positive experiences with others</li>
                </ul>
              </div>
              
              <div className="bg-red-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-3 text-red-800">You May Not:</h3>
                <ul className="list-disc list-inside text-red-700 space-y-2">
                  <li>Use the platform for illegal activities</li>
                  <li>Attempt to hack, disrupt, or damage our systems</li>
                  <li>Create fake accounts or impersonate others</li>
                  <li>Post offensive, harmful, or inappropriate content</li>
                  <li>Violate any applicable laws or regulations</li>
                  <li>Interfere with other users&apos; use of the platform</li>
                </ul>
              </div>
            </div>

            {/* Orders and Payments */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Orders and Payments</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Order Processing</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Orders are subject to restaurant acceptance and availability</li>
                    <li>Prices may vary and are subject to change</li>
                    <li>We reserve the right to refuse or cancel orders</li>
                    <li>Delivery times are estimates and may vary</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Payment Terms</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Payment is due at the time of order placement</li>
                    <li>We accept various payment methods as displayed</li>
                    <li>Refunds are processed according to our refund policy</li>
                    <li>You are responsible for any applicable taxes</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Intellectual Property */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Intellectual Property</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                The Butler platform, including its design, functionality, and content, is protected by intellectual property laws:
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>All platform content is owned by Butler or licensed to us</li>
                <li>You may not copy, modify, or distribute our content without permission</li>
                <li>Restaurant content is owned by respective restaurant partners</li>
                <li>User-generated content remains your property but grants us usage rights</li>
              </ul>
            </div>

            {/* Disclaimers */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                <AlertTriangle className="w-6 h-6 text-yellow-600" />
                Disclaimers and Limitations
              </h2>
              <div className="bg-yellow-50 p-6 rounded-lg">
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li>The platform is provided &quot;as is&quot; without warranties</li>
                  <li>We are not responsible for restaurant service quality</li>
                  <li>Food safety and quality are the restaurant&apos;s responsibility</li>
                  <li>We do not guarantee uninterrupted service availability</li>
                  <li>Our liability is limited to the maximum extent permitted by law</li>
                </ul>
              </div>
            </div>

            {/* Termination */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Termination</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">By You</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>You may close your account at any time</li>
                    <li>Contact support for account deletion</li>
                    <li>Some data may be retained as required by law</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-3">By Us</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>We may suspend accounts for policy violations</li>
                    <li>Termination may be immediate for serious breaches</li>
                    <li>We will provide notice when reasonably possible</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Governing Law */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Governing Law</h2>
              <p className="text-gray-600 leading-relaxed">
                These terms are governed by the laws of India. Any disputes will be resolved in the courts of [Your Jurisdiction]. If any provision of these terms is found to be unenforceable, the remaining provisions will continue in full force and effect.
              </p>
            </div>

            {/* Changes to Terms */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Changes to Terms</h2>
              <p className="text-gray-600 leading-relaxed">
                We reserve the right to modify these terms at any time. We will notify users of material changes by posting the updated terms on this page and updating the &quot;Last updated&quot; date. Your continued use of the platform after changes constitutes acceptance of the new terms.
              </p>
            </div>

            {/* Contact Information */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Contact Information</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="text-gray-600 leading-relaxed mb-4">
                  If you have questions about these Terms of Service, please contact us:
                </p>
                <ul className="text-gray-600 space-y-2">
                  <li><strong>Email:</strong> <EMAIL></li>
                  <li><strong>Phone:</strong> +91 9876543210</li>
                  <li><strong>Address:</strong> Butler Legal Team, [Your Address]</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of restaurants and customers using Butler
          </p>
          <div className="flex justify-center gap-4">
            <Button
              size="lg"
              variant="secondary"
              onClick={() => router.push("/contact")}
              className="text-blue-600"
            >
              Partner With Us
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={() => router.push("/conversations")}
              className="text-blue-600"
            >
              Start Ordering
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
