/* Mobile viewport handling */
:root {
  --vh: 1vh;
}

/* Prevent zoom on input focus on iOS */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="url"],
textarea,
select {
  font-size: 16px !important;
}

/* Main container styles */
.main-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Input container improvements */
.input-container {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  z-index: 50;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Mobile specific fixes */
@media screen and (max-width: 768px) {
  html {
    height: 100%;
    overflow: hidden;
  }

  body {
    height: 100%;
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  .main-container {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for modern browsers */
    max-height: 100vh;
    max-height: 100dvh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  /* Chat area should be scrollable */
  .chat-area {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: env(safe-area-inset-bottom, 0);
  }

  /* Input area fixed at bottom */
  .input-container {
    flex-shrink: 0;
    position: relative;
    bottom: 0;
    padding-bottom: env(safe-area-inset-bottom, 0);
  }

  /* Prevent body scroll when keyboard is open */
  body.keyboard-open {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  /* Ensure proper height calculation */
  .main-container.keyboard-open {
    height: 100vh;
    height: 100dvh;
    max-height: 100vh;
    max-height: 100dvh;
  }
}

/* Enhanced input styling */
.input-container input {
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.input-container input:focus {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

/* Send button animations */
.send-button {
  transition: all 0.2s ease-in-out;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
}

/* Loading animation for send button */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Typing indicator animation */
@keyframes typing-dot {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

.typing-dot {
  animation: typing-dot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

/* Responsive dropdown positioning */
@media (max-width: 768px) {
  .dropdown-content {
    position: fixed !important;
    top: auto !important;
    bottom: 80px !important;
    left: 8px !important;
    right: 8px !important;
    width: calc(100vw - 16px) !important;
    max-width: none !important;
    transform: none !important;
  }
}

#streamingText {
  display: inline-block;
}

@keyframes bounceIn {
  0% {
    transform: translateY(30px);
    opacity: 1;
  }
  80% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

#streamingText {
  animation: bounceIn 0.4s ease-in-out forwards; /* Bounce effect with a smooth ease-out */
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Ensure header stays visible */
  .chat-header {
    position: sticky;
    top: 0;
    z-index: 50;
  }

  /* Improve touch targets */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for mobile */
  .mobile-padding {
    padding: 8px;
  }

  /* Responsive text sizing */
  .mobile-text {
    font-size: 14px;
  }
}
  