"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { <PERSON>, Lock, Eye, UserCheck } from "lucide-react";
import Image from "next/image";

export default function PrivacyPolicyPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 flex justify-between items-center px-6 py-4 bg-white/80 backdrop-blur-sm shadow-sm">
        <div className="flex items-center gap-2">
          <Image
            src="/logos/butler.png"
            alt="Butler Logo"
            width={32}
            height={32}
            className="w-8 h-8"
          />
          <div className="text-2xl font-bold text-blue-600">Butler</div>
        </div>
        <div className="space-x-4">
          <Button variant="ghost" onClick={() => router.push("/")}>
            Home
          </Button>
          <Button variant="ghost" onClick={() => router.push("/about")}>
            About
          </Button>
          <Button variant="default" onClick={() => router.push("/login")}>
            Login
          </Button>
        </div>
      </nav>

      {/* Header */}
      <section className="bg-gradient-to-b from-blue-50 to-white py-16">
        <div className="container mx-auto px-6 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Shield className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Privacy Policy
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Your privacy is important to us. This policy explains how we collect,
            use, and protect your information.
          </p>
          <p className="text-sm text-gray-500 mt-4">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>
      </section>

      {/* Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6 max-w-4xl">
          <div className="prose prose-lg max-w-none">
            {/* Introduction */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                <UserCheck className="w-6 h-6 text-blue-600" />
                Introduction
              </h2>
              <p className="text-gray-600 leading-relaxed">
                Butler (&quot;we,&quot; &quot;our,&quot; or &quot;us&quot;) is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our restaurant management platform and ordering system.
              </p>
            </div>

            {/* Information We Collect */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                <Eye className="w-6 h-6 text-blue-600" />
                Information We Collect
              </h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Personal Information</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Name, email address, and phone number</li>
                    <li>Delivery address and location information</li>
                    <li>Payment information (processed securely through third-party providers)</li>
                    <li>Account credentials and preferences</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Usage Information</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Order history and preferences</li>
                    <li>Conversation data with our AI assistant</li>
                    <li>Device information and IP address</li>
                    <li>App usage patterns and analytics</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-3">Restaurant Data</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Menu items, pricing, and availability</li>
                    <li>Restaurant location and operating hours</li>
                    <li>Staff information and roles</li>
                    <li>Sales and analytics data</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* How We Use Information */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">How We Use Your Information</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-3">For Customers</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Process and fulfill your orders</li>
                    <li>Provide personalized recommendations</li>
                    <li>Send order updates and notifications</li>
                    <li>Improve our AI assistant</li>
                    <li>Customer support and communication</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-3">For Restaurants</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Manage orders and inventory</li>
                    <li>Provide analytics and insights</li>
                    <li>Process payments and transactions</li>
                    <li>Enable staff management features</li>
                    <li>Platform maintenance and support</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Data Security */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                <Lock className="w-6 h-6 text-blue-600" />
                Data Security
              </h2>
              <div className="bg-blue-50 p-6 rounded-lg">
                <p className="text-gray-700 leading-relaxed mb-4">
                  We implement industry-standard security measures to protect your information:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Encryption of data in transit and at rest</li>
                  <li>Secure payment processing through certified providers</li>
                  <li>Regular security audits and updates</li>
                  <li>Access controls and authentication measures</li>
                  <li>Monitoring for unauthorized access attempts</li>
                </ul>
              </div>
            </div>

            {/* Information Sharing */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Information Sharing</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                We do not sell your personal information. We may share your information only in the following circumstances:
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>With restaurants to fulfill your orders</li>
                <li>With payment processors for transaction processing</li>
                <li>With service providers who assist in platform operations</li>
                <li>When required by law or to protect our rights</li>
                <li>With your explicit consent</li>
              </ul>
            </div>

            {/* Your Rights */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Your Rights</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Access & Control</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>View and update your profile information</li>
                    <li>Download your data</li>
                    <li>Delete your account</li>
                    <li>Opt out of marketing communications</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-3">Data Protection</h3>
                  <ul className="list-disc list-inside text-gray-600 space-y-2">
                    <li>Request data correction</li>
                    <li>Limit data processing</li>
                    <li>Data portability</li>
                    <li>File privacy complaints</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Cookies */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Cookies and Tracking</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                We use cookies and similar technologies to enhance your experience:
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>Essential cookies for platform functionality</li>
                <li>Analytics cookies to improve our services</li>
                <li>Preference cookies to remember your settings</li>
                <li>You can manage cookie preferences in your browser</li>
              </ul>
            </div>

            {/* Contact */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Contact Us</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="text-gray-600 leading-relaxed mb-4">
                  If you have questions about this Privacy Policy or our data practices, please contact us:
                </p>
                <ul className="text-gray-600 space-y-2">
                  <li><strong>Email:</strong> <EMAIL></li>
                  <li><strong>Phone:</strong> +91 9876543210</li>
                  <li><strong>Address:</strong> Butler Privacy Team, [Your Address]</li>
                </ul>
              </div>
            </div>

            {/* Updates */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Policy Updates</h2>
              <p className="text-gray-600 leading-relaxed">
                We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new policy on this page and updating the &quot;Last updated&quot; date. Your continued use of our services after any changes constitutes acceptance of the updated policy.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">Questions About Privacy?</h2>
          <p className="text-xl mb-8 opacity-90">
            Our team is here to help you understand how we protect your data
          </p>
          <Button
            size="lg"
            variant="secondary"
            onClick={() => router.push("/contact")}
            className="text-blue-600"
          >
            Contact Us
          </Button>
        </div>
      </section>
    </div>
  );
}
