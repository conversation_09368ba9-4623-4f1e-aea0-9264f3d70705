"use client";
import { ConversationListData } from "@/app/type";
import { useTheme } from "@/contexts/ThemeContext";
import { getAllConversation } from "@/server/user";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import { usePathname } from "next/navigation";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react/dist/iconify.js";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { formatTime } from "@/app/helper/time";
import { LoaderCircle } from "lucide-react";

const ConversationPage = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(true);
  const [conversations, setConversations] = useState<ConversationListData[]>(
    []
  );
  const [lastRefresh, setLastRefresh] = useState<number>(Date.now());

  const formatOutletDisplay = (name: string, address: string) => {
    const shortAddress = address.split(",")[0]; // Take first part of address
    return `${name} (${shortAddress})`;
  };

  const getAllConversationFunc = async () => {
    getAllConversation()
      .then((data) => {
        if (data && data.data) {
          // Sort conversations by updatedAt timestamp (most recent first)
          const sortedConversations = [...data.data].sort((a, b) => {
            return (
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
          });
          setConversations(sortedConversations);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // Refresh conversations when the page becomes visible
  useEffect(() => {
    // Initial load
    setLoading(true);
    getAllConversationFunc();

    // Set up an interval to refresh conversations every 30 seconds
    const intervalId = setInterval(() => {
      if (pathname === "/conversations") {
        getAllConversationFunc();
        setLastRefresh(Date.now());
      }
    }, 30000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [pathname]);

  // Refresh when the component mounts or when returning to this page
  useEffect(() => {
    const handleFocus = () => {
      if (pathname === "/conversations" && Date.now() - lastRefresh > 5000) {
        getAllConversationFunc();
        setLastRefresh(Date.now());
      }
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      window.removeEventListener("focus", handleFocus);
    };
  }, [pathname, lastRefresh]);
  return (
    <div className="h-screen overflow-y-scroll">
      <div className="bg-white fixed z-10 p-3 flex border-b justify-between w-full items-center">
        <div className=" font-bold text-xl flex items-center gap-2">
          <Avatar
            className="h-9 w-9 border"
            style={{
              backgroundColor: `${theme.secondaryColor}30`,
              borderColor: `${theme.secondaryColor}50`,
            }}
          >
            <AvatarFallback
              className="text-sm font-medium"
              style={{ color: theme.secondaryColor }}
            >
              C
            </AvatarFallback>
          </Avatar>
          <div>Conversations</div>
        </div>
        <div>
          <DropdownMenu
          // onOpenChange={(open) => {
          //   setIsOpen(open);
          //   if (open) setIsMenuClicked(true);
          // }}
          >
            <DropdownMenuTrigger asChild>
              <Button variant={"ghost"} className="rounded-full">
                <Icon
                  icon="simple-line-icons:options-vertical"
                  width="1024"
                  height="1024"
                />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent
              className="rounded-lg border-0 mr-6"
              sideOffset={5}
            >
              <DropdownMenuLabel className="font-bold">Menu</DropdownMenuLabel>
              <Separator />
              <DropdownMenuItem className="w-full">
                <Link
                  href={"/outlets"}
                  className="flex justify-between items-center w-full"
                >
                  Outlets
                  <div className="">
                    <Icon
                      icon="material-symbols:location-on-outline"
                      width="24"
                      height="24"
                    />
                  </div>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem className="w-full">
                <Link
                  href={"/orders"}
                  className="flex justify-between items-center w-full"
                >
                  Orders
                  <div className="">
                    <Icon
                      icon="material-symbols:order-approve-outline-rounded"
                      width="24"
                      height="24"
                    />
                  </div>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem className="w-full">
                <Link
                  href={"/user-settings/profile"}
                  className="flex justify-between items-center w-full"
                >
                  Settings
                  <div className="">
                    <Icon icon="ic:round-settings" width="24" height="24" />
                  </div>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem className="w-full">
                <div
                  onClick={() => {
                    localStorage.removeItem("user-token");
                    localStorage.removeItem("userId");
                    document.cookie =
                      "user-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC;";
                    window.location.reload();
                  }}
                  className="flex justify-between items-center w-full"
                >
                  Logout
                  <div className="">
                    <Icon icon="ic:round-logout" width="24" height="24" />
                  </div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="h-full mt-16 p-2">
        {loading ? (
          <div className="h-full w-full flex justify-center items-center animate-spin">
            <LoaderCircle />
          </div>
        ) : conversations?.length > 0 ? (
          conversations.map((conversation, index) => (
            <Card
              key={index}
              className="hover:bg-slate-50 transition-colors cursor-pointer"
              onClick={() => {
                router.push(
                  `/chat?chainId=${conversation.foodChainId}&outletId=${
                    conversation.conversationId.split("_")[1]
                  }`
                );
                localStorage.setItem("chainId", conversation.foodChainId);
                localStorage.setItem(
                  "outletId",
                  conversation.conversationId.split("_")[1]
                );
              }}
            >
              <CardContent className="p-2 flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage
                    src={conversation?.outletImage || ""}
                    alt={conversation.outletName}
                  />
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {conversation.outletName.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-grow overflow-hidden">
                  <div className="flex items-baseline gap-2">
                    <span className="font-semibold text-sm">
                      {formatOutletDisplay(
                        conversation?.outletName,
                        conversation?.outletAddress
                      )}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground truncate mt-1">
                    {conversation?.lastMessage?.message}
                  </p>
                </div>

                <div className="text-xs text-muted-foreground whitespace-nowrap">
                  {conversation?.lastMessage != null
                    ? typeof conversation?.lastMessage?.time === "string"
                      ? formatTime(conversation?.lastMessage?.time)
                      : format(
                          new Date(conversation?.lastMessage?.time),
                          "h:mm a"
                        )
                    : ""}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="flex justify-center items-center h-full flex-col">
            <div>
              No Conversations, start a conversation by ordering from an outlet
            </div>
            <Button>
              <Link href="/outlets">Find Outlets</Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationPage;
