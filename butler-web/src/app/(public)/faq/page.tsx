"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ChevronDown, ChevronUp, HelpCircle, Users, Building2, Smartphone } from "lucide-react";
import Image from "next/image";

export default function FAQPage() {
  const router = useRouter();
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 flex justify-between items-center px-6 py-4 bg-white/80 backdrop-blur-sm shadow-sm">
        <div className="flex items-center gap-2">
          <Image
            src="/logos/butler.png"
            alt="Butler Logo"
            width={32}
            height={32}
            className="w-8 h-8"
          />
          <div className="text-2xl font-bold text-blue-600">Butler</div>
        </div>
        <div className="space-x-4">
          <Button variant="ghost" onClick={() => router.push("/")}>
            Home
          </Button>
          <Button variant="ghost" onClick={() => router.push("/about")}>
            About
          </Button>
          <Button variant="default" onClick={() => router.push("/login")}>
            Login
          </Button>
        </div>
      </nav>

      {/* Header */}
      <section className="bg-gradient-to-b from-blue-50 to-white py-16">
        <div className="container mx-auto px-6 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <HelpCircle className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Find answers to common questions about Butler&apos;s platform, features, and services.
          </p>
        </div>
      </section>

      {/* FAQ Categories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6 max-w-6xl">
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="text-center p-6 border rounded-lg hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">For Customers</h3>
              <p className="text-gray-600">Questions about ordering, payments, and using our app</p>
            </div>
            
            <div className="text-center p-6 border rounded-lg hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Building2 className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">For Restaurants</h3>
              <p className="text-gray-600">Information about partnerships, features, and pricing</p>
            </div>
            
            <div className="text-center p-6 border rounded-lg hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Technical</h3>
              <p className="text-gray-600">Platform features, AI assistant, and troubleshooting</p>
            </div>
          </div>

          {/* Customer FAQs */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <Users className="w-6 h-6 text-blue-600" />
              For Customers
            </h2>
            <div className="space-y-4">
              {customerFAQs.map((faq, index) => (
                <div key={index} className="border rounded-lg">
                  <button
                    className="w-full p-4 text-left flex justify-between items-center hover:bg-gray-50"
                    onClick={() => toggleItem(index)}
                  >
                    <span className="font-medium">{faq.question}</span>
                    {openItems.includes(index) ? (
                      <ChevronUp className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {openItems.includes(index) && (
                    <div className="p-4 pt-0 text-gray-600">
                      {faq.answer}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Restaurant FAQs */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <Building2 className="w-6 h-6 text-blue-600" />
              For Restaurants
            </h2>
            <div className="space-y-4">
              {restaurantFAQs.map((faq, index) => (
                <div key={index} className="border rounded-lg">
                  <button
                    className="w-full p-4 text-left flex justify-between items-center hover:bg-gray-50"
                    onClick={() => toggleItem(index + 100)}
                  >
                    <span className="font-medium">{faq.question}</span>
                    {openItems.includes(index + 100) ? (
                      <ChevronUp className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {openItems.includes(index + 100) && (
                    <div className="p-4 pt-0 text-gray-600">
                      {faq.answer}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Technical FAQs */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <Smartphone className="w-6 h-6 text-blue-600" />
              Technical Questions
            </h2>
            <div className="space-y-4">
              {technicalFAQs.map((faq, index) => (
                <div key={index} className="border rounded-lg">
                  <button
                    className="w-full p-4 text-left flex justify-between items-center hover:bg-gray-50"
                    onClick={() => toggleItem(index + 200)}
                  >
                    <span className="font-medium">{faq.question}</span>
                    {openItems.includes(index + 200) ? (
                      <ChevronUp className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {openItems.includes(index + 200) && (
                    <div className="p-4 pt-0 text-gray-600">
                      {faq.answer}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">Still Have Questions?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Can&apos;t find what you&apos;re looking for? Our support team is here to help you.
          </p>
          <div className="flex justify-center gap-4">
            <Button
              size="lg"
              onClick={() => router.push("/contact")}
            >
              Contact Support
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={() => router.push("/conversations")}
            >
              Try Our AI Assistant
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}

const customerFAQs = [
  {
    question: "How do I place an order using Butler?",
    answer: "Simply visit our platform, browse restaurants in your area, chat with our AI assistant to get recommendations, add items to your cart, and proceed to checkout. Our AI can help you discover new dishes based on your preferences!"
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards, debit cards, UPI, net banking, and digital wallets. All payments are processed securely through our certified payment partners."
  },
  {
    question: "How does the AI assistant work?",
    answer: "Our AI assistant understands natural language and can help you discover dishes, answer questions about ingredients, suggest items based on your preferences, and guide you through the ordering process. Just chat with it like you would with a friend!"
  },
  {
    question: "Can I track my order?",
    answer: "Yes! Once you place an order, you can track its status in real-time through our app or website. You'll receive notifications when your order is confirmed, being prepared, and ready for pickup or delivery."
  },
  {
    question: "What if I need to cancel or modify my order?",
    answer: "You can cancel or modify your order before the restaurant starts preparing it. Contact the restaurant directly or reach out to our support team for assistance."
  },
  {
    question: "How do I create an account?",
    answer: "You can create an account using your email and password, phone number, or sign up with Google. We've made the process quick and simple so you can start ordering right away."
  }
];

const restaurantFAQs = [
  {
    question: "How can my restaurant join Butler?",
    answer: "Visit our contact page and fill out the restaurant registration form. Our team will review your application and get back to you within 2-3 business days to discuss partnership details."
  },
  {
    question: "What are the pricing plans for restaurants?",
    answer: "We offer flexible pricing plans starting from ₹1,999/month for up to 2 outlets. Our Growth plan (₹4,999/month) includes advanced features like AI integration and analytics. Contact us for custom enterprise solutions."
  },
  {
    question: "Do you provide training for restaurant staff?",
    answer: "Yes! We provide comprehensive training for your staff on using our platform, including order management, menu updates, and customer service features. We also offer ongoing support."
  },
  {
    question: "How does the AI help my restaurant?",
    answer: "Our AI handles customer queries, provides personalized recommendations, processes orders efficiently, and reduces the workload on your staff. It can also help with upselling and cross-selling based on customer preferences."
  },
  {
    question: "Can I manage multiple outlets?",
    answer: "Absolutely! Our platform is designed for restaurant chains. You can manage multiple outlets from a single dashboard, with centralized menu management, analytics, and staff coordination."
  },
  {
    question: "What kind of analytics do you provide?",
    answer: "We provide detailed insights into sales performance, customer behavior, popular dishes, peak hours, and much more. Our analytics help you make data-driven decisions to grow your business."
  }
];

const technicalFAQs = [
  {
    question: "Is the Butler platform available as a mobile app?",
    answer: "Yes! Butler is available as a Progressive Web App (PWA) that works seamlessly on both mobile and desktop devices. You can install it on your phone for a native app-like experience."
  },
  {
    question: "What languages does the AI assistant support?",
    answer: "Our AI assistant supports multiple languages including English, Hindi, Tamil, and other regional languages. It can understand and respond in the language you're most comfortable with."
  },
  {
    question: "Is my data secure on Butler?",
    answer: "Yes, we take data security very seriously. We use industry-standard encryption, secure payment processing, and follow strict privacy policies to protect your information."
  },
  {
    question: "What if the app is not working properly?",
    answer: "If you're experiencing technical issues, try refreshing the page or clearing your browser cache. If the problem persists, contact our support team with details about the issue you're facing."
  },
  {
    question: "Can I use Butler offline?",
    answer: "Some features like viewing your order history and saved preferences work offline, but placing new orders requires an internet connection to ensure real-time communication with restaurants."
  },
  {
    question: "How accurate are the delivery time estimates?",
    answer: "Our delivery time estimates are based on real-time data from restaurants and delivery partners. While we strive for accuracy, actual times may vary due to factors like weather, traffic, and order volume."
  }
];
