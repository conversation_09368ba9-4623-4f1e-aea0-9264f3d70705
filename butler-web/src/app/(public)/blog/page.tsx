"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import {
  Calendar,
  Clock,
  User,
  ArrowRight,
  BookOpen,
  Brain,
  TrendingUp,
  Shield,
  Smartphone,
} from "lucide-react";
import Image from "next/image";
import Head from "next/head";

export default function BlogPage() {
  const router = useRouter();

  return (
    <>
      <Head>
        <title>Blog & Resources - Butler | Restaurant Industry Insights</title>
        <meta
          name="description"
          content="Stay updated with the latest trends in restaurant technology, AI innovations, and industry insights from <PERSON>'s expert team."
        />
        <meta
          name="keywords"
          content="restaurant blog, AI technology, restaurant trends, food industry insights, restaurant management tips"
        />
        <meta
          property="og:title"
          content="Butler Blog - Restaurant Industry Insights"
        />
        <meta
          property="og:description"
          content="Stay updated with the latest trends in restaurant technology and industry insights."
        />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://butler.com/blog" />
      </Head>
      <div className="min-h-screen">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 flex justify-between items-center px-6 py-4 bg-white/80 backdrop-blur-sm shadow-sm">
          <div className="flex items-center gap-2">
            <Image
              src="/logos/butler.png"
              alt="Butler Logo"
              width={32}
              height={32}
              className="w-8 h-8"
            />
            <div className="text-2xl font-bold text-blue-600">Butler</div>
          </div>
          <div className="space-x-4">
            <Button variant="ghost" onClick={() => router.push("/")}>
              Home
            </Button>
            <Button variant="ghost" onClick={() => router.push("/about")}>
              About
            </Button>
            <Button variant="default" onClick={() => router.push("/login")}>
              Login
            </Button>
          </div>
        </nav>

        {/* Header */}
        <section className="bg-gradient-to-b from-blue-50 to-white py-16">
          <div className="container mx-auto px-6 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <BookOpen className="w-8 h-8 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Blog & Resources
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Stay updated with the latest trends in restaurant technology, AI
              innovations, and industry insights from our expert team.
            </p>
          </div>
        </section>

        {/* Featured Article */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6 max-w-6xl">
            <div className="mb-12">
              <h2 className="text-3xl font-bold mb-8">Featured Article</h2>
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8 md:p-12">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        Dec 15, 2024
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />5 min read
                      </span>
                      <span className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        Butler Team
                      </span>
                    </div>
                    <h3 className="text-2xl md:text-3xl font-bold mb-4">
                      The Future of Restaurant Management: How AI is
                      Transforming the Industry
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Discover how artificial intelligence is revolutionizing
                      restaurant operations, from automated ordering systems to
                      predictive analytics that help optimize inventory and
                      improve customer satisfaction.
                    </p>
                    <Button
                      onClick={() =>
                        router.push("/blog/ai-restaurant-management")
                      }
                      className="flex items-center gap-2"
                    >
                      Read More <ArrowRight className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="relative h-64 md:h-80 rounded-lg overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                      <div className="text-white text-center">
                        <BookOpen className="w-16 h-16 mx-auto mb-4" />
                        <p className="text-lg font-semibold">
                          Featured Article
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Recent Articles */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-6 max-w-6xl">
            <h2 className="text-3xl font-bold mb-8">Recent Articles</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogPosts.map((post, index) => (
                <article
                  key={index}
                  className="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow overflow-hidden"
                >
                  <div className="h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                    <div className="text-white text-center">
                      <post.icon className="w-12 h-12 mx-auto mb-2" />
                      <p className="font-semibold">{post.category}</p>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {post.date}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {post.readTime}
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold mb-3">{post.title}</h3>
                    <p className="text-gray-600 mb-4">{post.excerpt}</p>
                    <Button
                      variant="outline"
                      onClick={() => router.push(post.slug)}
                      className="flex items-center gap-2"
                    >
                      Read More <ArrowRight className="w-4 h-4" />
                    </Button>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>

        {/* Categories */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6 max-w-6xl">
            <h2 className="text-3xl font-bold mb-8 text-center">
              Explore by Category
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {categories.map((category, index) => (
                <div
                  key={index}
                  className="text-center p-6 border rounded-lg hover:shadow-lg transition-shadow cursor-pointer"
                  onClick={() => router.push(`/blog/category/${category.slug}`)}
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <category.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    {category.name}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {category.count} articles
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="py-16 bg-blue-600 text-white">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Subscribe to our newsletter and get the latest restaurant industry
              insights delivered to your inbox.
            </p>
            <div className="max-w-md mx-auto flex gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg text-gray-900"
              />
              <Button variant="secondary" className="text-blue-600">
                Subscribe
              </Button>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-gray-300 py-12">
          <div className="container mx-auto px-6">
            <div className="grid md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Image
                    src="/logos/butler.png"
                    alt="Butler Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                  <div className="text-xl font-bold text-white">Butler</div>
                </div>
                <p className="text-sm">
                  Transforming restaurant management with AI-powered solutions.
                </p>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Product</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/#features")}
                      className="hover:text-white transition-colors"
                    >
                      Features
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/#pricing")}
                      className="hover:text-white transition-colors"
                    >
                      Pricing
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/faq")}
                      className="hover:text-white transition-colors"
                    >
                      FAQ
                    </button>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Company</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/about")}
                      className="hover:text-white transition-colors"
                    >
                      About Us
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/blog")}
                      className="hover:text-white transition-colors"
                    >
                      Blog
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/contact")}
                      className="hover:text-white transition-colors"
                    >
                      Contact
                    </button>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Legal</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/terms")}
                      className="hover:text-white transition-colors"
                    >
                      Terms of Service
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/privacy")}
                      className="hover:text-white transition-colors"
                    >
                      Privacy Policy
                    </button>
                  </li>
                </ul>
              </div>
            </div>
            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p>© {new Date().getFullYear()} Butler. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}

const blogPosts = [
  {
    title: "5 Ways AI is Revolutionizing Restaurant Operations",
    excerpt:
      "Explore how artificial intelligence is streamlining restaurant workflows and improving customer experiences.",
    date: "Dec 10, 2024",
    readTime: "4 min read",
    category: "AI Technology",
    icon: Brain,
    slug: "/blog/ai-restaurant-operations",
  },
  {
    title: "Restaurant Analytics: Making Data-Driven Decisions",
    excerpt:
      "Learn how to leverage restaurant analytics to optimize menu pricing, inventory, and customer satisfaction.",
    date: "Dec 8, 2024",
    readTime: "6 min read",
    category: "Analytics",
    icon: TrendingUp,
    slug: "/blog/restaurant-analytics",
  },
  {
    title: "Food Safety in the Digital Age",
    excerpt:
      "Discover how technology is helping restaurants maintain the highest food safety standards.",
    date: "Dec 5, 2024",
    readTime: "3 min read",
    category: "Food Safety",
    icon: Shield,
    slug: "/blog/digital-food-safety",
  },
  {
    title: "Mobile Ordering Trends for 2025",
    excerpt:
      "Stay ahead of the curve with the latest mobile ordering trends and customer expectations.",
    date: "Dec 3, 2024",
    readTime: "5 min read",
    category: "Mobile Tech",
    icon: Smartphone,
    slug: "/blog/mobile-ordering-trends",
  },
  {
    title: "Building Customer Loyalty Through Technology",
    excerpt:
      "Learn how to use technology to create memorable experiences that keep customers coming back.",
    date: "Nov 30, 2024",
    readTime: "4 min read",
    category: "Customer Experience",
    icon: User,
    slug: "/blog/customer-loyalty-tech",
  },
  {
    title: "The Complete Guide to Restaurant POS Systems",
    excerpt:
      "Everything you need to know about choosing and implementing the right POS system for your restaurant.",
    date: "Nov 28, 2024",
    readTime: "8 min read",
    category: "Technology",
    icon: BookOpen,
    slug: "/blog/restaurant-pos-guide",
  },
];

const categories = [
  {
    name: "AI Technology",
    slug: "ai-technology",
    count: 12,
    icon: Brain,
  },
  {
    name: "Analytics",
    slug: "analytics",
    count: 8,
    icon: TrendingUp,
  },
  {
    name: "Food Safety",
    slug: "food-safety",
    count: 6,
    icon: Shield,
  },
  {
    name: "Mobile Tech",
    slug: "mobile-tech",
    count: 10,
    icon: Smartphone,
  },
];
