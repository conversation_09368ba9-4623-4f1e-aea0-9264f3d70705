export const botName = { upperCase: "<PERSON>", lowerCase: "butler" };
export const generateInitialMessages = () => {
  const time = new Date();
  const hours = time.getHours();
  if (hours < 12) {
    return "Good morning! What would you like have for breakfast?";
  } else if (hours < 18) {
    return "Good afternoon! What would you like to eat for lunch?";
  } else {
    return "Good evening! What would you like to eat for dinner?";
  }
};

export const generateChatPlaceHolder = () => {
  const time = new Date();
  const hours = time.getHours();
  if (hours < 12) {
    return "What's for the breakfast?";
  } else if (hours < 18) {
    return "What's for the lunch?";
  } else {
    return "what's for the dinner?";
  }
};

export const formatStringToHtml = (str: string) => {
  return str
    .split("\n")
    .map((line, index) => {
      const numberedPoint = line.match(/^(\d+)\.\s(.+)/);
      if (numberedPoint) {
        return `<p key=${index} class="mb-2"><strong>${numberedPoint[1]}.</strong> ${numberedPoint[2]}</p>`;
      }
      return line.trim() ? `<p key=${index} class="mb-2">${line}</p>` : "<br/>";
    })
    .join("");
};

export const firstLetterExtractor = (str: string) => {
  return str
    .split(" ")
    .map((word) => word[0])
    .join("");
};
