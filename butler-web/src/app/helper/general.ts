/* eslint-disable @typescript-eslint/no-explicit-any */
export const arrayOperations = (
  operation: "append-unique",
  array: any,
  setArray: any,
  item: any
) => {
  // Get the item ID, handling both direct _id and nested dish._id structures
  const getItemId = (item: any) => {
    if (item?._id) return item._id;
    if (item?.dish?._id) return item.dish._id;
    return null;
  };

  const itemId = getItemId(item);
  if (!itemId) {
    console.error("Invalid item structure - no ID found:", item);
    return;
  }

  switch (operation) {
    case "append-unique":
      const newArray = [...array]; // Create a shallow copy of the array

      // Check if the item already exists in the cart
      const isIncludes = newArray.some((i) => getItemId(i) === itemId);

      if (isIncludes) {
        // Remove the item if it exists
        setArray(newArray.filter((i) => getItemId(i) !== itemId));
      } else {
        // Add the item with proper structure
        // Ensure we're using the correct structure for the cart
        const newItem = {
          _id: itemId,
          name: item.name || item.dish?.name,
          price: item.price || item.dish?.price,
          description: item.description || item.dish?.description,
          category: item.category || item.dish?.category,
          image: item.image || item.dish?.image,
          isVeg: item.isVeg !== undefined ? item.isVeg : item.dish?.isVeg,
          quantity: 1,
        };

        setArray([...newArray, newItem]);
      }
      return;

    default:
      break;
  }
};

export const stringReducer = (str: string, length: number) => {
  if (str.length <= length) {
    return str;
  }
  return str.slice(0, length) + "...";
};
