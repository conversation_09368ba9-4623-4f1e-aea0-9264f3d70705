"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/custom/chain/Create<PERSON>hain";
import { getAllChains } from "@/server/super-admin";
import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { PhoneCall, Mail, Calendar, Store, Edit, Trash } from "lucide-react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";

interface Theme {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  logoUrl: string;
  favIcon: string;
  icon?: string;
}

interface Outlet {
  _id: string;
  name: string;
  address: string;
  contact: string;
}

interface FoodChain {
  _id: string;
  name: string;
  tagline?: string;
  contact: string;
  email?: string;
  theme: Theme;
  outlets: Outlet[];
  categories: string[];
  createdAt: string;
  updatedAt: string;
}

const SuperAdminChainPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(false);
  const [chains, setChains] = useState<FoodChain[]>([]);

  const getFoodChainFunc = async () => {
    setLoading(true);
    try {
      const res = await getAllChains();
      setChains(res.data);
    } catch (error) {
      console.error("Failed to fetch chains:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getFoodChainFunc();
  }, []);

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Food Chains</h1>
          <p className="text-muted-foreground">Manage your restaurant chains</p>
        </div>
        <CreateChain
          onSuccess={() => {
            getFoodChainFunc();
          }}
        />
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="p-4">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-4">
                <Skeleton className="h-20 w-full" />
              </CardContent>
              <CardFooter className="p-4 flex justify-end gap-2">
                <Skeleton className="h-8 w-8 rounded-md" />
                <Skeleton className="h-8 w-8 rounded-md" />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : chains.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 bg-muted/30 rounded-lg">
          <Store className="h-12 w-12 text-muted-foreground mb-2" />
          <h3 className="text-xl font-medium">No Food Chains Found</h3>
          <p className="text-muted-foreground mb-4">
            Get started by creating your first food chain
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {chains.map((chain) => (
            <Card
              key={chain._id}
              className="overflow-hidden border-t-4 flex flex-col justify-between"
              style={{ borderTopColor: chain.theme.primaryColor }}
            >
              <CardHeader className="p-4 pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div
                      className="h-12 w-12 rounded-md flex items-center justify-center text-white text-xl font-bold"
                      style={{ backgroundColor: chain.theme.primaryColor }}
                    >
                      {chain.theme.icon || chain.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">{chain.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {chain.tagline || "No tagline provided"}
                      </p>
                    </div>
                  </div>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Store className="h-3 w-3" />
                    {chain.outlets.length || 0}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="p-4 pt-2">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <PhoneCall className="h-4 w-4 text-muted-foreground" />
                    <span>{chain.contact}</span>
                  </div>

                  {chain.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{chain.email}</span>
                    </div>
                  )}

                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>
                      Created {format(new Date(chain.createdAt), "MMM d, yyyy")}
                    </span>
                  </div>
                </div>

                {chain.outlets.length > 0 && (
                  <>
                    <Separator className="my-3" />
                    <div>
                      <h4 className="text-sm font-medium mb-2">Outlets</h4>
                      <div className="space-y-2 max-h-32 overflow-y-auto pr-1">
                        {chain.outlets.map((outlet) => (
                          <div
                            key={outlet._id}
                            className="p-2 bg-muted rounded-md text-sm"
                          >
                            <div className="font-medium">{outlet.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {outlet.address}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>

              <CardFooter className="p-3 bg-muted/30 flex justify-between items-center">
                <div className="flex items-center gap-1">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: chain.theme.primaryColor }}
                  ></div>
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: chain.theme.secondaryColor }}
                  ></div>
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: chain.theme.accentColor }}
                  ></div>
                </div>

                <div className="flex gap-2">
                  {/* <Button variant="ghost" size="icon" className="h-8 w-8">
                    <ExternalLink className="h-4 w-4" />
                  </Button> */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => {
                      router.push(`/super-admin/chain/${chain._id}`);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-destructive"
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default SuperAdminChainPage;
