"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import {
  getSubscriptionPlans,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  getAllSubscriptions,
  createSubscription,
  exemptFromNextPayment,
  triggerMonthlyBilling,
  checkTrialPeriods,
} from "@/server/subscription";
import { getAllChains } from "@/server/super-admin";
import { Badge } from "@/components/ui/badge";
import { Indian } from "@/lib/currency";
import { format } from "date-fns";
import { Loader2, Plus, Edit } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";

// Define types
interface SubscriptionPlan {
  _id: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  interval: string;
  features: string[];
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FoodChain {
  _id: string;
  name: string;
  email: string;
  outlets: string[];
}

interface Subscription {
  _id: string;
  foodChainId: {
    _id: string;
    name: string;
    email: string;
  };
  planId: {
    _id: string;
    name: string;
    price: number;
    interval: string;
  };
  outletCount: number;
  totalAmount: number;
  status: string;
  startDate: string;
  endDate?: string;
  autoRenew: boolean;
  isExemptedFromNextPayment?: boolean;
  exemptionReason?: string;
  lastInvoiceId: {
    _id: string;
    invoiceNumber: string;
    status: string;
    amount: number;
    dueDate: string;
  };
  createdAt: string;
}

export default function SubscriptionsPage() {
  // State for plans tab
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoadingPlans, setIsLoadingPlans] = useState(true);
  const [showPlanForm, setShowPlanForm] = useState(false);
  const [editingPlan, setEditingPlan] = useState<SubscriptionPlan | null>(null);

  // State for plan form
  const [planName, setPlanName] = useState("");
  const [planDescription, setPlanDescription] = useState("");
  const [planPrice, setPlanPrice] = useState("");
  const [planInterval, setPlanInterval] = useState("monthly");
  const [planFeatures, setPlanFeatures] = useState("");
  const [isDefault, setIsDefault] = useState(false);
  const [isActive, setIsActive] = useState(true);

  // State for subscriptions tab
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [isLoadingSubscriptions, setIsLoadingSubscriptions] = useState(true);
  const [showSubscriptionDialog, setShowSubscriptionDialog] = useState(false);

  // State for subscription form
  const [foodChains, setFoodChains] = useState<FoodChain[]>([]);
  const [selectedFoodChain, setSelectedFoodChain] = useState("");
  const [selectedPlan, setSelectedPlan] = useState("");
  const [outletCount, setOutletCount] = useState("");
  const [exemptionReason, setExemptionReason] = useState("");
  const [showExemptDialog, setShowExemptDialog] = useState(false);
  const [selectedSubscription, setSelectedSubscription] =
    useState<Subscription | null>(null);
  const [isProcessingBilling, setIsProcessingBilling] = useState(false);
  const [isCheckingTrials, setIsCheckingTrials] = useState(false);
  const [autoRenew, setAutoRenew] = useState(true);

  // Fetch subscription plans
  const fetchPlans = async () => {
    setIsLoadingPlans(true);
    try {
      const response = await getSubscriptionPlans(false);
      if (response.success) {
        setPlans(response.data);
      } else {
        toast.error("Failed to fetch subscription plans");
      }
    } catch (error) {
      console.error("Error fetching plans:", error);
      toast.error("Failed to fetch subscription plans");
    } finally {
      setIsLoadingPlans(false);
    }
  };

  // Fetch subscriptions
  const fetchSubscriptions = async () => {
    setIsLoadingSubscriptions(true);
    try {
      const response = await getAllSubscriptions();
      if (response.success) {
        setSubscriptions(response.data.subscriptions);
      } else {
        toast.error("Failed to fetch subscriptions");
      }
    } catch (error) {
      console.error("Error fetching subscriptions:", error);
      toast.error("Failed to fetch subscriptions");
    } finally {
      setIsLoadingSubscriptions(false);
    }
  };

  // Fetch food chains
  const fetchFoodChains = async () => {
    try {
      const response = await getAllChains();
      if (response.success) {
        setFoodChains(response.data);
      } else {
        toast.error("Failed to fetch food chains");
      }
    } catch (error) {
      console.error("Error fetching food chains:", error);
      toast.error("Failed to fetch food chains");
    }
  };

  useEffect(() => {
    fetchPlans();
    fetchSubscriptions();
    fetchFoodChains();
  }, []);

  // Handle plan form submission
  const handlePlanSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!planName || !planPrice) {
      toast.error("Name and price are required");
      return;
    }

    const planData = {
      name: planName,
      description: planDescription,
      price: parseFloat(planPrice),
      interval: planInterval,
      features: planFeatures
        .split("\n")
        .filter((feature) => feature.trim() !== ""),
      isDefault,
      isActive,
    };

    try {
      if (editingPlan) {
        // Update existing plan
        const response = await updateSubscriptionPlan(
          editingPlan._id,
          planData
        );
        if (response.success) {
          toast.success("Subscription plan updated successfully");
          fetchPlans();
          resetPlanForm();
        } else {
          toast.error(response.message || "Failed to update subscription plan");
        }
      } else {
        // Create new plan
        const response = await createSubscriptionPlan(planData);
        if (response.success) {
          toast.success("Subscription plan created successfully");
          fetchPlans();
          resetPlanForm();
        } else {
          toast.error(response.message || "Failed to create subscription plan");
        }
      }
    } catch (error) {
      console.error("Error saving plan:", error);
      toast.error("Failed to save subscription plan");
    }
  };

  // Reset plan form
  const resetPlanForm = () => {
    setPlanName("");
    setPlanDescription("");
    setPlanPrice("");
    setPlanInterval("monthly");
    setPlanFeatures("");
    setIsDefault(false);
    setIsActive(true);
    setEditingPlan(null);
    setShowPlanForm(false);
  };

  // Edit plan
  const handleEditPlan = (plan: SubscriptionPlan) => {
    setEditingPlan(plan);
    setPlanName(plan.name);
    setPlanDescription(plan.description || "");
    setPlanPrice(plan.price.toString());
    setPlanInterval(plan.interval);
    setPlanFeatures(plan.features.join("\n"));
    setIsDefault(plan.isDefault);
    setIsActive(plan.isActive);
    setShowPlanForm(true);
  };

  // Handle subscription form submission
  const handleSubscriptionSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFoodChain || !selectedPlan) {
      toast.error("Food chain and plan are required");
      return;
    }

    const subscriptionData = {
      foodChainId: selectedFoodChain,
      planId: selectedPlan,
      outletCount: outletCount ? parseInt(outletCount) : undefined,
      autoRenew,
    };

    try {
      const response = await createSubscription(subscriptionData);
      if (response.success) {
        toast.success("Subscription created successfully");
        fetchSubscriptions();
        setShowSubscriptionDialog(false);
        resetSubscriptionForm();
      } else {
        toast.error(response.message || "Failed to create subscription");
      }
    } catch (error) {
      console.error("Error creating subscription:", error);
      toast.error("Failed to create subscription");
    }
  };

  // Reset subscription form
  const resetSubscriptionForm = () => {
    setSelectedFoodChain("");
    setSelectedPlan("");
    setOutletCount("");
    setAutoRenew(true);
  };

  // Handle exemption dialog
  const openExemptDialog = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setExemptionReason("");
    setShowExemptDialog(true);
  };

  // Handle exemption submission
  const handleExemptSubmit = async () => {
    if (!selectedSubscription) return;

    try {
      const response = await exemptFromNextPayment(
        selectedSubscription._id,
        exemptionReason
      );

      if (response.success) {
        toast.success("Subscription exempted from next payment");
        fetchSubscriptions();
        setShowExemptDialog(false);
      } else {
        toast.error(response.message || "Failed to exempt subscription");
      }
    } catch (error) {
      console.error("Error exempting subscription:", error);
      toast.error("Failed to exempt subscription");
    }
  };

  // Handle trigger monthly billing
  const handleTriggerBilling = async () => {
    setIsProcessingBilling(true);
    try {
      const response = await triggerMonthlyBilling();
      if (response.success) {
        // Show detailed results in a toast
        const results = response.data?.data || {};
        const summary = `
          Processed: ${results.processed || 0}
          Failed: ${results.failed || 0}
          Exempted: ${results.exempted || 0}
          No Outlets: ${results.noOutlets || 0}
        `;

        toast.success(
          <div>
            <p>{response.message}</p>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-xs whitespace-pre-wrap">
              {summary}
            </pre>
          </div>,
          {
            duration: 5000,
          }
        );

        // Refresh the subscriptions list to show updated data
        fetchSubscriptions();
      } else {
        toast.error(response.message || "Failed to trigger monthly billing");
      }
    } catch (error) {
      console.error("Error triggering monthly billing:", error);
      toast.error("Failed to trigger monthly billing");
    } finally {
      setIsProcessingBilling(false);
    }
  };

  // Handle check trial periods
  const handleCheckTrials = async () => {
    setIsCheckingTrials(true);
    try {
      const response = await checkTrialPeriods();
      if (response.success) {
        toast.success(
          <div>
            <p>{response.message}</p>
            {response.data?.count > 0 && (
              <p className="mt-2 text-sm">
                Notifications have been sent to the affected food chains.
              </p>
            )}
          </div>,
          {
            duration: 5000,
          }
        );
      } else {
        toast.error(response.message || "Failed to check trial periods");
      }
    } catch (error) {
      console.error("Error checking trial periods:", error);
      toast.error("Failed to check trial periods");
    } finally {
      setIsCheckingTrials(false);
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case "expired":
        return <Badge className="bg-red-500">Expired</Badge>;
      case "cancelled":
        return <Badge className="bg-gray-500">Cancelled</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Subscription Management</h1>

      <Tabs defaultValue="subscriptions">
        <TabsList className="mb-4">
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="plans">Subscription Plans</TabsTrigger>
        </TabsList>

        {/* Subscriptions Tab */}
        <TabsContent value="subscriptions">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">All Subscriptions</h2>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={handleTriggerBilling}
                  disabled={isProcessingBilling}
                >
                  {isProcessingBilling ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Trigger Monthly Billing"
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleCheckTrials}
                  disabled={isCheckingTrials}
                >
                  {isCheckingTrials ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    "Check Trial Periods"
                  )}
                </Button>
                <Button onClick={() => setShowSubscriptionDialog(true)}>
                  <Plus className="mr-2 h-4 w-4" /> Create Subscription
                </Button>
              </div>
            </div>

            {isLoadingSubscriptions ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : subscriptions.length === 0 ? (
              <Card>
                <CardContent className="py-10 text-center">
                  <p className="text-muted-foreground">
                    No subscriptions found
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Food Chain</TableHead>
                        <TableHead>Plan</TableHead>
                        <TableHead>Outlets</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead>Last Invoice</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {subscriptions.map((subscription) => (
                        <TableRow key={subscription._id}>
                          <TableCell>{subscription.foodChainId.name}</TableCell>
                          <TableCell>{subscription.planId.name}</TableCell>
                          <TableCell>{subscription.outletCount}</TableCell>
                          <TableCell>
                            {Indian(subscription.totalAmount)}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(subscription.status)}
                          </TableCell>
                          <TableCell>
                            {subscription.startDate
                              ? format(
                                  new Date(subscription.startDate),
                                  "dd MMM yyyy"
                                )
                              : "-"}
                          </TableCell>
                          <TableCell>
                            {subscription.endDate
                              ? format(
                                  new Date(subscription.endDate),
                                  "dd MMM yyyy"
                                )
                              : "-"}
                          </TableCell>
                          <TableCell>
                            {subscription.lastInvoiceId ? (
                              <div className="flex flex-col">
                                <span>
                                  {subscription.lastInvoiceId.invoiceNumber}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {getStatusBadge(
                                    subscription.lastInvoiceId.status
                                  )}
                                </span>
                              </div>
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openExemptDialog(subscription)}
                              disabled={subscription.isExemptedFromNextPayment}
                            >
                              {subscription.isExemptedFromNextPayment
                                ? "Exempted"
                                : "Exempt Next Payment"}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Create Subscription Dialog */}
          <Dialog
            open={showSubscriptionDialog}
            onOpenChange={setShowSubscriptionDialog}
          >
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Create New Subscription</DialogTitle>
                <DialogDescription>
                  Create a new subscription for a food chain.
                </DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubscriptionSubmit}>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="foodChain">Food Chain</Label>
                    <Select
                      value={selectedFoodChain}
                      onValueChange={setSelectedFoodChain}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a food chain" />
                      </SelectTrigger>
                      <SelectContent>
                        {foodChains.map((chain) => (
                          <SelectItem key={chain._id} value={chain._id}>
                            {chain.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="plan">Subscription Plan</Label>
                    <Select
                      value={selectedPlan}
                      onValueChange={setSelectedPlan}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a plan" />
                      </SelectTrigger>
                      <SelectContent>
                        {plans
                          .filter((plan) => plan.isActive)
                          .map((plan) => (
                            <SelectItem key={plan._id} value={plan._id}>
                              {plan.name} - {Indian(plan.price)}/{plan.interval}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="outletCount">
                      Number of Outlets (Optional)
                    </Label>
                    <Input
                      id="outletCount"
                      type="number"
                      min="1"
                      value={outletCount}
                      onChange={(e) => setOutletCount(e.target.value)}
                      placeholder="Leave blank to use actual outlet count"
                    />
                    <p className="text-xs text-muted-foreground">
                      If left blank, the system will count the actual number of
                      outlets.
                    </p>
                  </div>

                  <div className="space-y-2 col-span-2">
                    <p className="text-xs text-muted-foreground">
                      Start date will be set to the first day of the current
                      month, and end date will be set to the last day of the
                      current month (for monthly plans).
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="autoRenew"
                      checked={autoRenew}
                      onCheckedChange={(checked) =>
                        setAutoRenew(checked as boolean)
                      }
                    />
                    <Label htmlFor="autoRenew">Auto-renew subscription</Label>
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowSubscriptionDialog(false);
                      resetSubscriptionForm();
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">Create Subscription</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

          {/* Exempt Subscription Dialog */}
          <Dialog open={showExemptDialog} onOpenChange={setShowExemptDialog}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Exempt From Next Payment</DialogTitle>
                <DialogDescription>
                  This will exempt the subscription from the next payment cycle.
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="exemptionReason">Reason (Optional)</Label>
                  <Textarea
                    id="exemptionReason"
                    value={exemptionReason}
                    onChange={(e) => setExemptionReason(e.target.value)}
                    placeholder="Reason for exemption"
                    rows={3}
                  />
                </div>

                {selectedSubscription && (
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <p className="text-yellow-800 text-sm">
                      You are about to exempt{" "}
                      <strong>{selectedSubscription.foodChainId.name}</strong>{" "}
                      from their next payment.
                    </p>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowExemptDialog(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  variant="default"
                  onClick={handleExemptSubmit}
                >
                  Confirm Exemption
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>

        {/* Plans Tab */}
        <TabsContent value="plans">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Subscription Plans</h2>
              <Button onClick={() => setShowPlanForm(true)}>
                <Plus className="mr-2 h-4 w-4" /> Create Plan
              </Button>
            </div>

            {showPlanForm && (
              <Card>
                <CardHeader>
                  <CardTitle>
                    {editingPlan ? "Edit Plan" : "Create New Plan"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handlePlanSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="planName">Plan Name</Label>
                      <Input
                        id="planName"
                        value={planName}
                        onChange={(e) => setPlanName(e.target.value)}
                        placeholder="e.g. Basic Plan"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="planDescription">
                        Description (Optional)
                      </Label>
                      <Textarea
                        id="planDescription"
                        value={planDescription}
                        onChange={(e) => setPlanDescription(e.target.value)}
                        placeholder="Describe the plan"
                        rows={2}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="planPrice">Price (₹)</Label>
                        <Input
                          id="planPrice"
                          type="number"
                          min="0"
                          step="0.01"
                          value={planPrice}
                          onChange={(e) => setPlanPrice(e.target.value)}
                          placeholder="e.g. 2000"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="planInterval">Billing Interval</Label>
                        <Select
                          value={planInterval}
                          onValueChange={setPlanInterval}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select interval" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                            <SelectItem value="yearly">Yearly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="planFeatures">
                        Features (One per line, Optional)
                      </Label>
                      <Textarea
                        id="planFeatures"
                        value={planFeatures}
                        onChange={(e) => setPlanFeatures(e.target.value)}
                        placeholder="e.g. Unlimited outlets"
                        rows={3}
                      />
                    </div>

                    <div className="flex flex-col space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="isDefault"
                          checked={isDefault}
                          onCheckedChange={(checked) =>
                            setIsDefault(checked as boolean)
                          }
                        />
                        <Label htmlFor="isDefault">Set as default plan</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="isActive"
                          checked={isActive}
                          onCheckedChange={(checked) =>
                            setIsActive(checked as boolean)
                          }
                        />
                        <Label htmlFor="isActive">Plan is active</Label>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={resetPlanForm}
                      >
                        Cancel
                      </Button>
                      <Button type="submit">
                        {editingPlan ? "Update Plan" : "Create Plan"}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            {isLoadingPlans ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : plans.length === 0 ? (
              <Card>
                <CardContent className="py-10 text-center">
                  <p className="text-muted-foreground">No plans found</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {plans.map((plan) => (
                  <Card
                    key={plan._id}
                    className={!plan.isActive ? "opacity-60" : ""}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{plan.name}</CardTitle>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditPlan(plan)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      {plan.isDefault && (
                        <Badge className="mt-1">Default</Badge>
                      )}
                      {!plan.isActive && (
                        <Badge variant="outline" className="mt-1">
                          Inactive
                        </Badge>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="mb-4">
                        <p className="text-2xl font-bold">
                          {Indian(plan.price)}
                          <span className="text-sm font-normal text-muted-foreground">
                            /{plan.interval}
                          </span>
                        </p>
                        {plan.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {plan.description}
                          </p>
                        )}
                      </div>

                      {plan.features.length > 0 && (
                        <ul className="space-y-1 text-sm">
                          {plan.features.map((feature, index) => (
                            <li key={index} className="flex items-center">
                              <span className="mr-2">•</span>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      )}

                      <p className="text-xs text-muted-foreground mt-4">
                        Created:{" "}
                        {format(new Date(plan.createdAt), "dd MMM yyyy")}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
