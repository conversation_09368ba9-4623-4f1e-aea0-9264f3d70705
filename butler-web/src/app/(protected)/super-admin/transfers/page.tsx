"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Indian } from "@/lib/currency";

interface FoodChain {
  _id: string;
  name: string;
  razorpayFundAccountId?: string;
}

export default function SuperAdminTransfersPage() {
  const [foodChains, setFoodChains] = useState<FoodChain[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFoodChain, setSelectedFoodChain] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [transferMethod, setTransferMethod] = useState<string>("IMPS");
  const [transferring, setTransferring] = useState(false);

  const fetchFoodChains = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/super-admin/food-chains`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        setFoodChains(data.data);
      }
    } catch (error) {
      console.error("Error fetching food chains:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFoodChains();
  }, []);

  const handleTransfer = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFoodChain) {
      toast.error("Please select a food chain");
      return;
    }

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    setTransferring(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/super-admin/food-chain/${selectedFoodChain}/transfer`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
          body: JSON.stringify({
            amount: parseFloat(amount),
            description,
            transferMethod,
          }),
        }
      );

      const data = await response.json();

      if (data.success) {
        toast.success("Fund transfer initiated successfully");
        // Reset form
        setAmount("");
        setDescription("");
        setTransferMethod("IMPS");
      } else {
        toast.error(data.message || "Failed to initiate fund transfer");
      }
    } catch (error) {
      console.error("Error initiating fund transfer:", error);
      toast.error("Failed to initiate fund transfer");
    } finally {
      setTransferring(false);
    }
  };

  if (loading) {
    return <div>Loading food chains...</div>;
  }

  const selectedChain = foodChains.find((fc) => fc._id === selectedFoodChain);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Fund Transfers</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Initiate Fund Transfer</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleTransfer} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="foodChain">Food Chain</Label>
                <Select
                  value={selectedFoodChain}
                  onValueChange={setSelectedFoodChain}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a food chain" />
                  </SelectTrigger>
                  <SelectContent>
                    {foodChains.map((fc) => (
                      <SelectItem key={fc._id} value={fc._id}>
                        {fc.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedChain && !selectedChain.razorpayFundAccountId && (
                <div className="bg-yellow-50 p-3 rounded-lg text-sm text-yellow-800">
                  This food chain does not have a fund account set up yet.
                  Please ask them to set up their bank details.
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="amount">Amount (₹)</Label>
                <Input
                  id="amount"
                  type="number"
                  min="1"
                  step="0.01"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="Enter amount"
                  required
                  disabled={!selectedChain?.razorpayFundAccountId}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter description"
                  disabled={!selectedChain?.razorpayFundAccountId}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="transferMethod">Transfer Method</Label>
                <Select
                  value={transferMethod}
                  onValueChange={setTransferMethod}
                  disabled={!selectedChain?.razorpayFundAccountId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select transfer method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="IMPS">IMPS</SelectItem>
                    <SelectItem value="NEFT">NEFT</SelectItem>
                    <SelectItem value="RTGS">RTGS</SelectItem>
                    <SelectItem value="UPI">UPI</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={transferring || !selectedChain?.razorpayFundAccountId}
              >
                {transferring ? "Processing..." : "Initiate Transfer"}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Transfer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Funds are transferred from your Razorpay account to the food
                chain&apos;s bank account. The transfer process typically takes:
              </p>

              <ul className="list-disc list-inside text-sm text-gray-600 space-y-2">
                <li>
                  <span className="font-medium">IMPS:</span> Almost instant
                  (within minutes)
                </li>
                <li>
                  <span className="font-medium">NEFT:</span> Same day (within
                  hours)
                </li>
                <li>
                  <span className="font-medium">RTGS:</span> Same day (within
                  hours)
                </li>
                <li>
                  <span className="font-medium">UPI:</span> Almost instant
                  (within minutes)
                </li>
              </ul>

              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">
                  <span className="font-medium">Note:</span> Transfers can only
                  be made to food chains that have set up their bank details and
                  have a fund account ID.
                </p>
              </div>

              {amount && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Transfer Amount:</span>{" "}
                    {Indian(parseFloat(amount))}
                  </p>
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Transfer Fee:</span>{" "}
                    {Indian(parseFloat(amount) * 0.005)} (0.5%)
                  </p>
                  <p className="text-sm font-medium text-gray-800 mt-1">
                    Total Deduction: {Indian(parseFloat(amount) * 1.005)}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
