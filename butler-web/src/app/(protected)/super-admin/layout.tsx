"use client";
import { getRoutes } from "@/routes";
import { UserButton } from "@/components/UserButton";
import NotificationBell from "@/components/notifications/NotificationBell";
import { Toaster } from "sonner";
import ResponsiveSidebar from "@/components/layouts/ResponsiveSidebar";

export default function SuperAdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const routes = getRoutes("super-admin");

  return (
    <div className="min-h-screen flex flex-col sm:flex-row">
      {/* Super Admin sidebar */}
      <ResponsiveSidebar
        routes={routes}
        title="Super Admin"
        backgroundColor="#1f2937" // gray-800
        textColor="#ffffff"
      />

      {/* Main content */}
      <div className="h-screen overflow-y-auto w-full">
        <div className="flex-1 flex flex-col min-h-screen overflow-y-auto">
          <header className="border-b p-4 flex justify-end items-center gap-2 bg-white">
            <NotificationBell />
            <UserButton />
          </header>
          <main className="flex-1 p-4 md:p-8">{children}</main>
        </div>
      </div>
      <Toaster />
    </div>
  );
}
