"use client";

import React, { useEffect, useState } from "react";
import { getSuperAdminDashboard } from "@/server/super-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Indian } from "@/lib/currency";
import { format } from "date-fns";
import {
  Store,
  Users,
  ShoppingBag,
  // CreditCard,
  TrendingUp,
  // Activity,
  AlertCircle,
} from "lucide-react";

// Define types for the data structures
interface FoodChain {
  _id: string;
  name: string;
  contact: string;
  status: string;
  createdAt: string;
}

interface Order {
  _id: string;
  outletId?: {
    name: string;
  };
  userId?: {
    name: string;
  };
  status: string;
  createdAt: string;
}

interface Payment {
  _id: string;
  orderId?: {
    orderNumber: string;
  };
  amount: number;
  status: string;
  createdAt: string;
}

interface DashboardData {
  foodChains: {
    total: number;
    active: number;
    inactive: number;
    suspended: number;
    recent: FoodChain[];
  };
  outlets: {
    total: number;
    active: number;
    inactive: number;
  };
  users: {
    total: number;
    admins: number;
    customers: number;
  };
  orders: {
    total: number;
    pending: number;
    completed: number;
    cancelled: number;
    recent: Order[];
  };
  payments: {
    total: number;
    successful: number;
    failed: number;
    totalRevenue: number;
    recent: Payment[];
  };
}

const SuperAdminDashboardPage = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await getSuperAdminDashboard();
        if (response.success) {
          setDashboardData(response.data);
        } else {
          setError("Failed to fetch dashboard data");
        }
      } catch (err) {
        setError("An error occurred while fetching dashboard data");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Dashboard</h2>
        <Separator />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-8 w-24 mb-2" />
                <Skeleton className="h-12 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Dashboard</h2>
        <Separator />
        <Card className="bg-red-50">
          <CardContent className="p-6 flex items-center gap-4">
            <AlertCircle className="text-red-500" />
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Dashboard</h2>
      <Separator />

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">Food Chains</p>
                <h3 className="text-2xl font-bold mt-1">
                  {dashboardData?.foodChains.total || 0}
                </h3>
              </div>
              <Store className="h-6 w-6 text-primary" />
            </div>
            <div className="mt-4 flex justify-between text-xs text-muted-foreground">
              <span>Active: {dashboardData?.foodChains.active || 0}</span>
              <span>Inactive: {dashboardData?.foodChains.inactive || 0}</span>
              <span>Suspended: {dashboardData?.foodChains.suspended || 0}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">Outlets</p>
                <h3 className="text-2xl font-bold mt-1">
                  {dashboardData?.outlets.total || 0}
                </h3>
              </div>
              <Store className="h-6 w-6 text-primary" />
            </div>
            <div className="mt-4 flex justify-between text-xs text-muted-foreground">
              <span>Active: {dashboardData?.outlets.active || 0}</span>
              <span>Inactive: {dashboardData?.outlets.inactive || 0}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">Users</p>
                <h3 className="text-2xl font-bold mt-1">
                  {dashboardData?.users.total || 0}
                </h3>
              </div>
              <Users className="h-6 w-6 text-primary" />
            </div>
            <div className="mt-4 flex justify-between text-xs text-muted-foreground">
              <span>Admins: {dashboardData?.users.admins || 0}</span>
              <span>Customers: {dashboardData?.users.customers || 0}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <h3 className="text-2xl font-bold mt-1">
                  {Indian(dashboardData?.payments.totalRevenue || 0)}
                </h3>
              </div>
              <TrendingUp className="h-6 w-6 text-primary" />
            </div>
            <div className="mt-4 flex justify-between text-xs text-muted-foreground">
              <span>Payments: {dashboardData?.payments.total || 0}</span>
              <span>Successful: {dashboardData?.payments.successful || 0}</span>
              <span>Failed: {dashboardData?.payments.failed || 0}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">Orders</p>
                <h3 className="text-2xl font-bold mt-1">
                  {dashboardData?.orders.total || 0}
                </h3>
              </div>
              <ShoppingBag className="h-6 w-6 text-primary" />
            </div>
            <div className="mt-4 flex justify-between text-xs text-muted-foreground">
              <span>Pending: {dashboardData?.orders.pending || 0}</span>
              <span>Completed: {dashboardData?.orders.completed || 0}</span>
              <span>Cancelled: {dashboardData?.orders.cancelled || 0}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Food Chains */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Food Chains</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {dashboardData?.foodChains.recent.map((chain) => (
                  <TableRow key={chain._id}>
                    <TableCell className="font-medium">{chain.name}</TableCell>
                    <TableCell>{chain.contact}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          chain.status === "active"
                            ? "bg-green-100 text-green-800"
                            : chain.status === "inactive"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {chain.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      {format(new Date(chain.createdAt), "MMM dd, yyyy")}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Outlet</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {dashboardData?.orders.recent.map((order) => (
                  <TableRow key={order._id}>
                    <TableCell className="font-medium">
                      {order.outletId?.name || "N/A"}
                    </TableCell>
                    <TableCell>{order.userId?.name || "N/A"}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          order.status === "completed"
                            ? "bg-green-100 text-green-800"
                            : order.status === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : order.status === "cancelled"
                            ? "bg-red-100 text-red-800"
                            : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {order.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      {format(new Date(order.createdAt), "MMM dd, yyyy")}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Recent Payments */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Payments</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {dashboardData?.payments.recent.map((payment) => (
                  <TableRow key={payment._id}>
                    <TableCell className="font-medium">
                      {payment.orderId?.orderNumber || "N/A"}
                    </TableCell>
                    <TableCell>{Indian(payment.amount)}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          payment.status === "paid"
                            ? "bg-green-100 text-green-800"
                            : payment.status === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {payment.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      {format(new Date(payment.createdAt), "MMM dd, yyyy")}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Order Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Order Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col items-center justify-center p-4 bg-muted/30 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">
                  Pending
                </div>
                <div className="text-2xl font-bold">
                  {dashboardData?.orders.pending || 0}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {dashboardData?.orders.total
                    ? `${Math.round(
                        (dashboardData.orders.pending /
                          dashboardData.orders.total) *
                          100
                      )}%`
                    : "0%"}
                </div>
              </div>

              <div className="flex flex-col items-center justify-center p-4 bg-muted/30 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">
                  Completed
                </div>
                <div className="text-2xl font-bold">
                  {dashboardData?.orders.completed || 0}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {dashboardData?.orders.total
                    ? `${Math.round(
                        (dashboardData.orders.completed /
                          dashboardData.orders.total) *
                          100
                      )}%`
                    : "0%"}
                </div>
              </div>

              <div className="flex flex-col items-center justify-center p-4 bg-muted/30 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">
                  Cancelled
                </div>
                <div className="text-2xl font-bold">
                  {dashboardData?.orders.cancelled || 0}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {dashboardData?.orders.total
                    ? `${Math.round(
                        (dashboardData.orders.cancelled /
                          dashboardData.orders.total) *
                          100
                      )}%`
                    : "0%"}
                </div>
              </div>

              <div className="flex flex-col items-center justify-center p-4 bg-muted/30 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">Other</div>
                <div className="text-2xl font-bold">
                  {dashboardData?.orders.total
                    ? dashboardData.orders.total -
                      dashboardData.orders.pending -
                      dashboardData.orders.completed -
                      dashboardData.orders.cancelled
                    : 0}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {dashboardData?.orders.total
                    ? `${Math.round(
                        ((dashboardData.orders.total -
                          dashboardData.orders.pending -
                          dashboardData.orders.completed -
                          dashboardData.orders.cancelled) /
                          dashboardData.orders.total) *
                          100
                      )}%`
                    : "0%"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SuperAdminDashboardPage;
