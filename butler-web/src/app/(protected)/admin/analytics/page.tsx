/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";
import { getAnalytics } from "@/server/admin";
import { Indian } from "@/lib/currency";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Target,
  PieChart,
  Star,
} from "lucide-react";
// import { formatDateWithTimestamp } from "@/app/helper/date";
// import DateRangeSlider from "@/components/custom/analytics/DateRangeSelector";
import { useTheme } from "@/contexts/ThemeContext";
import {
  AreaChart,
  DonutChart,
  HeatmapChart,
  MultiAxisChart,
  KPICard,
  DashboardSection,
  // DashboardGrid,
  // AnalyticsFilters,
  // FilterState,
} from "@/components/analytics";
import // exportAnalyticsToExcel,
// exportAnalyticsToPDF,
"@/utils/analyticsExport";

const AnalyticsDashboard = () => {
  const { setChainAndOutlet } = useTheme();
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const daysToAnalyze = 30;
  // const [foodChainData, setFoodChainData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  // const [filters, setFilters] = useState<FilterState>({
  //   dateRange: "30",
  //   outlet: "all",
  //   category: "all",
  //   metric: "revenue",
  //   sortBy: "date",
  //   sortOrder: "desc",
  //   searchTerm: "",
  // });

  useEffect(() => {
    const chainId = localStorage.getItem("chainId");
    console.log("in the config ", chainId);
    if (chainId) {
      setChainAndOutlet(chainId);
    }
  }, []);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getAnalytics(daysToAnalyze);
        if (response.success) {
          setAnalytics(response.data);
          // setFoodChainData(response.data.foodChainData);
        } else {
          setError("Failed to fetch analytics data");
        }
      } catch (err) {
        setError("An error occurred while fetching data");
        console.error("Analytics fetch error:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [daysToAnalyze]);

  // const formatHour = (hour: number) => {
  //   if (hour === 0) return "12 AM";
  //   if (hour === 12) return "12 PM";
  //   return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
  // };

  // Prepare data for charts
  const prepareRevenueData = () => {
    return (
      analytics?.dailyRevenue?.map((item: any) => ({
        ...item,
        date: new Date(item._id).toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        }),
      })) || []
    );
  };

  const prepareHeatmapData = () => {
    return (
      analytics?.revenueByHour?.map((item: any) => ({
        day: item._id.dayOfWeek - 1, // MongoDB dayOfWeek is 1-7, convert to 0-6
        hour: item._id.hour,
        value: item.revenue,
      })) || []
    );
  };

  const prepareSparklineData = (data: any[], key: string) => {
    return (
      data?.slice(-7).map((item: any, index: number) => ({
        index,
        value: item[key] || 0,
      })) || []
    );
  };

  // Handle filter changes
  // const handleFilterChange = (newFilters: FilterState) => {
  //   setFilters(newFilters);
  //   if (newFilters.dateRange !== daysToAnalyze.toString()) {
  //     setDaysToAnalyze(parseInt(newFilters.dateRange));
  //   }
  // };

  // // Handle data export
  // const handleExport = (format: "pdf" | "excel") => {
  //   if (!analytics) return;

  //   if (format === "excel") {
  //     exportAnalyticsToExcel(analytics);
  //   } else {
  //     exportAnalyticsToPDF(analytics);
  //   }
  // };

  // // Handle refresh
  // const handleRefresh = () => {
  //   const fetchAnalytics = async () => {
  //     try {
  //       setLoading(true);
  //       setError(null);
  //       const response = await getAnalytics(daysToAnalyze);
  //       if (response.success) {
  //         setAnalytics(response.data);
  //         setFoodChainData(response.data.foodChainData);
  //       } else {
  //         setError("Failed to fetch analytics data");
  //       }
  //     } catch (err) {
  //       setError("An error occurred while fetching data");
  //       console.error("Analytics fetch error:", err);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   fetchAnalytics();
  // };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 mb-2">Error loading analytics</div>
          <div className="text-gray-600 mb-4">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6 p-4 md:p-6 max-w-full overflow-x-hidden">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">
            Analytics Dashboard
          </h1>
          <p className="text-sm md:text-base text-muted-foreground">
            Comprehensive insights for the last {daysToAnalyze} days
          </p>
        </div>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
          <div className="text-xs md:text-sm text-muted-foreground">
            Last updated: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Analytics Filters */}
      {/* <AnalyticsFilters
        onFilterChange={handleFilterChange}
        onExport={handleExport}
        onRefresh={handleRefresh}
        loading={loading}
      /> */}

      {/* Date Range Selector */}
      {/* <DashboardSection title="Date Range" collapsible={false}>
        <DateRangeSlider
          creationDate={formatDateWithTimestamp(foodChainData?.createdAt)}
          onRangeChange={(days) => setDaysToAnalyze(days)}
        />
      </DashboardSection> */}

      <Separator />

      {/* KPI Cards */}
      <DashboardSection title="Key Performance Indicators">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <KPICard
            title="Total Revenue"
            value={analytics?.summary?.totalRevenue || 0}
            change={analytics?.summary?.growth?.revenue || 0}
            sparklineData={prepareSparklineData(
              analytics?.dailyRevenue,
              "revenue"
            )}
            sparklineKey="value"
            icon={<DollarSign className="h-5 w-5" />}
            color="#10b981"
            formatValue={(value) => Indian(value)}
          />

          <KPICard
            title="Total Orders"
            value={analytics?.summary?.totalOrders || 0}
            change={analytics?.summary?.growth?.orders || 0}
            sparklineData={prepareSparklineData(
              analytics?.dailyRevenue,
              "orderCount"
            )}
            sparklineKey="value"
            icon={<ShoppingCart className="h-5 w-5" />}
            color="#3b82f6"
          />

          <KPICard
            title="Average Order Value"
            value={analytics?.summary?.averageOrderValue || 0}
            change={analytics?.summary?.growth?.averageOrderValue || 0}
            sparklineData={prepareSparklineData(
              analytics?.dailyRevenue,
              "averageOrderValue"
            )}
            sparklineKey="value"
            icon={<Target className="h-5 w-5" />}
            color="#f59e0b"
            formatValue={(value) => Indian(value)}
          />

          <KPICard
            title="Unique Customers"
            value={analytics?.summary?.uniqueCustomers || 0}
            change={analytics?.summary?.growth?.uniqueCustomers || 0}
            icon={<Users className="h-5 w-5" />}
            color="#8b5cf6"
            showSparkline={false}
          />
        </div>
      </DashboardSection>

      {/* Revenue and Orders Trend */}
      <DashboardSection title="Revenue & Orders Trend" expandable>
        <MultiAxisChart
          data={prepareRevenueData()}
          xKey="date"
          series={[
            {
              key: "revenue",
              name: "Revenue",
              type: "line",
              color: "#10b981",
              yAxisId: "left",
            },
            {
              key: "orderCount",
              name: "Orders",
              type: "bar",
              color: "#3b82f6",
              yAxisId: "right",
            },
          ]}
          height={400}
          formatLeftYAxis={(value) => Indian(value)}
          formatTooltip={(value, name) => [
            name === "Revenue" ? Indian(value) : value,
            name,
          ]}
          leftYAxisLabel="Revenue"
          rightYAxisLabel="Orders"
        />
      </DashboardSection>

      {/* Analytics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Segmentation */}
        <DashboardSection title="Customer Segmentation" expandable>
          <DonutChart
            data={analytics?.customerSegmentation || []}
            dataKey="customerCount"
            nameKey="_id"
            height={300}
            formatTooltip={(value, name) => [`${value} customers`, name]}
            centerContent={
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {analytics?.customerSegmentation?.reduce(
                    (sum: number, item: any) => sum + item.customerCount,
                    0
                  ) || 0}
                </div>
                <div className="text-sm text-muted-foreground">
                  Total Customers
                </div>
              </div>
            }
          />
        </DashboardSection>

        {/* Top Selling Items */}
        <DashboardSection title="Top Selling Items" expandable>
          <div className="space-y-3">
            {analytics?.topItems
              ?.slice(0, 8)
              .map((item: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium">
                        {item.dishDetails[0]?.name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {Indian(item.totalRevenue)} revenue
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{item.totalQuantity}</div>
                    <div className="text-sm text-muted-foreground">sold</div>
                  </div>
                </div>
              ))}
          </div>
        </DashboardSection>

        {/* Category Performance */}
        <DashboardSection title="Category Performance" expandable>
          <div className="space-y-3">
            {analytics?.categoryPerformance
              ?.slice(0, 6)
              .map((category: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-secondary/10 rounded-full flex items-center justify-center">
                      <PieChart className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">{category.categoryName}</div>
                      <div className="text-sm text-muted-foreground">
                        {category.totalQuantity} items sold
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      {Indian(category.totalRevenue)}
                    </div>
                    <div className="text-sm text-muted-foreground">revenue</div>
                  </div>
                </div>
              ))}
          </div>
        </DashboardSection>
      </div>

      {/* Revenue Heatmap */}
      <DashboardSection title="Revenue Heatmap - Day vs Hour" expandable>
        <HeatmapChart
          data={prepareHeatmapData()}
          width={800}
          height={250}
          formatValue={(value) => Indian(value)}
          colorScale={["#f0f9ff", "#0ea5e9", "#0369a1"]}
        />
      </DashboardSection>

      {/* Additional Analytics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Order Frequency */}
        <DashboardSection title="Customer Order Frequency" expandable>
          <AreaChart
            data={analytics?.customerRetention || []}
            xKey="_id"
            yKeys={[
              { key: "customerCount", name: "Customers", color: "#8b5cf6" },
            ]}
            height={300}
            formatXAxis={(value) => `${value} orders`}
            formatTooltip={(value, name) => [`${value} customers`, name]}
          />
        </DashboardSection>

        {/* Conversion Funnel */}
        <DashboardSection title="Order Status Distribution" expandable>
          <DonutChart
            data={analytics?.conversionFunnel || []}
            dataKey="count"
            nameKey="_id"
            height={300}
            formatTooltip={(value, name) => [`${value} orders`, name]}
            centerContent={
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {analytics?.conversionFunnel?.reduce(
                    (sum: number, item: any) => sum + item.count,
                    0
                  ) || 0}
                </div>
                <div className="text-sm text-muted-foreground">
                  Total Orders
                </div>
              </div>
            }
          />
        </DashboardSection>
      </div>

      {/* Popular Combinations */}
      <DashboardSection title="Popular Item Combinations" expandable>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {analytics?.popularCombinations?.map((combo: any, index: number) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 bg-muted/50 rounded-lg border"
            >
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span className="font-medium text-sm">
                    Combo #{index + 1}
                  </span>
                </div>
                <div className="space-y-1">
                  <div className="font-medium">
                    {combo.dish1Details[0]?.name}
                  </div>
                  <div className="text-sm text-muted-foreground">+</div>
                  <div className="font-medium">
                    {combo.dish2Details[0]?.name}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {combo.count}
                </div>
                <div className="text-sm text-muted-foreground">
                  times together
                </div>
              </div>
            </div>
          ))}
        </div>
      </DashboardSection>

      {/* Revenue Forecasting */}
      {analytics?.revenueForecasting && (
        <DashboardSection title="Revenue Forecast (Next 7 Days)" expandable>
          <div className="mb-4">
            <div
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${
                analytics.revenueForecasting.trend === "increasing"
                  ? "bg-green-100 text-green-800"
                  : analytics.revenueForecasting.trend === "decreasing"
                  ? "bg-red-100 text-red-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {analytics.revenueForecasting.trend === "increasing" && (
                <TrendingUp className="h-4 w-4 mr-1" />
              )}
              {analytics.revenueForecasting.trend === "decreasing" && (
                <TrendingDown className="h-4 w-4 mr-1" />
              )}
              Trend: {analytics.revenueForecasting.trend}
            </div>
          </div>
          <AreaChart
            data={analytics.revenueForecasting.forecast}
            xKey="date"
            yKeys={[
              {
                key: "predictedRevenue",
                name: "Predicted Revenue",
                color: "#10b981",
              },
            ]}
            height={300}
            formatXAxis={(value) =>
              new Date(value).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
              })
            }
            formatYAxis={(value) => Indian(value)}
            formatTooltip={(value, name) => [Indian(value), name]}
          />
        </DashboardSection>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
