"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  getFoodChainSubscription,
  getInvoiceDetails,
  getSubscriptionPlans,
  upgradeSubscriptionPlan,
} from "@/server/subscription";
import { getAdminFoodChainId } from "@/server/index";
import { Indian } from "@/lib/currency";
import { format, addMonths } from "date-fns";
import {
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock,
  ExternalLink,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";

// Define types
interface SubscriptionPlan {
  _id: string;
  name: string;
  price: number;
  interval: string;
  features: string[];
  isActive?: boolean;
  isDefault?: boolean;
}

interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

interface PaymentHistoryItem {
  status: string;
  paymentId?: string;
  invoiceId?: string;
  refundId?: string;
  amount?: number;
  method?: string;
  timestamp: string;
  details: string;
}

interface Invoice {
  _id: string;
  invoiceNumber: string;
  amount: number;
  status: string;
  dueDate: string;
  paidDate?: string;
  paymentLink?: string;
  items?: InvoiceItem[];
  paymentHistory?: PaymentHistoryItem[];
  createdAt: string;
}

interface Subscription {
  _id: string;
  planId: SubscriptionPlan;
  outletCount: number;
  totalAmount: number;
  status: string;
  startDate: string;
  endDate?: string;
  trialEndDate?: string;
  autoRenew: boolean;
  isExemptedFromNextPayment?: boolean;
  exemptionReason?: string;
  lastInvoiceId: Invoice;
  planHistory?: {
    planId: string;
    name: string;
    price: number;
    startDate: string;
    endDate?: string;
  }[];
  createdAt: string;
}

interface SubscriptionData {
  subscription: Subscription;
  invoices: Invoice[];
}

export default function SubscriptionPage() {
  const [subscriptionData, setSubscriptionData] =
    useState<SubscriptionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [showInvoiceDialog, setShowInvoiceDialog] = useState(false);
  const [availablePlans, setAvailablePlans] = useState<SubscriptionPlan[]>([]);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>("");
  const [isUpgrading, setIsUpgrading] = useState(false);

  // Fetch subscription data
  const fetchSubscriptionData = async () => {
    setIsLoading(true);
    try {
      const foodChainId = getAdminFoodChainId();
      if (!foodChainId) {
        toast.error("Food chain ID not found");
        return;
      }

      const response = await getFoodChainSubscription(foodChainId);
      if (response.success) {
        setSubscriptionData(response.data);

        // Fetch all available active plans
        const plansResponse = await getSubscriptionPlans(true);
        if (plansResponse.success) {
          const currentPlanId = response.data.subscription.planId._id;
          const currentPlanPrice = response.data.subscription.planId.price;

          // Check if user is super admin
          const isSuperAdmin = localStorage.getItem("superAdminToken") !== null;

          // For super admins: show all active plans except the current one
          // For regular admins: only show higher-priced plans
          const filteredPlans = plansResponse.data.filter(
            (plan: SubscriptionPlan) => {
              if (!plan.isActive || plan._id === currentPlanId) {
                return false;
              }

              // For regular admins, only show higher-priced plans
              if (!isSuperAdmin) {
                return plan.price > currentPlanPrice;
              }

              // For super admins, show all plans except the current one
              return true;
            }
          );

          setAvailablePlans(filteredPlans);
        }
      } else {
        toast.error(response.message || "Failed to fetch subscription data");
      }
    } catch (error) {
      console.error("Error fetching subscription data:", error);
      toast.error("Failed to fetch subscription data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  // View invoice details
  const viewInvoice = async (invoiceId: string) => {
    try {
      const response = await getInvoiceDetails(invoiceId);
      if (response.success) {
        setSelectedInvoice(response.data);
        setShowInvoiceDialog(true);
      } else {
        toast.error(response.message || "Failed to fetch invoice details");
      }
    } catch (error) {
      console.error("Error fetching invoice details:", error);
      toast.error("Failed to fetch invoice details");
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case "expired":
        return <Badge className="bg-red-500">Expired</Badge>;
      case "cancelled":
        return <Badge className="bg-gray-500">Cancelled</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Get invoice status badge
  const getInvoiceStatusBadge = (status: string) => {
    switch (status) {
      case "paid":
        return (
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
            <span>Paid</span>
          </div>
        );
      case "created":
        return (
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-yellow-500 mr-1" />
            <span>Pending</span>
          </div>
        );
      case "failed":
        return (
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-500 mr-1" />
            <span>Failed</span>
          </div>
        );
      case "cancelled":
        return (
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-gray-500 mr-1" />
            <span>Cancelled</span>
          </div>
        );
      case "expired":
        return (
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-gray-500 mr-1" />
            <span>Expired</span>
          </div>
        );
      case "partially_paid":
        return (
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-blue-500 mr-1" />
            <span>Partially Paid</span>
          </div>
        );
      case "refunded":
        return (
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-purple-500 mr-1" />
            <span>Refunded</span>
          </div>
        );
      case "partially_refunded":
        return (
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-purple-500 mr-1" />
            <span>Partially Refunded</span>
          </div>
        );
      default:
        return <span className="capitalize">{status.replace("_", " ")}</span>;
    }
  };

  // Calculate next billing date
  const getNextBillingDate = (subscription: Subscription) => {
    if (!subscription.startDate) return "N/A";

    const startDate = new Date(subscription.startDate);
    let months = 1;

    switch (subscription.planId.interval) {
      case "monthly":
        months = 1;
        break;
      case "quarterly":
        months = 3;
        break;
      case "yearly":
        months = 12;
        break;
    }

    if (subscription.endDate) {
      const endDate = new Date(subscription.endDate);
      if (endDate <= new Date()) {
        return "Subscription ended";
      }
    }

    // If there's a last invoice with a paid date, calculate from there
    if (subscription.lastInvoiceId && subscription.lastInvoiceId.paidDate) {
      const paidDate = new Date(subscription.lastInvoiceId.paidDate);
      return format(addMonths(paidDate, months), "dd MMM yyyy");
    }

    // Otherwise calculate from start date
    return format(addMonths(startDate, months), "dd MMM yyyy");
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  if (!subscriptionData || !subscriptionData.subscription) {
    return (
      <div className="container mx-auto py-10">
        <Card>
          <CardHeader>
            <CardTitle>Subscription</CardTitle>
          </CardHeader>
          <CardContent className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Active Subscription</h3>
            <p className="text-muted-foreground mb-4">
              You don&apos;t have an active subscription. Please contact the
              super admin to set up your subscription.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { subscription, invoices } = subscriptionData;

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Subscription Management</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Subscription Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Status</span>
              {getStatusBadge(subscription.status)}
            </div>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-muted-foreground">Plan</span>
              <span className="font-medium">{subscription.planId.name}</span>
            </div>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-muted-foreground">Outlets</span>
              <span className="font-medium">{subscription.outletCount}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Billing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Amount</span>
              <span className="font-medium">
                {Indian(subscription.totalAmount)}
              </span>
            </div>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-muted-foreground">Billing Cycle</span>
              <span className="font-medium capitalize">
                {subscription.planId.interval}
              </span>
            </div>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-muted-foreground">Auto-renew</span>
              <span className="font-medium">
                {subscription.autoRenew ? "Yes" : "No"}
              </span>
            </div>
            {subscription.isExemptedFromNextPayment && (
              <div className="mt-4 p-2 bg-yellow-50 rounded-md">
                <p className="text-sm text-yellow-800 font-medium">
                  Exempted from next payment
                </p>
                {subscription.exemptionReason && (
                  <p className="text-xs text-yellow-700 mt-1">
                    {subscription.exemptionReason}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Dates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Start Date</span>
              <span className="font-medium">
                {subscription.startDate
                  ? format(new Date(subscription.startDate), "dd MMM yyyy")
                  : "N/A"}
              </span>
            </div>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-muted-foreground">End Date</span>
              <span className="font-medium">
                {subscription.endDate
                  ? format(new Date(subscription.endDate), "dd MMM yyyy")
                  : "N/A"}
              </span>
            </div>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-muted-foreground">Next Billing</span>
              <span className="font-medium">
                {getNextBillingDate(subscription)}
              </span>
            </div>
            {subscription.trialEndDate && (
              <div className="mt-2 flex items-center justify-between">
                <span className="text-muted-foreground">Trial Ends</span>
                <span className="font-medium">
                  {format(new Date(subscription.trialEndDate), "dd MMM yyyy")}
                </span>
              </div>
            )}
            <div className="mt-4 p-2 bg-blue-50 rounded-md">
              <p className="text-sm text-blue-800">
                Billing is prorated based on usage. You will be billed at the
                beginning of each month for the previous month&apos;s usage.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Plan Details */}
      <Card className="mb-8">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Plan Details</CardTitle>
          {availablePlans.length > 0 && (
            <Button onClick={() => setShowUpgradeDialog(true)}>
              Change Plan
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between">
            <div>
              <h3 className="text-xl font-bold mb-2">
                {subscription.planId.name}
              </h3>
              <p className="text-2xl font-bold text-primary mb-4">
                {Indian(subscription.planId.price)}
                <span className="text-sm font-normal text-muted-foreground">
                  /{subscription.planId.interval}
                </span>
              </p>
            </div>

            {subscription.planId.features &&
              subscription.planId.features.length > 0 && (
                <div className="mt-4 md:mt-0">
                  <h4 className="font-medium mb-2">Features</h4>
                  <ul className="space-y-1">
                    {subscription.planId.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <span className="mr-2">•</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
          </div>
        </CardContent>
      </Card>

      {/* Invoices */}
      <Card>
        <CardHeader>
          <CardTitle>Invoices</CardTitle>
        </CardHeader>
        <CardContent>
          {invoices.length === 0 ? (
            <p className="text-center py-4 text-muted-foreground">
              No invoices found
            </p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice #</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice._id}>
                    <TableCell>{invoice.invoiceNumber}</TableCell>
                    <TableCell>
                      {format(new Date(invoice.createdAt), "dd MMM yyyy")}
                    </TableCell>
                    <TableCell>{Indian(invoice.amount)}</TableCell>
                    <TableCell>
                      {getInvoiceStatusBadge(invoice.status)}
                    </TableCell>
                    <TableCell>
                      {format(new Date(invoice.dueDate), "dd MMM yyyy")}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewInvoice(invoice._id)}
                        >
                          View
                        </Button>
                        {invoice.status === "created" &&
                          invoice.paymentLink && (
                            <Button
                              size="sm"
                              onClick={() =>
                                window.open(invoice.paymentLink, "_blank")
                              }
                            >
                              Pay Now
                            </Button>
                          )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Invoice Dialog */}
      <Dialog open={showInvoiceDialog} onOpenChange={setShowInvoiceDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Invoice Details</DialogTitle>
            <DialogDescription>
              Invoice #{selectedInvoice?.invoiceNumber}
            </DialogDescription>
          </DialogHeader>

          {selectedInvoice && (
            <div className="space-y-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-bold">
                    Invoice #{selectedInvoice.invoiceNumber}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Created:{" "}
                    {format(new Date(selectedInvoice.createdAt), "dd MMM yyyy")}
                  </p>
                </div>
                <div>{getInvoiceStatusBadge(selectedInvoice.status)}</div>
              </div>

              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Invoice Items</h4>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Quantity</TableHead>
                      <TableHead className="text-right">Unit Price</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedInvoice.items?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.description}</TableCell>
                        <TableCell className="text-right">
                          {item.quantity}
                        </TableCell>
                        <TableCell className="text-right">
                          {Indian(item.unitPrice)}
                        </TableCell>
                        <TableCell className="text-right">
                          {Indian(item.amount)}
                        </TableCell>
                      </TableRow>
                    )) || (
                      <TableRow>
                        <TableCell>
                          {subscription.planId.name} -{" "}
                          {subscription.planId.interval} subscription
                        </TableCell>
                        <TableCell className="text-right">
                          {subscription.outletCount}
                        </TableCell>
                        <TableCell className="text-right">
                          {Indian(subscription.planId.price)}
                        </TableCell>
                        <TableCell className="text-right">
                          {Indian(selectedInvoice.amount)}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>

              <div className="flex justify-between items-center border-t pt-4">
                <div>
                  <p className="text-sm text-muted-foreground">
                    Due Date:{" "}
                    {format(new Date(selectedInvoice.dueDate), "dd MMM yyyy")}
                  </p>
                  {selectedInvoice.paidDate && (
                    <p className="text-sm text-green-600">
                      Paid on:{" "}
                      {format(
                        new Date(selectedInvoice.paidDate),
                        "dd MMM yyyy"
                      )}
                    </p>
                  )}
                </div>
                <div className="text-xl font-bold">
                  Total: {Indian(selectedInvoice.amount)}
                </div>
              </div>

              {/* Payment History */}
              {selectedInvoice.paymentHistory &&
                selectedInvoice.paymentHistory.length > 0 && (
                  <div className="mt-4 border-t pt-4">
                    <h4 className="font-medium mb-2">Payment History</h4>
                    <div className="bg-gray-50 rounded-md p-3 max-h-40 overflow-y-auto">
                      {selectedInvoice.paymentHistory.map((item, index) => (
                        <div
                          key={index}
                          className="mb-2 pb-2 border-b border-gray-200 last:border-0 last:mb-0 last:pb-0"
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <span
                                className={`inline-block px-2 py-1 text-xs rounded-full mr-2 ${
                                  item.status === "success"
                                    ? "bg-green-100 text-green-800"
                                    : item.status === "failed"
                                    ? "bg-red-100 text-red-800"
                                    : item.status === "partial"
                                    ? "bg-blue-100 text-blue-800"
                                    : item.status === "refunded"
                                    ? "bg-purple-100 text-purple-800"
                                    : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {item.status === "success"
                                  ? "Success"
                                  : item.status === "failed"
                                  ? "Failed"
                                  : item.status === "partial"
                                  ? "Partial"
                                  : item.status === "refunded"
                                  ? "Refunded"
                                  : item.status.charAt(0).toUpperCase() +
                                    item.status.slice(1)}
                              </span>
                              <span className="text-sm">{item.details}</span>
                            </div>
                            <span className="text-xs text-gray-500">
                              {format(
                                new Date(item.timestamp),
                                "dd MMM yyyy HH:mm"
                              )}
                            </span>
                          </div>
                          {item.amount && (
                            <div className="mt-1 text-sm">
                              Amount: {Indian(item.amount)}
                              {item.method && (
                                <span className="ml-2">via {item.method}</span>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

              {selectedInvoice.status === "created" &&
                selectedInvoice.paymentLink && (
                  <div className="flex justify-end">
                    <Button
                      onClick={() =>
                        window.open(selectedInvoice.paymentLink, "_blank")
                      }
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Pay Now
                    </Button>
                  </div>
                )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Change Plan Dialog */}
      <Dialog open={showUpgradeDialog} onOpenChange={setShowUpgradeDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Change Subscription Plan</DialogTitle>
            <DialogDescription>
              {localStorage.getItem("superAdminToken") !== null ? (
                <>
                  As a super admin, you can upgrade or downgrade this
                  subscription to any available plan.
                </>
              ) : (
                <>
                  As an admin, you can only upgrade to higher-priced plans.
                  Please contact support if you need to downgrade your
                  subscription.
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="plan">Select New Plan</Label>
                <select
                  id="plan"
                  className="w-full p-2 border rounded-md"
                  value={selectedPlan}
                  onChange={(e) => setSelectedPlan(e.target.value)}
                >
                  <option value="">Select a plan</option>
                  {availablePlans.map((plan) => (
                    <option key={plan._id} value={plan._id}>
                      {plan.name} - {Indian(plan.price)}/{plan.interval}
                    </option>
                  ))}
                </select>
              </div>

              {selectedPlan && (
                <div className="bg-yellow-50 p-4 rounded-md">
                  <p className="text-sm text-yellow-800">
                    Your plan will be changed immediately. You will be billed
                    for the prorated amount at the beginning of next month based
                    on the number of days each plan was used.
                  </p>
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowUpgradeDialog(false);
                setSelectedPlan("");
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              disabled={!selectedPlan || isUpgrading}
              onClick={async () => {
                if (!selectedPlan) return;

                setIsUpgrading(true);
                try {
                  const response = await upgradeSubscriptionPlan(
                    subscription._id,
                    selectedPlan
                  );

                  if (response.success) {
                    toast.success(
                      response.message ||
                        "Subscription plan upgraded successfully"
                    );
                    setShowUpgradeDialog(false);
                    setSelectedPlan("");
                    fetchSubscriptionData();
                  } else {
                    toast.error(
                      response.message || "Failed to upgrade subscription plan"
                    );
                  }
                } catch (error) {
                  console.error("Error upgrading subscription plan:", error);
                  toast.error("Failed to upgrade subscription plan");
                } finally {
                  setIsUpgrading(false);
                }
              }}
            >
              {isUpgrading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Change Plan"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
