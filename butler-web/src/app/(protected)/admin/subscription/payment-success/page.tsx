"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { verifySubscriptionPayment } from "@/server/subscription";
import { toast } from "sonner";
import { CheckCircle, Loader2, AlertCircle } from "lucide-react";

// Component that uses searchParams
function PaymentVerification() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isVerifying, setIsVerifying] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        if (!searchParams) {
          setErrorMessage("No payment parameters found");
          setIsSuccess(false);
          setIsVerifying(false);
          return;
        }

        // Check for both payment link and invoice parameters
        const razorpay_payment_id = searchParams.get("razorpay_payment_id");

        // Handle both payment link and invoice callbacks
        const razorpay_payment_link_id =
          searchParams.get("razorpay_payment_link_id") ||
          searchParams.get("razorpay_invoice_id");

        const razorpay_signature = searchParams.get("razorpay_signature");

        if (
          !razorpay_payment_id ||
          !razorpay_payment_link_id ||
          !razorpay_signature
        ) {
          // Check if we have order_id and invoice_id (alternative Razorpay invoice flow)
          const razorpay_order_id = searchParams.get("razorpay_order_id");
          const razorpay_invoice_id = searchParams.get("razorpay_invoice_id");

          if (razorpay_order_id && razorpay_invoice_id && razorpay_signature) {
            // Use these parameters instead
            const response = await verifySubscriptionPayment({
              razorpay_payment_id: razorpay_order_id, // Use order_id as payment_id
              razorpay_payment_link_id: razorpay_invoice_id,
              razorpay_signature,
            });

            if (response.success) {
              setIsSuccess(true);
              toast.success("Payment verified successfully");
            } else {
              setErrorMessage(
                response.message || "Payment verification failed"
              );
              setIsSuccess(false);
            }
            setIsVerifying(false);
            return;
          }

          setErrorMessage("Missing payment parameters");
          setIsSuccess(false);
          setIsVerifying(false);
          return;
        }

        const response = await verifySubscriptionPayment({
          razorpay_payment_id,
          razorpay_payment_link_id,
          razorpay_signature,
        });

        if (response.success) {
          setIsSuccess(true);
          toast.success("Payment verified successfully");
        } else {
          setErrorMessage(response.message || "Payment verification failed");
          setIsSuccess(false);
        }
      } catch (error) {
        console.error("Error verifying payment:", error);
        setErrorMessage("An error occurred while verifying payment");
        setIsSuccess(false);
      } finally {
        setIsVerifying(false);
      }
    };

    verifyPayment();
  }, [searchParams]);

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center">Subscription Payment</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          {isVerifying ? (
            <div className="py-8 flex flex-col items-center">
              <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
              <p>Verifying your payment...</p>
            </div>
          ) : isSuccess ? (
            <div className="py-8 flex flex-col items-center">
              <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
              <h3 className="text-xl font-bold mb-2">Payment Successful!</h3>
              <p className="text-muted-foreground mb-6">
                Your subscription payment has been processed successfully.
              </p>
              <Button onClick={() => router.push("/admin/subscription")}>
                View Subscription
              </Button>
            </div>
          ) : (
            <div className="py-8 flex flex-col items-center">
              <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-xl font-bold mb-2">
                Payment Verification Failed
              </h3>
              <p className="text-muted-foreground mb-2">
                {errorMessage || "There was an issue verifying your payment."}
              </p>
              <p className="text-sm text-muted-foreground mb-6">
                Please contact support if you believe this is an error.
              </p>
              <div className="space-x-4">
                <Button
                  variant="outline"
                  onClick={() => router.push("/admin/subscription")}
                >
                  View Subscription
                </Button>
                <Button onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Loading fallback for Suspense
function PaymentLoadingFallback() {
  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center">Subscription Payment</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <div className="py-8 flex flex-col items-center">
            <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
            <p>Loading payment details...</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Main page component with Suspense boundary
export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<PaymentLoadingFallback />}>
      <PaymentVerification />
    </Suspense>
  );
}
