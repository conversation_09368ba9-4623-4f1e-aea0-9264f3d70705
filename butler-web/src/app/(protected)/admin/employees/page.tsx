"use client";
import {
  getAllEmployees,
  getAllOutlets,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  resetEmployeePassword,
} from "@/server/admin";
import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import {
  Search,
  UserPlus,
  ShieldAlert,
  ShieldCheck,
  Loader2,
  Pencil,
  Trash2,
  Key,
  Building2,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Outlet } from "@/app/type";
import EmployeeFormDialog from "@/components/custom/employees/EmployeeFormDialog";
import ConfirmDialog from "@/components/custom/ConfirmDialog";
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

interface Employee {
  _id: string;
  name: string;
  phone?: string;
  email: string;
  role: string;
  status?: "active" | "inactive";
  createdAt?: string;
  outlets?: { _id: string; name: string }[];
  hasPortalAccess?: boolean;
  salary?: number;
  address?: string;
  emergencyContact?: {
    name?: string;
    phone?: string;
    relation?: string;
  };
}

const Employees = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [showFormDialog, setShowFormDialog] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showResetPasswordConfirm, setShowResetPasswordConfirm] =
    useState(false);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalEmployees, setTotalEmployees] = useState(0);
  const [limit, setLimit] = useState(20);
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [newPassword, setNewPassword] = useState<string | null>(null);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch employees when page, limit, or search changes
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await getAllEmployees(
          currentPage,
          limit,
          debouncedSearch
        );
        console.log("Employee response:", response);

        if (response.success) {
          // Handle the response format
          if (response.data && response.data.employees) {
            setEmployees(response.data.employees);
            if (response.data.pagination) {
              setTotalPages(response.data.pagination.pages);
              setTotalEmployees(response.data.pagination.total);
            }
          } else if (Array.isArray(response.data)) {
            // Handle old format for backward compatibility
            setEmployees(response.data);
            if (response.pagination) {
              setTotalPages(response.pagination.pages);
              setTotalEmployees(response.pagination.total);
            }
          } else {
            console.error("Unexpected data format:", response.data);
            setEmployees([]);
          }
        } else {
          toast.error(response.message || "Failed to fetch employees");
        }
      } catch (error) {
        console.error("Error fetching employees:", error);
        toast.error("An error occurred while fetching employees");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentPage, limit, debouncedSearch]);

  // Fetch outlets for employee assignment
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await getAllOutlets();
        if (response.success) {
          setOutlets(response.data);
        } else {
          toast.error("Failed to load outlets");
        }
      } catch (error) {
        console.error("Error fetching outlets:", error);
      }
    };

    fetchOutlets();
  }, []);

  const handleCreateEmployee = () => {
    setSelectedEmployee(null);
    setIsCreating(true);
    setShowFormDialog(true);
  };

  const handleEditEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsCreating(false);
    setShowFormDialog(true);
  };

  const handleDeleteEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setShowDeleteConfirm(true);
  };

  const handleResetPassword = (employee: Employee) => {
    setSelectedEmployee(employee);
    setShowResetPasswordConfirm(true);
  };

  const confirmDelete = async () => {
    if (!selectedEmployee) return;

    try {
      setProcessingId(selectedEmployee._id);
      const response = await deleteEmployee(selectedEmployee._id);

      if (response.success) {
        toast.success("Employee deleted successfully");
        // Remove from local state
        setEmployees(
          employees.filter((emp) => emp._id !== selectedEmployee._id)
        );
        setShowDeleteConfirm(false);
      } else {
        toast.error(response.message || "Failed to delete employee");
      }
    } catch (error) {
      console.error("Error deleting employee:", error);
      toast.error("An error occurred while deleting employee");
    } finally {
      setProcessingId(null);
    }
  };

  const confirmResetPassword = async () => {
    if (!selectedEmployee) return;

    try {
      setProcessingId(selectedEmployee._id);
      const response = await resetEmployeePassword(selectedEmployee._id);

      if (response.success) {
        setNewPassword(response.data.newPassword);
        toast.success("Password reset successfully");
      } else {
        toast.error(response.message || "Failed to reset password");
      }
    } catch (error) {
      console.error("Error resetting password:", error);
      toast.error("An error occurred while resetting password");
    } finally {
      setProcessingId(null);
      setShowResetPasswordConfirm(false);
    }
  };

  const handleFormSubmit = async (data: {
    name: string;
    email: string;
    phone?: string;
    role: string;
    status?: "active" | "inactive";
    outletIds: string[];
    hasPortalAccess?: boolean;
    salary?: number;
    address?: string;
    emergencyContact?: {
      name?: string;
      phone?: string;
      relation?: string;
    };
  }) => {
    try {
      let response;

      if (isCreating) {
        response = await createEmployee(data);
        if (response.success) {
          toast.success("Employee created successfully");
          // Add to local state or refresh
          refreshEmployees();

          // Show initial password
          if (response.data.initialPassword) {
            setNewPassword(response.data.initialPassword);
          }
        } else {
          console.log(response);
          toast.error(response.message || "Failed to create employee");
        }
      } else if (selectedEmployee) {
        response = await updateEmployee(selectedEmployee._id, data);
        if (response.success) {
          toast.success("Employee updated successfully");
          // Update in local state or refresh
          refreshEmployees();
        } else {
          toast.error(response.message || "Failed to update employee");
        }
      }

      setShowFormDialog(false);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("An error occurred while saving employee");
    }
  };

  // Function to handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Function to handle limit change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setCurrentPage(1); // Reset to first page when changing limit
  };

  // Function to refresh employees
  const refreshEmployees = () => {
    // This will trigger the useEffect that fetches employees
    const currentTimestamp = new Date().getTime();
    setDebouncedSearch(debouncedSearch + `?refresh=${currentTimestamp}`);
    setTimeout(() => setDebouncedSearch(debouncedSearch), 100);
  };

  const filteredEmployees = employees.filter(
    (employee) =>
      employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (employee.phone && employee.phone.includes(searchTerm)) ||
      employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getRoleBadgeColor = (role: string) => {
    const colors: Record<string, string> = {
      admin: "bg-purple-100 text-purple-800",
      manager: "bg-blue-100 text-blue-800",
      chef: "bg-green-100 text-green-800",
      delivery: "bg-yellow-100 text-yellow-800",
      cashier: "bg-orange-100 text-orange-800",
      waiter: "bg-pink-100 text-pink-800",
    };
    return colors[role] || "bg-gray-100 text-gray-800";
  };

  return (
    <div className="p-4">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Employees</h1>
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-500">
              {totalEmployees > 0 && `Total: ${totalEmployees} employees`}
            </div>
            <Button
              onClick={handleCreateEmployee}
              className="bg-primary text-white hover:bg-primary/90"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              <div className="hidden md:block">Add Employee</div>
            </Button>
          </div>
        </div>
        <div className="relative w-full max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search employees..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {loading ? (
        <div className="text-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="mt-2">Loading employees...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredEmployees.length === 0 ? (
            <div className="col-span-full text-center py-10">
              <p className="text-gray-500">No employees found</p>
            </div>
          ) : (
            filteredEmployees.map((employee) => (
              <Card key={employee._id} className="overflow-hidden">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{employee.name}</CardTitle>
                      <CardDescription>{employee.email}</CardDescription>
                    </div>
                    <div className="flex flex-col gap-1 items-end">
                      <Badge className={getRoleBadgeColor(employee.role)}>
                        {employee.role.toUpperCase()}
                      </Badge>
                      {employee.status && (
                        <Badge
                          className={
                            employee.status === "active"
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }
                        >
                          {employee.status === "active" ? (
                            <ShieldCheck className="h-3 w-3 mr-1" />
                          ) : (
                            <ShieldAlert className="h-3 w-3 mr-1" />
                          )}
                          <div className="hidden md:block">
                            {employee.status.toUpperCase()}
                          </div>
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Phone</span>
                      <span>{employee.phone || "Not provided"}</span>
                    </div>

                    {employee.createdAt && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Joined</span>
                        <span>
                          {format(new Date(employee.createdAt), "PP")}
                        </span>
                      </div>
                    )}

                    {employee.outlets && employee.outlets.length > 0 && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Outlets</span>
                        <div className="flex flex-wrap justify-end gap-1">
                          {employee.outlets.map((outlet) => (
                            <Badge
                              key={outlet._id}
                              variant="outline"
                              className="flex items-center"
                            >
                              <Building2 className="h-3 w-3 mr-1" />
                              {outlet.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="mt-4 pt-4 border-t flex justify-between">
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditEmployee(employee)}
                      >
                        <Pencil className="h-4 w-4 mr-1" />
                        <div className="hidden md:block"> Edit</div>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleResetPassword(employee)}
                        disabled={processingId === employee._id}
                      >
                        {processingId === employee._id ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-1" />
                        ) : (
                          <Key className="h-4 w-4 mr-1" />
                        )}
                        <div className="hidden md:block">Reset Password</div>
                      </Button>
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteEmployee(employee)}
                      disabled={processingId === employee._id}
                    >
                      {processingId === employee._id ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : (
                        <Trash2 className="h-4 w-4 mr-1" />
                      )}
                      <div className="hidden md:block">Delete</div>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}

      {/* Pagination */}
      {!loading && totalPages > 1 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing {filteredEmployees.length} of {totalEmployees} employees
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Show pages around current page
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    className="w-8 h-8 p-0"
                    onClick={() => handlePageChange(pageNum)}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Show</span>
            <select
              className="border rounded p-1 text-sm"
              value={limit}
              onChange={(e) => handleLimitChange(Number(e.target.value))}
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm text-gray-500">per page</span>
          </div>
        </div>
      )}

      {/* Employee Form Dialog */}
      <EmployeeFormDialog
        open={showFormDialog}
        onOpenChange={setShowFormDialog}
        employee={selectedEmployee}
        outlets={outlets}
        onSubmit={handleFormSubmit}
        isCreating={isCreating}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Delete Employee"
        description={`Are you sure you want to delete ${selectedEmployee?.name}? This action cannot be undone.`}
        onConfirm={confirmDelete}
        loading={!!processingId}
      />

      {/* Reset Password Confirmation Dialog */}
      <ConfirmDialog
        open={showResetPasswordConfirm}
        onOpenChange={setShowResetPasswordConfirm}
        title="Reset Password"
        description={`Are you sure you want to reset the password for ${selectedEmployee?.name}?`}
        onConfirm={confirmResetPassword}
        loading={!!processingId}
      />

      {/* New Password Dialog */}
      {newPassword && (
        <Dialog open={!!newPassword} onOpenChange={() => setNewPassword(null)}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>New Password Generated</DialogTitle>
              <DialogDescription>
                Please save this password. It will not be shown again.
              </DialogDescription>
            </DialogHeader>
            <div className="p-4 bg-gray-100 rounded-md text-center">
              <p className="text-lg font-mono">{newPassword}</p>
            </div>
            <DialogFooter>
              <Button onClick={() => setNewPassword(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default Employees;
