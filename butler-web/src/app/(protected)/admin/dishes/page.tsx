"use client";

import { Dish } from "@/app/type";
import CreateDishesDrawer from "@/components/custom/dishes/CreateDishesDrawer";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "@/contexts/ThemeContext";
import {
  getAllDishes,
  deleteDish,
  updateDish,
  toggleDishFeaturedStatus,
} from "@/server/admin";
import React, { Suspense, useEffect, useState } from "react";
import {
  Edit,
  Trash2,
  Menu,
  IndianRupee,
  Clock,
  Calendar,
  Utensils,
  ChevronLeft,
  Star,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRouter, useSearchParams } from "next/navigation";
import { Switch } from "@/components/ui/switch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import DishOutletManagement from "@/components/custom/dishes/DishOutletManagement";
import DishAvailabilitySettings from "@/components/custom/dishes/DishAvailabilitySettings";
import DishIngredientsDialog from "@/components/custom/dishes/DishIngredientsDialog";

const AdminDishesPage = () => {
  const { theme } = useTheme();
  const router = useRouter();
  const searchParams = useSearchParams();
  const outletId = searchParams?.get("outletId");

  const [loading, setLoading] = useState<boolean>(false);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [activeTab, setActiveTab] = useState<string>("all");
  const [dishToEdit, setDishToEdit] = useState<Dish | false>(false);
  const [dishToDelete, setDishToDelete] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);
  const [showOutletManagement, setShowOutletManagement] =
    useState<boolean>(false);
  const [showAvailabilitySettings, setShowAvailabilitySettings] =
    useState<boolean>(false);
  const [showIngredientsDialog, setShowIngredientsDialog] =
    useState<boolean>(false);
  const [selectedDish, setSelectedDish] = useState<Dish | null>(null);
  const [isUpdating, setIsUpdating] = useState<string | null>(null);

  const getDishesFunction = async () => {
    setLoading(true);
    try {
      const response = await getAllDishes();
      setDishes(response.data);
    } catch (error) {
      console.error("Failed to fetch dishes:", error);
    } finally {
      setLoading(false);
    }
  };
  console.log(showIngredientsDialog);

  useEffect(() => {
    getDishesFunction();
  }, []);

  const handleDeleteDish = async () => {
    if (!dishToDelete) return;

    try {
      const response = await deleteDish(dishToDelete);
      if (response.success) {
        toast.success("Dish deleted successfully");
        getDishesFunction(); // Refresh the dishes list
      } else {
        toast.error(response.message || "Failed to delete dish");
      }
    } catch (error) {
      console.error("Error deleting dish:", error);
      toast.error("An error occurred while deleting the dish");
    } finally {
      setDishToDelete(null);
      setShowDeleteDialog(false);
    }
  };

  const handleToggleAvailability = async (dish: Dish) => {
    if (!dish._id) return;

    setIsUpdating(dish._id);
    try {
      const updatedDish = {
        ...dish,
        isAvailable: !dish.isAvailable,
      };

      const response = await updateDish(updatedDish);
      if (response.success) {
        toast.success(
          `Dish ${
            updatedDish.isAvailable ? "enabled" : "disabled"
          } successfully`
        );
        // Update the dish in the local state
        setDishes(
          dishes.map((d) =>
            d._id === dish._id ? { ...d, isAvailable: !dish.isAvailable } : d
          )
        );
      } else {
        toast.error(response.message || "Failed to update dish availability");
      }
    } catch (error) {
      console.error("Error updating dish availability:", error);
      toast.error("An error occurred while updating dish availability");
    } finally {
      setIsUpdating(null);
    }
  };

  const handleToggleFeatured = async (dish: Dish) => {
    if (!dish._id) return;

    setIsUpdating(dish._id);
    try {
      const response = await toggleDishFeaturedStatus(
        dish._id,
        !dish.isFeatured
      );
      if (response.success) {
        toast.success(
          `Dish ${
            !dish.isFeatured ? "marked as featured" : "removed from featured"
          } successfully`
        );
        // Update the dish in the local state
        setDishes(
          dishes.map((d) =>
            d._id === dish._id ? { ...d, isFeatured: !dish.isFeatured } : d
          )
        );
      } else {
        toast.error(
          response.message || "Failed to update dish featured status"
        );
      }
    } catch (error) {
      console.error("Error updating dish featured status:", error);
      toast.error("An error occurred while updating dish featured status");
    } finally {
      setIsUpdating(null);
    }
  };

  const handleManageOutlets = (dish: Dish) => {
    setSelectedDish(dish);
    setShowOutletManagement(true);
    setShowAvailabilitySettings(false);
    setShowIngredientsDialog(false);
  };

  const handleManageAvailability = (dish: Dish) => {
    setSelectedDish(dish);
    setShowAvailabilitySettings(true);
    setShowOutletManagement(false);
    setShowIngredientsDialog(false);
  };

  const handleManageIngredients = (dish: Dish) => {
    setSelectedDish(dish);
    setShowIngredientsDialog(true);
    setShowOutletManagement(false);
    setShowAvailabilitySettings(false);
  };

  // Get unique categories for tabs
  const categories = React.useMemo(() => {
    const uniqueCategories = new Set<string>();
    dishes.forEach((dish) => {
      if (typeof dish.category === "object" && dish.category?.name) {
        uniqueCategories.add(dish.category.name);
      }
    });
    return Array.from(uniqueCategories);
  }, [dishes]);

  // Filter dishes based on active tab
  const filteredDishes = React.useMemo(() => {
    if (activeTab === "all") return dishes;
    return dishes.filter(
      (dish) =>
        typeof dish.category === "object" && dish.category?.name === activeTab
    );
  }, [dishes, activeTab]);

  return (
    <div className="container mx-auto p-4">
      {outletId && (
        <Button
          variant="outline"
          className="mb-3"
          onClick={() => router.back()}
        >
          <ChevronLeft className="h-4 w-4 mr-1" /> Back to Outlet
        </Button>
      )}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">
          {outletId ? "Add Dishes to Outlet" : "Dishes"}
        </h1>
      </div>

      <Separator className="my-4" />
      <CreateDishesDrawer
        onSuccess={() => {
          getDishesFunction();
          setDishToEdit(false);
          if (outletId) {
            router.back();
          }
        }}
        isUpdate={dishToEdit ? true : false}
        dishToEdit={dishToEdit != false ? dishToEdit : undefined}
        onClose={() => {
          setDishToEdit(false);
        }}
        preselectedOutletId={outletId || undefined}
      />
      <Tabs defaultValue="all" className="my-6" onValueChange={setActiveTab}>
        <TabsList className="mb-4 flex flex-wrap h-auto">
          <TabsTrigger
            value="all"
            // className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            All Dishes
          </TabsTrigger>
          {categories?.map((category) => (
            <TabsTrigger
              key={category}
              value={category}
              // className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              {category}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="mt-0">
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6]?.map((i) => (
                <Card key={i} className="overflow-hidden">
                  <CardHeader className="p-4 pb-2">
                    <div className="flex justify-between">
                      <Skeleton className="h-6 w-32" />
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                  <CardFooter className="p-4 flex justify-between">
                    <Skeleton className="h-5 w-20" />
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-8 rounded-md" />
                      <Skeleton className="h-8 w-8 rounded-md" />
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : filteredDishes?.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 bg-muted/30 rounded-lg">
              <Menu className="h-12 w-12 text-muted-foreground mb-2" />
              <h3 className="text-xl font-medium">No Dishes Found</h3>
              <p className="text-muted-foreground mb-4">
                {activeTab === "all"
                  ? "Get started by adding your first dish"
                  : `No dishes in the ${activeTab} category`}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filteredDishes?.map((dish, index) => (
                <Card
                  key={index}
                  className={`overflow-hidden relative h-full flex flex-col justify-between ${
                    !dish.isAvailable ? "opacity-70" : ""
                  }`}
                >
                  <div className="absolute top-2 right-2 z-10 flex flex-col gap-1">
                    {dish.isFeatured && (
                      <Badge
                        variant="outline"
                        className="bg-yellow-100 text-yellow-800 border-yellow-300"
                      >
                        <Star className="h-3 w-3 mr-1 fill-current" />
                        Featured
                      </Badge>
                    )}
                    {dish.isSeasonal && (
                      <Badge
                        variant="outline"
                        className="bg-amber-100 text-amber-800 border-amber-300"
                      >
                        <Calendar className="h-3 w-3 mr-1" />
                        Seasonal
                      </Badge>
                    )}
                  </div>

                  <CardHeader
                    className="p-4 pb-2"
                    style={{ backgroundColor: `${theme.primaryColor}10` }}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex flex-col">
                        <h3
                          className="text-lg font-semibold truncate"
                          title={dish.name}
                        >
                          {dish.name}
                        </h3>
                        <div className="flex flex-col space-y-2 mt-1">
                          <div className="flex items-center">
                            <Switch
                              checked={dish.isAvailable}
                              onCheckedChange={() =>
                                handleToggleAvailability(dish)
                              }
                              disabled={isUpdating === dish._id}
                              className="mr-2"
                            />
                            <span className="text-xs text-gray-500">
                              {dish.isAvailable ? "Available" : "Unavailable"}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <Switch
                              checked={dish.isFeatured || false}
                              onCheckedChange={() => handleToggleFeatured(dish)}
                              disabled={isUpdating === dish._id}
                              className="mr-2"
                            />
                            <Star
                              className={`h-3 w-3 mr-1 ${
                                dish.isFeatured
                                  ? "text-yellow-500 fill-current"
                                  : "text-gray-400"
                              }`}
                            />
                            <span className="text-xs text-gray-500">
                              {dish.isFeatured ? "Featured" : "Regular"}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center font-semibold">
                        <IndianRupee className="h-4 w-4 mr-1" />
                        {dish.price}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="p-4">
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {dish.description || "No description available"}
                    </p>

                    {dish.timeAvailability && (
                      <div className="mt-2 flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>
                          {dish.timeAvailability.customHours
                            ? `${dish.timeAvailability.startTime} - ${dish.timeAvailability.endTime}`
                            : [
                                dish.timeAvailability.breakfast && "Breakfast",
                                dish.timeAvailability.lunch && "Lunch",
                                dish.timeAvailability.dinner && "Dinner",
                              ]
                                .filter(Boolean)
                                .join(", ") || "All day"}
                        </span>
                      </div>
                    )}
                  </CardContent>

                  <CardFooter className="p-4 pt-0 flex-col">
                    <div className="flex justify-between items-center w-full mb-2">
                      <Badge
                        style={{
                          backgroundColor: theme.primaryColor,
                          color: "white",
                        }}
                      >
                        {typeof dish?.category === "object" &&
                          dish?.category?.name}
                      </Badge>

                      <Badge
                        variant="outline"
                        className={
                          dish.isVeg
                            ? "border-green-500 text-green-600"
                            : "border-red-500 text-red-600"
                        }
                      >
                        {dish.isVeg ? "Veg" : "Non-Veg"}
                      </Badge>
                    </div>

                    <div className="flex justify-between w-full mt-2 pt-2 border-t">
                      <div className="flex gap-1 flex-wrap">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8"
                          onClick={() => handleManageOutlets(dish)}
                        >
                          Outlets
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8"
                          onClick={() => handleManageAvailability(dish)}
                        >
                          Availability
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8"
                          onClick={() => handleManageIngredients(dish)}
                        >
                          <Utensils className="h-3 w-3 mr-1" />
                          Ingredients
                        </Button>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => setDishToEdit(dish)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-destructive"
                          onClick={() => {
                            setDishToDelete(dish._id || "");
                            setShowDeleteDialog(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              dish and remove it from all outlets.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDish}
              className="bg-destructive hover:bg-destructive/90 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Outlet Management Dialog */}
      {selectedDish && (
        <DishOutletManagement
          dish={selectedDish}
          open={showOutletManagement}
          onOpenChange={(open) => {
            setShowOutletManagement(open);
            if (!open) {
              setSelectedDish(null);
            }
          }}
          onSuccess={getDishesFunction}
        />
      )}

      {/* Availability Settings Dialog */}
      {selectedDish && (
        <DishAvailabilitySettings
          dish={selectedDish}
          open={showAvailabilitySettings}
          onOpenChange={(open) => {
            setShowAvailabilitySettings(open);
            if (!open) {
              setSelectedDish(null);
            }
          }}
          onSuccess={getDishesFunction}
        />
      )}

      {/* Ingredients Management Dialog */}
      {selectedDish && (
        <DishIngredientsDialog
          open={showIngredientsDialog}
          dish={selectedDish}
          onClose={(refresh) => {
            setShowIngredientsDialog(false);
            setSelectedDish(null);
            if (refresh) getDishesFunction();
          }}
        />
      )}
    </div>
  );
};

const Page = () => {
  return (
    <Suspense>
      <AdminDishesPage />
    </Suspense>
  );
};

export default Page;
