"use client";
import { Outlet } from "@/app/type";
import CreateOutletDrawer from "@/components/custom/outlet/CreateOutletDrawer";
import EditOutletDrawer from "@/components/custom/outlet/EditOutletDrawer";
import {
  <PERSON>,
  CardHeader,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  getAllOutlets,
  deleteOutlet,
  updateOutletStatus,
} from "@/server/admin";
import { useRouter } from "next/navigation";
import { useTheme } from "@/contexts/ThemeContext";
import {
  Edit,
  Eye,
  Trash2,
  Store,
  Clock,
  MapPin,
  Phone,
  AlertCircle,
} from "lucide-react";

import React, { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const AdminOutletPage = () => {
  const router = useRouter();
  const { theme } = useTheme();
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [statusUpdating, setStatusUpdating] = useState<string | null>(null);
  const [outletToEdit, setOutletToEdit] = useState<Outlet | null>(null);
  const [outletToDelete, setOutletToDelete] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);
  const [showEditDrawer, setShowEditDrawer] = useState<boolean>(false);

  const getOutlets = async () => {
    setLoading(true);
    try {
      const data = await getAllOutlets();
      if (data.success) {
        setOutlets(data.data);
      } else {
        toast.error("Failed to fetch outlets");
      }
    } catch (error) {
      console.error("Error fetching outlets:", error);
      toast.error("An error occurred while fetching outlets");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getOutlets();
  }, []);

  const handleStatusToggle = async (outlet: Outlet) => {
    if (!outlet._id) return;

    setStatusUpdating(outlet._id);
    try {
      const newStatus = outlet.status === "active" ? "inactive" : "active";
      const response = await updateOutletStatus(outlet._id, newStatus);

      if (response.success) {
        toast.success(
          `Outlet ${
            newStatus === "active" ? "activated" : "deactivated"
          } successfully`
        );
        // Update the outlet in the local state
        setOutlets(
          outlets.map((o) =>
            o._id === outlet._id ? { ...o, status: newStatus } : o
          )
        );
      } else {
        toast.error(response.message || "Failed to update outlet status");
      }
    } catch (error) {
      console.error("Error updating outlet status:", error);
      toast.error("An error occurred while updating outlet status");
    } finally {
      setStatusUpdating(null);
    }
  };

  const handleEditOutlet = (outlet: Outlet) => {
    setOutletToEdit(outlet);
    setShowEditDrawer(true);
  };

  const handleDeleteOutlet = async () => {
    if (!outletToDelete) return;

    try {
      const response = await deleteOutlet(outletToDelete);
      if (response.success) {
        toast.success("Outlet deleted successfully");
        getOutlets(); // Refresh the outlets list
      } else {
        toast.error(response.message || "Failed to delete outlet");
      }
    } catch (error) {
      console.error("Error deleting outlet:", error);
      toast.error("An error occurred while deleting the outlet");
    } finally {
      setOutletToDelete(null);
      setShowDeleteDialog(false);
    }
  };
  return (
    <div>
      <div className="font-bold text-xl">Outlets</div>
      <Separator className="my-3" />
      <CreateOutletDrawer
        onSuccess={() => {
          getOutlets();
        }}
      />
      {loading && (
        <div className="flex justify-center items-center h-64">
          <div className="text-lg font-medium">Loading...</div>
        </div>
      )}
      <div className="mt-2 sm:grid-cols-3 grid-cols-1 grid gap-4">
        {outlets?.map((outlet, index) => {
          return (
            <Card
              key={index}
              className={`overflow-hidden relative h-full flex flex-col justify-between ${
                outlet.status === "inactive" ? "opacity-60" : ""
              }`}
            >
              {outlet.status === "inactive" && (
                <div className="absolute top-2 right-2 z-10">
                  <Badge
                    variant="outline"
                    className="bg-red-100 text-red-800 border-red-300"
                  >
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Inactive
                  </Badge>
                </div>
              )}

              <CardHeader
                className="p-4 pb-2"
                style={{ backgroundColor: `${theme.primaryColor}10` }}
              >
                <div className="flex justify-between items-center">
                  <div className="flex flex-col">
                    <h3
                      className="text-lg font-semibold truncate"
                      title={outlet.name}
                    >
                      {outlet.name}
                    </h3>
                    <div className="flex items-center mt-1">
                      <Switch
                        checked={outlet.status !== "inactive"}
                        onCheckedChange={() => handleStatusToggle(outlet)}
                        disabled={statusUpdating === outlet._id}
                        className="mr-2"
                      />
                      <span className="text-xs text-gray-500">
                        {outlet.status !== "inactive" ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                  <Badge
                    style={{
                      backgroundColor: theme.primaryColor,
                      color: "white",
                    }}
                  >
                    {outlet.dishes?.length || 0} Dishes
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 mr-2 mt-1 text-gray-500" />
                    <p className="text-sm text-muted-foreground">
                      {outlet.address}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-gray-500" />
                    <p className="text-sm text-muted-foreground">
                      {outlet.contact}
                    </p>
                  </div>
                  {outlet.operatingHours &&
                    outlet.operatingHours.length > 0 && (
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-gray-500" />
                        <p className="text-sm text-muted-foreground">
                          {outlet.operatingHours.find((h) => h.day === "monday")
                            ?.isOpen
                            ? `${
                                outlet.operatingHours.find(
                                  (h) => h.day === "monday"
                                )?.openTime
                              } - ${
                                outlet.operatingHours.find(
                                  (h) => h.day === "monday"
                                )?.closeTime
                              }`
                            : "Closed today"}
                        </p>
                      </div>
                    )}
                </div>
              </CardContent>

              <CardFooter className="p-4 flex justify-between items-center">
                <Badge
                  variant="outline"
                  className={
                    outlet.isCloudKitchen
                      ? "border-blue-500 text-blue-600"
                      : "border-green-500 text-green-600"
                  }
                >
                  <Store className="h-3 w-3 mr-1" />
                  {outlet.isCloudKitchen ? "Cloud Kitchen" : "Physical Outlet"}
                </Badge>

                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => router.push(`/admin/outlets/${outlet._id}`)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleEditOutlet(outlet)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-destructive"
                    onClick={() => {
                      setOutletToDelete(outlet._id || "");
                      setShowDeleteDialog(true);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          );
        })}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              outlet and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteOutlet}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Outlet Drawer */}
      {outletToEdit && (
        <EditOutletDrawer
          outlet={outletToEdit}
          open={showEditDrawer}
          onOpenChange={setShowEditDrawer}
          onSuccess={getOutlets}
        />
      )}
    </div>
  );
};

export default AdminOutletPage;
