"use client";

import { useState, useEffect } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
// BankDetailsForm import removed as it's no longer needed
import FundTransfersList from "@/components/admin/FundTransfersList";
import TransferHistory from "@/components/admin/TransferHistory";
import RazorpaySetup from "@/components/admin/RazorpaySetup";
// UI components removed as they're no longer needed

// BankDetails interface removed as it's no longer needed

interface FoodChain {
  _id: string;
  name: string;
  razorpayAccountId?: string;
  razorpayRouteEnabled?: boolean;
  razorpayAccountStatus?: string;
}

export default function AdminPaymentsPage() {
  const [foodChain, setFoodChain] = useState<FoodChain | null>(null);
  const [loading, setLoading] = useState(true);
  // Fund account state removed as it's no longer needed

  const fetchFoodChainDetails = async () => {
    setLoading(true);
    try {
      // Get the food chain ID from the user's token
      const foodChain = localStorage.getItem("chainId");

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/food-chain/${foodChain}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        setFoodChain(data.data);
      }
    } catch (error) {
      console.error("Error fetching food chain details:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFoodChainDetails();
  }, []);

  // Fund account creation function removed as it's no longer needed

  if (loading) {
    return <div>Loading payment settings...</div>;
  }

  if (!foodChain) {
    return <div>Food chain not found</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Payment Settings</h1>

      <Tabs defaultValue="razorpay-route">
        <TabsList className="mb-4">
          <TabsTrigger value="razorpay-route">Razorpay Route</TabsTrigger>
          <TabsTrigger value="transfers">Transfers History</TabsTrigger>
        </TabsList>

        {/* Bank Details tab removed as it's no longer needed with Razorpay Route */}

        <TabsContent value="transfers">
          <FundTransfersList foodChainId={foodChain._id} />
        </TabsContent>

        <TabsContent value="history">
          <TransferHistory foodChainId={foodChain._id} />
        </TabsContent>

        <TabsContent value="razorpay-route">
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-blue-800 text-sm">
                Razorpay Route allows you to receive payments directly from
                customers. Follow the steps below to set up your Razorpay Route
                account.
              </p>
            </div>

            {foodChain.razorpayRouteEnabled ? (
              <div className="bg-green-50 p-4 rounded-lg mb-4">
                <p className="text-green-800 font-medium">
                  Razorpay Route is enabled for your account
                </p>
                <p className="text-sm text-green-700 mt-1">
                  Account ID: {foodChain.razorpayAccountId}
                </p>
              </div>
            ) : null}

            <RazorpaySetup
              foodChainId={foodChain._id}
              onSuccess={() => fetchFoodChainDetails()}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
