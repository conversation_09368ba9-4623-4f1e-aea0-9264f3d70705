"use client";
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar } from "@/components/ui/avatar";
import { toast } from "sonner";
import { getAdminProfile, updateAdminProfile } from "@/server/admin";
import AdminPasswordChangeDialog from "@/components/custom/auth/AdminPasswordChangeDialog";
import { Loader2, Save, X, KeyRound } from "lucide-react";

interface AdminProfile {
  _id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  foodChain: {
    _id: string;
    name: string;
  };
}

const AdminProfilePage = () => {
  const [profile, setProfile] = useState<AdminProfile | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [editedProfile, setEditedProfile] = useState<Partial<AdminProfile>>({});

  const handleEdit = () => {
    setIsEditing(true);
    setEditedProfile(profile || {});
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedProfile(profile || {});
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setEditedProfile((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      const res = await updateAdminProfile(editedProfile);
      if (res.success) {
        setProfile({
          ...profile!,
          ...editedProfile,
        });
        toast.success("Profile updated successfully");
        setIsEditing(false);
      } else {
        toast.error(res.message || "Failed to update profile");
      }
    } catch (error) {
      toast.error("An error occurred while updating profile");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAdminProfile = async () => {
    try {
      setIsLoading(true);
      const res = await getAdminProfile();
      if (res.success) {
        setProfile(res.data);
        setEditedProfile(res.data);
      } else {
        toast.error(res.message || "Failed to fetch profile");
      }
    } catch (error) {
      toast.error("An error occurred while fetching profile");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAdminProfile();
  }, []);

  if (isLoading && !profile) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
          <p className="mt-4">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto md:py-6 md:px-4 max-w-5xl">
      <Tabs defaultValue="details" className="w-full">
        <div className="flex justify-between items-center">
          <h1 className="md:text-2xl text-lg font-bold md:mb-6">My Profile</h1>
          <TabsList className="md:mb-4">
            <TabsTrigger value="details">Profile Details</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="details">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 md:pb-2">
              <CardTitle className="md:text-xl text-md font-bold">
                Personal Information
              </CardTitle>
              {!isEditing ? (
                <Button onClick={handleEdit}>Edit Profile</Button>
              ) : (
                <div className="space-x-2">
                  <Button variant="outline" onClick={handleCancel}>
                    <X className="h-4 w-4 mr-2" />
                    <div className="hidden md:block">Cancel</div>
                  </Button>
                  <Button onClick={handleSave}>
                    <Save className="h-4 w-4 mr-2" />
                    <div className="hidden md:block">Save Changes</div>
                  </Button>
                </div>
              )}
            </CardHeader>
            <Separator />
            <CardContent className=" md:pt-6 sm:p-1">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex flex-col items-center md:w-1/4">
                  <Avatar className="md:h-24 md:w-24 h-12 w-12 md-1 md:mb-4">
                    <div className="bg-primary md:h-24 md:w-24 h-12 w-12 rounded-full flex items-center justify-center">
                      <span className="md:text-3xl text-lg text-white">
                        {profile?.name.charAt(0)}
                      </span>
                    </div>
                  </Avatar>
                  <div className="text-center">
                    <h2 className="font-medium text-lg">{profile?.name}</h2>
                    <p className="text-sm text-gray-500 capitalize">
                      {profile?.role.replace("_", " ")}
                    </p>
                  </div>
                </div>

                <div className="md:w-3/4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      {isEditing ? (
                        <Input
                          id="name"
                          name="name"
                          value={editedProfile?.name || ""}
                          onChange={handleChange}
                        />
                      ) : (
                        <p className="text-gray-700 pt-1">{profile?.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <p className="text-gray-700 pt-1">{profile?.email}</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      {isEditing ? (
                        <Input
                          id="phone"
                          name="phone"
                          value={editedProfile?.phone || ""}
                          onChange={handleChange}
                        />
                      ) : (
                        <p className="text-gray-700 pt-1">
                          {profile?.phone || "Not provided"}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="foodChain">Associated Food Chain</Label>
                      <p className="text-gray-700 pt-1">
                        {profile?.foodChain?.name || "None"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium">Password</h3>
                    <p className="text-sm text-gray-500">
                      Change your account password
                    </p>
                  </div>
                  <Button
                    onClick={() => setShowPasswordDialog(true)}
                    variant="outline"
                  >
                    <KeyRound className="h-4 w-4 mr-2" />
                    Change Password
                  </Button>
                </div>
                <Separator />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {showPasswordDialog && (
        <AdminPasswordChangeDialog
          open={showPasswordDialog}
          onOpenChange={setShowPasswordDialog}
        />
      )}
    </div>
  );
};

export default AdminProfilePage;
