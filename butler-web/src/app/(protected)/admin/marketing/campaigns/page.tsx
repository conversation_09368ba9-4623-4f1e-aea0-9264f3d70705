"use client";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Search, Plus, Loader2 } from "lucide-react";
import { getAllCampaigns } from "@/server/marketing";

export default function CampaignsPage() {
  const [loading, setLoading] = useState(true);
  const [campaigns, setCampaigns] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  console.log(campaigns);
  // Fetch campaigns
  const fetchCampaigns = async () => {
    setLoading(true);
    try {
      const response = await getAllCampaigns();
      if (response.success) {
        setCampaigns(response.data);
      } else {
        toast.error(response.message || "Failed to fetch campaigns");
      }
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      toast.error("An error occurred while fetching campaigns");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check if campaigns are enabled
    const enableCampaigns = process.env.NEXT_PUBLIC_ENABLE_CAMPAIGNS === "true";
    if (enableCampaigns) {
      fetchCampaigns();
    } else {
      setLoading(false);
    }
  }, []);

  // Check if campaigns are enabled
  const enableCampaigns = process.env.NEXT_PUBLIC_ENABLE_CAMPAIGNS === "true";
  if (!enableCampaigns) {
    return (
      <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border border-dashed border-gray-300">
        <h3 className="text-lg font-medium text-gray-900">
          Campaigns are disabled
        </h3>
        <p className="text-sm text-gray-500 mt-2">
          This feature is currently disabled. Please contact your administrator.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="relative w-full md:w-96">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search campaigns..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Campaign
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <div className="text-center py-10">
          <h3 className="text-lg font-medium">
            Campaigns functionality coming soon
          </h3>
          <p className="text-sm text-gray-500 mt-2">
            This feature is under development
          </p>
        </div>
      )}
    </div>
  );
}
