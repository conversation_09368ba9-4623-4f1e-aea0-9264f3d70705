/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { ArrowLeft, Loader2, Save, Plus } from "lucide-react";
import { createOffer } from "@/server/marketing";
import { getAllOutlets, getAllDishes, getAllCategories } from "@/server/admin";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function OfferCreatePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [outlets, setOutlets] = useState([]);
  const [dishes, setDishes] = useState([]);
  const [categories, setCategories] = useState([]);

  // Initialize offer with default values
  const [offer, setOffer] = useState<any>({
    name: "",
    description: "",
    offerType: "",
    discountDetails: {
      discountType: "percentage",
      discountValue: 0,
      buyQuantity: 1,
      getQuantity: 1,
      freeItemId: "",
      freeItemQuantity: 1,
      comboItems: [] as any[],
      comboPrice: 0,
      minimumOrderValue: 0,
      maxDiscount: 0,
      requiredCategories: [] as string[],
      minimumCategoriesCount: 1,
      customerTiers: [] as string[],
      timeRestrictions: {
        startTime: "",
        endTime: "",
        daysOfWeek: [] as number[],
      },
    },
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    isActive: true,
    applicableOutlets: [] as string[],
    applicableDishes: [] as string[],
    termsAndConditions: "",
    bannerImage: "",
    displayOnApp: true,
    stackingRules: {
      canStackWithOthers: false,
      stackableOfferTypes: [] as string[],
      priority: 0,
    },
    usageRules: {
      usageLimit: 0,
      perCustomerLimit: 0,
      perOrderLimit: 1,
    },
    autoApply: false,
  });

  // Fetch outlets, dishes, and categories
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [outletsResponse, dishesResponse, categoriesResponse] =
        await Promise.all([
          getAllOutlets(),
          getAllDishes(),
          getAllCategories(),
        ]);

      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
      }

      if (dishesResponse.success) {
        setDishes(dishesResponse.data);
      }

      if (categoriesResponse.success) {
        setCategories(categoriesResponse.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load required data");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setOffer({ ...offer, [name]: value });
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setOffer({ ...offer, [name]: value });
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    if (date) {
      setOffer({ ...offer, [name]: date });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setOffer({ ...offer, [name]: checked });
  };

  // Handle nested object changes
  const handleNestedChange = (path: string, value: any) => {
    const keys = path.split(".");
    const newOffer = { ...offer };
    let current = newOffer;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    setOffer(newOffer);
  };

  // Handle outlet selection
  const handleOutletSelection = (outletId: string, checked: boolean) => {
    setOffer((prev: any) => {
      const outlets = checked
        ? [...prev.applicableOutlets, outletId]
        : prev.applicableOutlets.filter((id: string) => id !== outletId);
      return { ...prev, applicableOutlets: outlets };
    });
  };

  // Handle dish selection
  const handleDishSelection = (dishId: string, checked: boolean) => {
    setOffer((prev: any) => {
      const dishes = checked
        ? [...prev.applicableDishes, dishId]
        : prev.applicableDishes.filter((id: string) => id !== dishId);
      return { ...prev, applicableDishes: dishes };
    });
  };

  // Handle category selection for multi-dish type offers
  const handleCategorySelection = (categoryId: string, checked: boolean) => {
    setOffer((prev: any) => {
      const categories = checked
        ? [...prev.discountDetails.requiredCategories, categoryId]
        : prev.discountDetails.requiredCategories.filter(
            (id: string) => id !== categoryId
          );
      return {
        ...prev,
        discountDetails: {
          ...prev.discountDetails,
          requiredCategories: categories,
        },
      };
    });
  };

  // Handle combo items
  const addComboItem = () => {
    setOffer((prev: any) => ({
      ...prev,
      discountDetails: {
        ...prev.discountDetails,
        comboItems: [
          ...prev.discountDetails.comboItems,
          { dishId: "", quantity: 1 },
        ],
      },
    }));
  };

  const removeComboItem = (index: number) => {
    setOffer((prev: any) => ({
      ...prev,
      discountDetails: {
        ...prev.discountDetails,
        comboItems: prev.discountDetails.comboItems.filter(
          (_: any, i: number) => i !== index
        ),
      },
    }));
  };

  const updateComboItem = (index: number, field: string, value: any) => {
    setOffer((prev: any) => {
      const newComboItems = [...prev.discountDetails.comboItems];
      newComboItems[index] = { ...newComboItems[index], [field]: value };
      return {
        ...prev,
        discountDetails: {
          ...prev.discountDetails,
          comboItems: newComboItems,
        },
      };
    });
  };

  // Handle customer tier selection
  const handleCustomerTierSelection = (tier: string, checked: boolean) => {
    setOffer((prev: any) => {
      const tiers = checked
        ? [...prev.discountDetails.customerTiers, tier]
        : prev.discountDetails.customerTiers.filter((t: string) => t !== tier);
      return {
        ...prev,
        discountDetails: {
          ...prev.discountDetails,
          customerTiers: tiers,
        },
      };
    });
  };

  // Handle day of week selection
  const handleDayOfWeekSelection = (day: number, checked: boolean) => {
    setOffer((prev: any) => {
      const days = checked
        ? [...prev.discountDetails.timeRestrictions.daysOfWeek, day]
        : prev.discountDetails.timeRestrictions.daysOfWeek.filter(
            (d: number) => d !== day
          );
      return {
        ...prev,
        discountDetails: {
          ...prev.discountDetails,
          timeRestrictions: {
            ...prev.discountDetails.timeRestrictions,
            daysOfWeek: days,
          },
        },
      };
    });
  };

  // Validation function
  const validateOffer = () => {
    if (!offer.name.trim()) {
      toast.error("Offer name is required");
      return false;
    }

    if (!offer.offerType) {
      toast.error("Offer type is required");
      return false;
    }

    if (!offer.startDate || !offer.endDate) {
      toast.error("Start and end dates are required");
      return false;
    }

    if (offer.endDate <= offer.startDate) {
      toast.error("End date must be after start date");
      return false;
    }

    // Validate discount details based on offer type
    const { discountDetails } = offer;

    switch (offer.offerType) {
      case "discount":
      case "minimumAmount":
      case "quantityDiscount":
      case "dateRange":
      case "customerTier":
      case "firstTime":
      case "timeBasedSpecial":
      case "dayOfWeek":
        if (
          !discountDetails.discountValue ||
          discountDetails.discountValue <= 0
        ) {
          toast.error("Discount value is required and must be greater than 0");
          return false;
        }
        break;

      case "BOGO":
        if (!discountDetails.buyQuantity || !discountDetails.getQuantity) {
          toast.error("Buy and get quantities are required for BOGO offers");
          return false;
        }
        break;

      case "freeItem":
        if (!discountDetails.freeItemId) {
          toast.error("Free item selection is required");
          return false;
        }
        break;

      case "combo":
        if (!discountDetails.comboItems.length || !discountDetails.comboPrice) {
          toast.error("Combo items and price are required for combo offers");
          return false;
        }
        break;

      case "multiDishType":
        if (!discountDetails.requiredCategories.length) {
          toast.error(
            "Required categories must be selected for multi-dish type offers"
          );
          return false;
        }
        break;
    }

    return true;
  };

  // Clean offer data before submission
  const cleanOfferData = (offerData: any) => {
    const cleaned = { ...offerData };

    // Clean discount details based on offer type
    const cleanedDiscountDetails = { ...cleaned.discountDetails };

    // Remove empty or irrelevant fields based on offer type
    switch (cleaned.offerType) {
      case "BOGO":
        // Only keep BOGO-specific fields
        cleanedDiscountDetails.freeItemId = undefined;
        cleanedDiscountDetails.comboItems = undefined;
        cleanedDiscountDetails.comboPrice = undefined;
        cleanedDiscountDetails.minimumOrderValue = undefined;
        cleanedDiscountDetails.requiredCategories = undefined;
        cleanedDiscountDetails.customerTiers = undefined;
        cleanedDiscountDetails.discountValue = undefined;
        cleanedDiscountDetails.discountType = undefined;
        cleanedDiscountDetails.maxDiscount = undefined;
        break;

      case "freeItem":
        // Only keep free item fields, remove empty freeItemId
        if (!cleanedDiscountDetails.freeItemId) {
          delete cleanedDiscountDetails.freeItemId;
        }
        cleanedDiscountDetails.buyQuantity = undefined;
        cleanedDiscountDetails.getQuantity = undefined;
        cleanedDiscountDetails.comboItems = undefined;
        cleanedDiscountDetails.comboPrice = undefined;
        cleanedDiscountDetails.minimumOrderValue = undefined;
        cleanedDiscountDetails.requiredCategories = undefined;
        cleanedDiscountDetails.customerTiers = undefined;
        cleanedDiscountDetails.discountValue = undefined;
        cleanedDiscountDetails.discountType = undefined;
        cleanedDiscountDetails.maxDiscount = undefined;
        break;

      case "combo":
        // Only keep combo fields
        cleanedDiscountDetails.freeItemId = undefined;
        cleanedDiscountDetails.buyQuantity = undefined;
        cleanedDiscountDetails.getQuantity = undefined;
        cleanedDiscountDetails.minimumOrderValue = undefined;
        cleanedDiscountDetails.requiredCategories = undefined;
        cleanedDiscountDetails.customerTiers = undefined;
        cleanedDiscountDetails.discountValue = undefined;
        cleanedDiscountDetails.discountType = undefined;
        cleanedDiscountDetails.maxDiscount = undefined;
        break;

      case "multiDishType":
        // Only keep multi-dish type fields
        cleanedDiscountDetails.freeItemId = undefined;
        cleanedDiscountDetails.buyQuantity = undefined;
        cleanedDiscountDetails.getQuantity = undefined;
        cleanedDiscountDetails.comboItems = undefined;
        cleanedDiscountDetails.comboPrice = undefined;
        cleanedDiscountDetails.minimumOrderValue = undefined;
        cleanedDiscountDetails.customerTiers = undefined;
        break;

      case "customerTier":
        // Only keep customer tier fields
        cleanedDiscountDetails.freeItemId = undefined;
        cleanedDiscountDetails.buyQuantity = undefined;
        cleanedDiscountDetails.getQuantity = undefined;
        cleanedDiscountDetails.comboItems = undefined;
        cleanedDiscountDetails.comboPrice = undefined;
        cleanedDiscountDetails.requiredCategories = undefined;
        break;

      case "minimumAmount":
        // Keep minimum amount and discount fields
        cleanedDiscountDetails.freeItemId = undefined;
        cleanedDiscountDetails.buyQuantity = undefined;
        cleanedDiscountDetails.getQuantity = undefined;
        cleanedDiscountDetails.comboItems = undefined;
        cleanedDiscountDetails.comboPrice = undefined;
        cleanedDiscountDetails.requiredCategories = undefined;
        cleanedDiscountDetails.customerTiers = undefined;
        break;

      default:
        // For discount, quantityDiscount, dateRange, firstTime, timeBasedSpecial
        cleanedDiscountDetails.freeItemId = undefined;
        cleanedDiscountDetails.buyQuantity = undefined;
        cleanedDiscountDetails.getQuantity = undefined;
        cleanedDiscountDetails.comboItems = undefined;
        cleanedDiscountDetails.comboPrice = undefined;
        cleanedDiscountDetails.requiredCategories = undefined;
        cleanedDiscountDetails.customerTiers = undefined;
        if (cleaned.offerType !== "minimumAmount") {
          cleanedDiscountDetails.minimumOrderValue = undefined;
        }
        break;
    }

    // Remove undefined fields
    Object.keys(cleanedDiscountDetails).forEach((key) => {
      if (
        cleanedDiscountDetails[key] === undefined ||
        cleanedDiscountDetails[key] === ""
      ) {
        delete cleanedDiscountDetails[key];
      }
    });

    cleaned.discountDetails = cleanedDiscountDetails;

    // Clean empty arrays
    if (cleaned.applicableOutlets && cleaned.applicableOutlets.length === 0) {
      cleaned.applicableOutlets = undefined;
    }
    if (cleaned.applicableDishes && cleaned.applicableDishes.length === 0) {
      cleaned.applicableDishes = undefined;
    }

    return cleaned;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateOffer()) {
      return;
    }

    setSaving(true);
    try {
      const cleanedOffer = cleanOfferData(offer);
      console.log("Submitting offer:", cleanedOffer);

      const response = await createOffer(cleanedOffer);
      if (response.success) {
        toast.success("Offer created successfully");
        router.push("/admin/marketing/offers");
      } else {
        toast.error(response.message || "Failed to create offer");
      }
    } catch (error) {
      console.error("Error creating offer:", error);
      toast.error("An error occurred while creating the offer");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.push("/admin/marketing/offers")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Offers
        </Button>
        <Button onClick={handleSubmit} disabled={saving}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Create Offer
            </>
          )}
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Create New Offer</h2>

        <div className="grid gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Offer Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="e.g., Summer Special"
                    value={offer.name}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="offerType">Offer Type *</Label>
                  <Select
                    value={offer.offerType}
                    onValueChange={(value) =>
                      handleSelectChange("offerType", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select offer type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="BOGO">Buy One Get One</SelectItem>
                      <SelectItem value="combo">Combo Offer</SelectItem>
                      <SelectItem value="discount">Discount</SelectItem>
                      <SelectItem value="freeItem">Free Item</SelectItem>
                      <SelectItem value="quantityDiscount">
                        Quantity Discount
                      </SelectItem>
                      <SelectItem value="multiDishType">
                        Multi-Dish Type
                      </SelectItem>
                      <SelectItem value="minimumAmount">
                        Minimum Amount
                      </SelectItem>
                      <SelectItem value="dayOfWeek">Day of Week</SelectItem>
                      <SelectItem value="dateRange">Date Range</SelectItem>
                      <SelectItem value="customerTier">
                        Customer Tier
                      </SelectItem>
                      <SelectItem value="firstTime">First Time</SelectItem>
                      <SelectItem value="timeBasedSpecial">
                        Time Based Special
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Describe the offer"
                  value={offer.description || ""}
                  onChange={handleInputChange}
                  className="min-h-[100px]"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Start Date *</Label>
                  <DatePicker
                    date={offer.startDate}
                    setDate={(date) => handleDateChange("startDate", date)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>End Date *</Label>
                  <DatePicker
                    date={offer.endDate}
                    setDate={(date) => handleDateChange("endDate", date)}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={offer.isActive}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange("isActive", checked === true)
                    }
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="displayOnApp"
                    checked={offer.displayOnApp}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange("displayOnApp", checked === true)
                    }
                  />
                  <Label htmlFor="displayOnApp">Display on App</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="autoApply"
                    checked={offer.autoApply}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange("autoApply", checked === true)
                    }
                  />
                  <Label htmlFor="autoApply">Auto Apply</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Discount Details */}
          {offer.offerType && (
            <Card>
              <CardHeader>
                <CardTitle>Discount Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Common discount fields */}
                {[
                  "discount",
                  "minimumAmount",
                  "quantityDiscount",
                  "dateRange",
                  "customerTier",
                  "firstTime",
                  "timeBasedSpecial",
                  "dayOfWeek",
                ].includes(offer.offerType) && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Discount Type</Label>
                      <Select
                        value={offer.discountDetails.discountType}
                        onValueChange={(value) =>
                          handleNestedChange(
                            "discountDetails.discountType",
                            value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="percentage">Percentage</SelectItem>
                          <SelectItem value="fixed">Fixed Amount</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Discount Value *</Label>
                      <Input
                        type="number"
                        placeholder={
                          offer.discountDetails.discountType === "percentage"
                            ? "e.g., 10"
                            : "e.g., 100"
                        }
                        value={offer.discountDetails.discountValue}
                        onChange={(e) =>
                          handleNestedChange(
                            "discountDetails.discountValue",
                            Number(e.target.value)
                          )
                        }
                      />
                    </div>
                    {offer.discountDetails.discountType === "percentage" && (
                      <div className="space-y-2">
                        <Label>Max Discount (₹)</Label>
                        <Input
                          type="number"
                          placeholder="e.g., 500"
                          value={offer.discountDetails.maxDiscount}
                          onChange={(e) =>
                            handleNestedChange(
                              "discountDetails.maxDiscount",
                              Number(e.target.value)
                            )
                          }
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Minimum Amount specific fields */}
                {offer.offerType === "minimumAmount" && (
                  <div className="space-y-2">
                    <Label>Minimum Order Value (₹) *</Label>
                    <Input
                      type="number"
                      placeholder="e.g., 500"
                      value={offer.discountDetails.minimumOrderValue}
                      onChange={(e) =>
                        handleNestedChange(
                          "discountDetails.minimumOrderValue",
                          Number(e.target.value)
                        )
                      }
                    />
                  </div>
                )}

                {/* BOGO specific fields */}
                {offer.offerType === "BOGO" && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Buy Quantity *</Label>
                      <Input
                        type="number"
                        min="1"
                        placeholder="e.g., 2"
                        value={offer.discountDetails.buyQuantity}
                        onChange={(e) =>
                          handleNestedChange(
                            "discountDetails.buyQuantity",
                            Number(e.target.value)
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Get Quantity *</Label>
                      <Input
                        type="number"
                        min="1"
                        placeholder="e.g., 1"
                        value={offer.discountDetails.getQuantity}
                        onChange={(e) =>
                          handleNestedChange(
                            "discountDetails.getQuantity",
                            Number(e.target.value)
                          )
                        }
                      />
                    </div>
                  </div>
                )}

                {/* Free Item specific fields */}
                {offer.offerType === "freeItem" && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Free Item *</Label>
                      <Select
                        value={offer.discountDetails.freeItemId}
                        onValueChange={(value) =>
                          handleNestedChange(
                            "discountDetails.freeItemId",
                            value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select free item" />
                        </SelectTrigger>
                        <SelectContent>
                          {dishes.map((dish: any) => (
                            <SelectItem key={dish._id} value={dish._id}>
                              {dish.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Free Item Quantity</Label>
                      <Input
                        type="number"
                        min="1"
                        placeholder="e.g., 1"
                        value={offer.discountDetails.freeItemQuantity}
                        onChange={(e) =>
                          handleNestedChange(
                            "discountDetails.freeItemQuantity",
                            Number(e.target.value)
                          )
                        }
                      />
                    </div>
                  </div>
                )}

                {/* Combo specific fields */}
                {offer.offerType === "combo" && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Combo Items *</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addComboItem}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Item
                      </Button>
                    </div>
                    {offer.discountDetails.comboItems.map(
                      (item: any, index: number) => (
                        <div
                          key={index}
                          className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg"
                        >
                          <div className="space-y-2">
                            <Label>Dish</Label>
                            <Select
                              value={item.dishId}
                              onValueChange={(value) =>
                                updateComboItem(index, "dishId", value)
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select dish" />
                              </SelectTrigger>
                              <SelectContent>
                                {dishes.map((dish: any) => (
                                  <SelectItem key={dish._id} value={dish._id}>
                                    {dish.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>Quantity</Label>
                            <Input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) =>
                                updateComboItem(
                                  index,
                                  "quantity",
                                  Number(e.target.value)
                                )
                              }
                            />
                          </div>
                          <div className="flex items-end">
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              onClick={() => removeComboItem(index)}
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                      )
                    )}
                    <div className="space-y-2">
                      <Label>Combo Price (₹) *</Label>
                      <Input
                        type="number"
                        placeholder="e.g., 299"
                        value={offer.discountDetails.comboPrice}
                        onChange={(e) =>
                          handleNestedChange(
                            "discountDetails.comboPrice",
                            Number(e.target.value)
                          )
                        }
                      />
                    </div>
                  </div>
                )}

                {/* Multi-dish type specific fields */}
                {offer.offerType === "multiDishType" && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Required Categories *</Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
                        {categories.map((category: any) => (
                          <div
                            key={category._id}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`category-${category._id}`}
                              checked={offer.discountDetails.requiredCategories.includes(
                                category._id
                              )}
                              onCheckedChange={(checked) =>
                                handleCategorySelection(
                                  category._id,
                                  checked === true
                                )
                              }
                            />
                            <Label
                              htmlFor={`category-${category._id}`}
                              className="text-sm"
                            >
                              {category.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Minimum Categories Count</Label>
                      <Input
                        type="number"
                        min="1"
                        placeholder="e.g., 2"
                        value={offer.discountDetails.minimumCategoriesCount}
                        onChange={(e) =>
                          handleNestedChange(
                            "discountDetails.minimumCategoriesCount",
                            Number(e.target.value)
                          )
                        }
                      />
                    </div>
                  </div>
                )}

                {/* Customer Tier specific fields */}
                {offer.offerType === "customerTier" && (
                  <div className="space-y-2">
                    <Label>Customer Tiers</Label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {["new", "regular", "vip", "premium"].map((tier) => (
                        <div key={tier} className="flex items-center space-x-2">
                          <Checkbox
                            id={`tier-${tier}`}
                            checked={offer.discountDetails.customerTiers.includes(
                              tier
                            )}
                            onCheckedChange={(checked) =>
                              handleCustomerTierSelection(
                                tier,
                                checked === true
                              )
                            }
                          />
                          <Label
                            htmlFor={`tier-${tier}`}
                            className="text-sm capitalize"
                          >
                            {tier}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Time-based restrictions */}
                {["dayOfWeek", "timeBasedSpecial"].includes(
                  offer.offerType
                ) && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Start Time</Label>
                        <Input
                          type="time"
                          value={
                            offer.discountDetails.timeRestrictions.startTime
                          }
                          onChange={(e) =>
                            handleNestedChange(
                              "discountDetails.timeRestrictions.startTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>End Time</Label>
                        <Input
                          type="time"
                          value={offer.discountDetails.timeRestrictions.endTime}
                          onChange={(e) =>
                            handleNestedChange(
                              "discountDetails.timeRestrictions.endTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Days of Week</Label>
                      <div className="grid grid-cols-7 gap-2">
                        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                          (day, index) => (
                            <div
                              key={day}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`day-${index}`}
                                checked={offer.discountDetails.timeRestrictions.daysOfWeek.includes(
                                  index
                                )}
                                onCheckedChange={(checked) =>
                                  handleDayOfWeekSelection(
                                    index,
                                    checked === true
                                  )
                                }
                              />
                              <Label
                                htmlFor={`day-${index}`}
                                className="text-sm"
                              >
                                {day}
                              </Label>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Applicability */}
          <Card>
            <CardHeader>
              <CardTitle>Applicability</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Applicable Outlets</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
                  {outlets.map((outlet: any) => (
                    <div
                      key={outlet._id}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`outlet-${outlet._id}`}
                        checked={offer.applicableOutlets.includes(outlet._id)}
                        onCheckedChange={(checked) =>
                          handleOutletSelection(outlet._id, checked === true)
                        }
                      />
                      <Label
                        htmlFor={`outlet-${outlet._id}`}
                        className="text-sm"
                      >
                        {outlet.address}
                      </Label>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500">
                  If none selected, offer applies to all outlets
                </p>
              </div>

              <div className="space-y-2">
                <Label>Applicable Dishes</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
                  {dishes.map((dish: any) => (
                    <div key={dish._id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`dish-${dish._id}`}
                        checked={offer.applicableDishes.includes(dish._id)}
                        onCheckedChange={(checked) =>
                          handleDishSelection(dish._id, checked === true)
                        }
                      />
                      <Label htmlFor={`dish-${dish._id}`} className="text-sm">
                        {dish.name}
                      </Label>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500">
                  If none selected, offer applies to all dishes
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Usage Rules */}
          <Card>
            <CardHeader>
              <CardTitle>Usage Rules</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Total Usage Limit</Label>
                  <Input
                    type="number"
                    min="0"
                    placeholder="0 = unlimited"
                    value={offer.usageRules.usageLimit}
                    onChange={(e) =>
                      handleNestedChange(
                        "usageRules.usageLimit",
                        Number(e.target.value)
                      )
                    }
                  />
                  <p className="text-xs text-gray-500">0 means unlimited</p>
                </div>
                <div className="space-y-2">
                  <Label>Per Customer Limit</Label>
                  <Input
                    type="number"
                    min="0"
                    placeholder="0 = unlimited"
                    value={offer.usageRules.perCustomerLimit}
                    onChange={(e) =>
                      handleNestedChange(
                        "usageRules.perCustomerLimit",
                        Number(e.target.value)
                      )
                    }
                  />
                  <p className="text-xs text-gray-500">
                    0 means unlimited per customer
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Per Order Limit</Label>
                  <Input
                    type="number"
                    min="1"
                    placeholder="1"
                    value={offer.usageRules.perOrderLimit}
                    onChange={(e) =>
                      handleNestedChange(
                        "usageRules.perOrderLimit",
                        Number(e.target.value)
                      )
                    }
                  />
                  <p className="text-xs text-gray-500">
                    How many times per order
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stacking Rules */}
          <Card>
            <CardHeader>
              <CardTitle>Stacking Rules</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="canStackWithOthers"
                  checked={offer.stackingRules.canStackWithOthers}
                  onCheckedChange={(checked) =>
                    handleNestedChange(
                      "stackingRules.canStackWithOthers",
                      checked === true
                    )
                  }
                />
                <Label htmlFor="canStackWithOthers">
                  Can stack with other offers
                </Label>
              </div>

              <div className="space-y-2">
                <Label>Priority</Label>
                <Input
                  type="number"
                  min="0"
                  placeholder="0"
                  value={offer.stackingRules.priority}
                  onChange={(e) =>
                    handleNestedChange(
                      "stackingRules.priority",
                      Number(e.target.value)
                    )
                  }
                />
                <p className="text-xs text-gray-500">
                  Higher number = higher priority
                </p>
              </div>

              {offer.stackingRules.canStackWithOthers && (
                <div className="space-y-2">
                  <Label>Stackable Offer Types</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {[
                      "BOGO",
                      "combo",
                      "discount",
                      "freeItem",
                      "quantityDiscount",
                      "multiDishType",
                      "minimumAmount",
                      "dayOfWeek",
                      "dateRange",
                      "customerTier",
                      "firstTime",
                      "timeBasedSpecial",
                    ].map((type) => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={`stackable-${type}`}
                          checked={offer.stackingRules.stackableOfferTypes?.includes(
                            type
                          )}
                          onCheckedChange={(checked) => {
                            const currentTypes =
                              offer.stackingRules.stackableOfferTypes || [];
                            const newTypes = checked
                              ? [...currentTypes, type]
                              : currentTypes.filter((t: string) => t !== type);
                            handleNestedChange(
                              "stackingRules.stackableOfferTypes",
                              newTypes
                            );
                          }}
                        />
                        <Label
                          htmlFor={`stackable-${type}`}
                          className="text-sm"
                        >
                          {type}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Terms and Additional Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="termsAndConditions">Terms and Conditions</Label>
                <Textarea
                  id="termsAndConditions"
                  name="termsAndConditions"
                  placeholder="Enter terms and conditions"
                  value={offer.termsAndConditions || ""}
                  onChange={handleInputChange}
                  className="min-h-[100px]"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bannerImage">Banner Image URL</Label>
                <Input
                  id="bannerImage"
                  name="bannerImage"
                  placeholder="https://example.com/banner.jpg"
                  value={offer.bannerImage || ""}
                  onChange={handleInputChange}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
