/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import {
  Search,
  Plus,
  Loader2,
  MoreVertical,
  Trash2,
  Eye,
  ToggleLeft,
  ToggleRight,
} from "lucide-react";
import { getAllOffers, deleteOffer, updateOffer } from "@/server/marketing";
import { useRouter } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";

export default function OffersPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [offers, setOffers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [offerToDelete, setOfferToDelete] = useState<any>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Fetch offers
  const fetchOffers = async () => {
    setLoading(true);
    try {
      const response = await getAllOffers({
        status: statusFilter !== "all" ? statusFilter : undefined,
        type: typeFilter !== "all" ? typeFilter : undefined,
        search: searchTerm || undefined,
      });
      if (response.success) {
        setOffers(response.data);
      } else {
        toast.error(response.message || "Failed to fetch offers");
      }
    } catch (error) {
      console.error("Error fetching offers:", error);
      toast.error("An error occurred while fetching offers");
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (offer: any) => {
    try {
      setActionLoading(offer._id);
      const response = await updateOffer(offer._id, {
        isActive: !offer.isActive,
      });

      if (response.success) {
        toast.success(
          `Offer ${!offer.isActive ? "activated" : "deactivated"} successfully`
        );
        fetchOffers();
      } else {
        toast.error(response.message || "Failed to update offer status");
      }
    } catch (error) {
      console.error("Error updating offer status:", error);
      toast.error("An error occurred while updating offer status");
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteOffer = async () => {
    if (!offerToDelete) return;

    try {
      setActionLoading(offerToDelete._id);
      const response = await deleteOffer(offerToDelete._id);

      if (response.success) {
        toast.success("Offer deleted successfully");
        setOffers(
          offers.filter((offer: any) => offer._id !== offerToDelete._id)
        );
        setDeleteDialogOpen(false);
        setOfferToDelete(null);
      } else {
        toast.error(response.message || "Failed to delete offer");
      }
    } catch (error) {
      console.error("Error deleting offer:", error);
      toast.error("An error occurred while deleting offer");
    } finally {
      setActionLoading(null);
    }
  };

  const getOfferTypeLabel = (type: string) => {
    const typeLabels: { [key: string]: string } = {
      BOGO: "Buy One Get One",
      combo: "Combo Deal",
      discount: "Discount",
      freeItem: "Free Item",
      quantityDiscount: "Quantity Discount",
      multiDishType: "Multi-Category",
      minimumAmount: "Minimum Amount",
      dayOfWeek: "Day of Week",
      dateRange: "Date Range",
      customerTier: "Customer Tier",
      firstTime: "First Time",
      timeBasedSpecial: "Time Special",
    };
    return typeLabels[type] || type;
  };

  const getOfferTypeColor = (type: string) => {
    const typeColors: { [key: string]: string } = {
      BOGO: "bg-green-100 text-green-800",
      combo: "bg-blue-100 text-blue-800",
      discount: "bg-purple-100 text-purple-800",
      freeItem: "bg-orange-100 text-orange-800",
      quantityDiscount: "bg-indigo-100 text-indigo-800",
      multiDishType: "bg-pink-100 text-pink-800",
      minimumAmount: "bg-yellow-100 text-yellow-800",
      dayOfWeek: "bg-cyan-100 text-cyan-800",
      dateRange: "bg-red-100 text-red-800",
      customerTier: "bg-emerald-100 text-emerald-800",
      firstTime: "bg-violet-100 text-violet-800",
      timeBasedSpecial: "bg-amber-100 text-amber-800",
    };
    return typeColors[type] || "bg-gray-100 text-gray-800";
  };

  useEffect(() => {
    fetchOffers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [statusFilter, typeFilter, searchTerm]);

  // Filter offers based on search term
  const filteredOffers = offers.filter((offer: any) => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      offer.name.toLowerCase().includes(searchLower) ||
      (offer.description &&
        offer.description.toLowerCase().includes(searchLower))
    );
  });

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Check if offer is active
  const isOfferActive = (offer: any) => {
    const now = new Date();
    const startDate = new Date(offer.startDate);
    const endDate = new Date(offer.endDate);
    return offer.isActive && now >= startDate && now <= endDate;
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row justify-between gap-4">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search offers..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="BOGO">BOGO</SelectItem>
                <SelectItem value="discount">Discount</SelectItem>
                <SelectItem value="combo">Combo</SelectItem>
                <SelectItem value="freeItem">Free Item</SelectItem>
                <SelectItem value="quantityDiscount">
                  Quantity Discount
                </SelectItem>
                <SelectItem value="minimumAmount">Minimum Amount</SelectItem>
                <SelectItem value="customerTier">Customer Tier</SelectItem>
                <SelectItem value="firstTime">First Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <Button onClick={() => router.push("/admin/marketing/offers/new")}>
          <Plus className="h-4 w-4 mr-2" />
          Create Offer
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : filteredOffers.length > 0 ? (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 text-xs uppercase text-gray-500">
                <tr>
                  <th className="px-6 py-3 text-left">Name</th>
                  <th className="px-6 py-3 text-left">Type</th>
                  <th className="px-6 py-3 text-left">Validity</th>
                  <th className="px-6 py-3 text-left">Usage</th>
                  <th className="px-6 py-3 text-left">Status</th>
                  <th className="px-6 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {filteredOffers.map((offer: any) => (
                  <tr key={offer._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="font-medium">{offer.name}</div>
                      <div className="text-sm text-gray-500">
                        {offer.description}
                      </div>
                      {offer.autoApply && (
                        <Badge variant="outline" className="mt-1 text-xs">
                          Auto Apply
                        </Badge>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <Badge className={getOfferTypeColor(offer.offerType)}>
                        {getOfferTypeLabel(offer.offerType)}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        {formatDate(offer.startDate)} -{" "}
                        {formatDate(offer.endDate)}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        <div>
                          Used: {offer.analytics?.totalUsage || 0} times
                        </div>
                        <div className="text-gray-500">
                          Saved: ₹{offer.analytics?.totalSavings || 0}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          isOfferActive(offer)
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {isOfferActive(offer) ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-800"
                          onClick={() =>
                            router.push(`/admin/marketing/offers/${offer._id}`)
                          }
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleToggleStatus(offer)}
                              disabled={actionLoading === offer._id}
                            >
                              {offer.isActive ? (
                                <ToggleLeft className="h-4 w-4 mr-2" />
                              ) : (
                                <ToggleRight className="h-4 w-4 mr-2" />
                              )}
                              {offer.isActive ? "Deactivate" : "Activate"}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                setOfferToDelete(offer);
                                setDeleteDialogOpen(true);
                              }}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="text-center py-10 bg-white rounded-lg shadow">
          <h3 className="text-lg font-medium">No offers found</h3>
          <p className="text-sm text-gray-500 mt-2">
            {searchTerm
              ? "Try a different search term"
              : "Create your first offer to get started"}
          </p>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Offer</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{offerToDelete?.name}&quot;?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteOffer}
              className="bg-red-600 hover:bg-red-700"
              disabled={actionLoading === offerToDelete?._id}
            >
              {actionLoading === offerToDelete?._id ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
