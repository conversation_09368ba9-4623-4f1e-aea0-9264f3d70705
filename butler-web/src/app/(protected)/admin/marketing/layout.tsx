"use client";
import { Tabs, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function MarketingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState("coupons");

  // Determine active tab based on pathname
  useEffect(() => {
    if (pathname && pathname.includes("/campaigns")) {
      setActiveTab("campaigns");
    } else if (pathname && pathname.includes("/offers")) {
      setActiveTab("offers");
    } else {
      setActiveTab("coupons");
    }
  }, [pathname]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    switch (value) {
      case "campaigns":
        router.push("/admin/marketing/campaigns");
        break;
      case "offers":
        router.push("/admin/marketing/offers");
        break;
      case "coupons":
      default:
        router.push("/admin/marketing");
        break;
    }
  };

  // Check if campaigns are enabled
  const enableCampaigns = process.env.NEXT_PUBLIC_ENABLE_CAMPAIGNS === "true";

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4">
        <h1 className="text-2xl font-bold">Marketing</h1>
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList>
            <TabsTrigger value="coupons">Coupons</TabsTrigger>
            <TabsTrigger value="offers">Offers</TabsTrigger>
            {enableCampaigns && (
              <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            )}
          </TabsList>
        </Tabs>
      </div>
      <div>{children}</div>
    </div>
  );
}
