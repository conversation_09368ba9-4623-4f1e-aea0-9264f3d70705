/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { ArrowLeft, Loader2, Save } from "lucide-react";
import { getCouponById, updateCoupon } from "@/server/marketing";
import { getAllOutlets, getAllDishes } from "@/server/admin";
import { useRouter, useParams } from "next/navigation";

export default function CouponEditPage() {
  const { id } = useParams() || {};
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [coupon, setCoupon] = useState<any>(null);
  const [outlets, setOutlets] = useState([]);
  const [dishes, setDishes] = useState([]);

  // Fetch coupon details
  const fetchCouponDetails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getCouponById(id as string);
      if (response.success) {
        setCoupon({
          ...response.data,
          startDate: new Date(response.data.startDate),
          endDate: new Date(response.data.endDate),
          applicableOutlets:
            response.data.applicableOutlets?.map((o: any) => o._id) || [],
          applicableDishes:
            response.data.applicableDishes?.map((d: any) => d._id) || [],
        });
      } else {
        toast.error(response.message || "Failed to fetch coupon details");
        router.push("/admin/marketing");
      }
    } catch (error) {
      console.error("Error fetching coupon details:", error);
      toast.error("An error occurred while fetching coupon details");
      router.push("/admin/marketing");
    } finally {
      setLoading(false);
    }
  }, [id, router]);

  // Fetch outlets and dishes
  const fetchOutletsAndDishes = useCallback(async () => {
    try {
      const outletsResponse = await getAllOutlets();
      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
      }

      const dishesResponse = await getAllDishes();
      if (dishesResponse.success) {
        setDishes(dishesResponse.data);
      }
    } catch (error) {
      console.error("Error fetching outlets and dishes:", error);
    }
  }, []);

  useEffect(() => {
    fetchCouponDetails();
    fetchOutletsAndDishes();
  }, [fetchCouponDetails, fetchOutletsAndDishes]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCoupon({ ...coupon, [name]: value });
  };

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCoupon({ ...coupon, [name]: parseFloat(value) || 0 });
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setCoupon({ ...coupon, [name]: value });
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    if (date) {
      setCoupon({ ...coupon, [name]: date });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setCoupon({ ...coupon, [name]: checked });
  };

  // Handle outlet selection
  const handleOutletSelection = (outletId: string, checked: boolean) => {
    setCoupon((prev: any) => {
      const outlets = checked
        ? [...prev.applicableOutlets, outletId]
        : prev.applicableOutlets.filter((id: string) => id !== outletId);
      return { ...prev, applicableOutlets: outlets };
    });
  };

  // Handle dish selection
  const handleDishSelection = (dishId: string, checked: boolean) => {
    setCoupon((prev: any) => {
      const dishes = checked
        ? [...prev.applicableDishes, dishId]
        : prev.applicableDishes.filter((id: string) => id !== dishId);
      return { ...prev, applicableDishes: dishes };
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!coupon.code) {
      toast.error("Coupon code is required");
      return;
    }

    if (!coupon.discountValue || coupon.discountValue <= 0) {
      toast.error("Discount value must be greater than 0");
      return;
    }

    setSaving(true);
    try {
      const response = await updateCoupon(id as string, coupon);
      if (response.success) {
        toast.success("Coupon updated successfully");
        router.push("/admin/marketing");
      } else {
        toast.error(response.message || "Failed to update coupon");
      }
    } catch (error) {
      console.error("Error updating coupon:", error);
      toast.error("An error occurred while updating the coupon");
    } finally {
      setSaving(false);
    }
  };

  if (!coupon) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium">Coupon not found</h3>
        <Button
          className="mt-4"
          onClick={() => router.push("/admin/marketing")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Coupons
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.push("/admin/marketing")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Coupons
        </Button>
        <Button onClick={handleSubmit} disabled={saving}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Edit Coupon</h2>

        <div className="grid gap-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="code">Coupon Code</Label>
              <Input
                id="code"
                name="code"
                placeholder="e.g., SUMMER20"
                value={coupon.code}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="discountType">Discount Type</Label>
              <Select
                value={coupon.discountType}
                onValueChange={(value) =>
                  handleSelectChange("discountType", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select discount type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage</SelectItem>
                  <SelectItem value="fixed">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="discountValue">
                {coupon.discountType === "percentage"
                  ? "Discount Percentage"
                  : "Discount Amount (₹)"}
              </Label>
              <Input
                id="discountValue"
                name="discountValue"
                type="number"
                min="0"
                step={coupon.discountType === "percentage" ? "1" : "0.01"}
                value={coupon.discountValue}
                onChange={handleNumberChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="minOrderValue">Minimum Order Value (₹)</Label>
              <Input
                id="minOrderValue"
                name="minOrderValue"
                type="number"
                min="0"
                step="0.01"
                value={coupon.minOrderValue}
                onChange={handleNumberChange}
              />
            </div>
          </div>

          {coupon.discountType === "percentage" && (
            <div className="space-y-2">
              <Label htmlFor="maxDiscount">Maximum Discount (₹)</Label>
              <Input
                id="maxDiscount"
                name="maxDiscount"
                type="number"
                min="0"
                step="0.01"
                value={coupon.maxDiscount || ""}
                onChange={handleNumberChange}
              />
              <p className="text-xs text-gray-500">
                Leave empty for no maximum discount limit
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              name="description"
              placeholder="Describe the coupon"
              value={coupon.description || ""}
              onChange={handleInputChange}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Start Date</Label>
              <DatePicker
                date={coupon.startDate}
                setDate={(date) => handleDateChange("startDate", date)}
              />
            </div>
            <div className="space-y-2">
              <Label>End Date</Label>
              <DatePicker
                date={coupon.endDate}
                setDate={(date) => handleDateChange("endDate", date)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="usageLimit">Usage Limit</Label>
            <Input
              id="usageLimit"
              name="usageLimit"
              type="number"
              min="0"
              step="1"
              value={coupon.usageLimit}
              onChange={handleNumberChange}
            />
            <p className="text-xs text-gray-500">
              Set to 0 for unlimited usage
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              checked={coupon.isActive}
              onCheckedChange={(checked) =>
                handleCheckboxChange("isActive", checked === true)
              }
            />
            <Label htmlFor="isActive">Active</Label>
          </div>

          <div className="space-y-2">
            <Label>Applicable Outlets</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
              {outlets.map((outlet: any) => (
                <div key={outlet._id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`outlet-${outlet._id}`}
                    checked={coupon.applicableOutlets.includes(outlet._id)}
                    onCheckedChange={(checked) =>
                      handleOutletSelection(outlet._id, checked === true)
                    }
                  />
                  <Label htmlFor={`outlet-${outlet._id}`} className="text-sm">
                    {outlet.name}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">
              If none selected, coupon applies to all outlets
            </p>
          </div>

          <div className="space-y-2">
            <Label>Applicable Dishes</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
              {dishes.map((dish: any) => (
                <div key={dish._id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`dish-${dish._id}`}
                    checked={coupon.applicableDishes.includes(dish._id)}
                    onCheckedChange={(checked) =>
                      handleDishSelection(dish._id, checked === true)
                    }
                  />
                  <Label htmlFor={`dish-${dish._id}`} className="text-sm">
                    {dish.name}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">
              If none selected, coupon applies to all dishes
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
