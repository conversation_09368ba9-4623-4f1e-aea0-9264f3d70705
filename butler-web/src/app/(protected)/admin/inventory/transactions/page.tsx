/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar as CalendarIcon, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import {
  getInventoryTransactions,
  getAllInventoryItems,
} from "@/server/inventory";
import { getAllOutlets } from "@/server/admin";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface Transaction {
  _id: string;
  itemId: {
    _id: string;
    name: string;
    unit: string;
  };
  type: string;
  quantity: number;
  previousQuantity: number;
  newQuantity: number;
  reason: string;
  timestamp: string;
  userId: {
    _id: string;
    name: string;
  };
  outletId: {
    _id: string;
    name: string;
  };
  transferToOutletId?: {
    _id: string;
    name: string;
  };
  orderId?: string;
}

interface InventoryItem {
  _id: string;
  name: string;
}

interface Outlet {
  _id: string;
  name: string;
}

const InventoryTransactionsPage = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState("all");
  const [selectedOutlet, setSelectedOutlet] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchTransactions();
    fetchInventoryItems();
    fetchOutlets();
  }, []);

  useEffect(() => {
    fetchTransactions();
  }, [page, selectedItem, selectedOutlet, selectedType, startDate, endDate]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const formattedStartDate = startDate
        ? format(startDate, "yyyy-MM-dd")
        : "";
      const formattedEndDate = endDate ? format(endDate, "yyyy-MM-dd") : "";

      const response = await getInventoryTransactions(
        page,
        20,
        getApiValue(selectedItem),
        getApiValue(selectedOutlet),
        getApiValue(selectedType),
        formattedStartDate,
        formattedEndDate
      );
      setTransactions(response.data.transactions);
      setTotalPages(response.data.pagination.pages);
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error("Failed to load transaction data");
    } finally {
      setLoading(false);
    }
  };

  const fetchInventoryItems = async () => {
    try {
      const response = await getAllInventoryItems(1, 100);
      setInventoryItems(response.data.items);
    } catch (error) {
      console.error("Error fetching inventory items:", error);
    }
  };

  const fetchOutlets = async () => {
    try {
      const response = await getAllOutlets();
      setOutlets(response.data);
    } catch (error) {
      console.error("Error fetching outlets:", error);
    }
  };

  const resetFilters = () => {
    setSelectedItem("all");
    setSelectedOutlet("all");
    setSelectedType("all");
    setStartDate(undefined);
    setEndDate(undefined);
    setPage(1);
  };

  // Convert select values for API calls
  const getApiValue = (value: string): string => {
    return value === "all" ? "" : value;
  };

  const getTransactionTypeBadge = (type: string) => {
    switch (type) {
      case "addition":
        return <Badge className="bg-green-100 text-green-800">Addition</Badge>;
      case "deduction":
        return <Badge className="bg-amber-100 text-amber-800">Deduction</Badge>;
      case "adjustment":
        return <Badge className="bg-blue-100 text-blue-800">Adjustment</Badge>;
      case "waste":
        return <Badge className="bg-red-100 text-red-800">Waste</Badge>;
      case "transfer":
        return (
          <Badge className="bg-purple-100 text-purple-800">Transfer</Badge>
        );
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  return (
    <div className="p-4">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Inventory Transactions</h1>
          <Button variant="outline" onClick={resetFilters}>
            Reset Filters
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Transaction History</CardTitle>
          <CardDescription>
            View all inventory transactions across your outlets
          </CardDescription>
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2">
              <Select value={selectedItem} onValueChange={setSelectedItem}>
                <SelectTrigger>
                  <SelectValue placeholder="All Items" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Items</SelectItem>
                  {inventoryItems.map((item) => (
                    <SelectItem key={item._id} value={item._id}>
                      {item.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedOutlet} onValueChange={setSelectedOutlet}>
                <SelectTrigger>
                  <SelectValue placeholder="All Outlets" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Outlets</SelectItem>
                  {outlets.map((outlet) => (
                    <SelectItem key={outlet._id} value={outlet._id}>
                      {outlet.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="addition">Addition</SelectItem>
                  <SelectItem value="deduction">Deduction</SelectItem>
                  <SelectItem value="adjustment">Adjustment</SelectItem>
                  <SelectItem value="waste">Waste</SelectItem>
                  <SelectItem value="transfer">Transfer</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-start text-left font-normal flex-1",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP") : "Start Date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-start text-left font-normal flex-1",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "PPP") : "End Date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : transactions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No transactions found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Item</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Previous</TableHead>
                    <TableHead>New</TableHead>
                    <TableHead>Outlet</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Reason</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction._id}>
                      <TableCell>
                        {format(new Date(transaction.timestamp), "PPP p")}
                      </TableCell>
                      <TableCell>
                        {transaction.itemId?.name || "Unknown Item"}
                      </TableCell>
                      <TableCell>
                        {getTransactionTypeBadge(transaction.type)}
                      </TableCell>
                      <TableCell>
                        {transaction.quantity} {transaction.itemId?.unit}
                      </TableCell>
                      <TableCell>{transaction.previousQuantity}</TableCell>
                      <TableCell>{transaction.newQuantity}</TableCell>
                      <TableCell>
                        {transaction.outletId?.name}
                        {transaction.transferToOutletId && (
                          <span className="text-xs block text-gray-500">
                            → {transaction.transferToOutletId.name}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {transaction.userId?.name || "System"}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {transaction.reason || "-"}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {totalPages > 1 && (
            <div className="flex justify-center mt-4">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
                className="mr-2"
              >
                Previous
              </Button>
              <span className="flex items-center mx-2">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
                className="ml-2"
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default InventoryTransactionsPage;
