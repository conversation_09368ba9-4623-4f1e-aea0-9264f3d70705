"use client";
import QuantityCards from "@/components/custom/dashboard/QuantityCards";
import { Separator } from "@/components/ui/separator";
import { getAllOutlets } from "@/server/admin";
import React, { useEffect, useState } from "react";

const AdminDashboardPage = () => {
  const [outlets, setOutlets] = useState([]);

  useEffect(() => {
    getAllOutlets().then((data) => {
      setOutlets(data.data);
    });
  }, []);
  return (
    <div>
      <div className="font-bold text-xl">Dashboard</div>
      <Separator />
      <div className="mt-2">
        <QuantityCards number={outlets?.length || 0} title="Outlets" />
      </div>
    </div>
  );
};

export default AdminDashboardPage;
