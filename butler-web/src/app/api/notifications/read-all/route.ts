import { NextRequest, NextResponse } from "next/server";
// import { getServerSession } from "next-auth";
// import { authOptions } from "@/lib/auth";
// import { API_URL } from "@/lib/constants";

export async function PUT(req: NextRequest) {
  // try {
  //   const session = await getServerSession(authOptions);

  //   if (!session || !session.user) {
  //     return NextResponse.json(
  //       { success: false, message: "Unauthorized" },
  //       { status: 401 }
  //     );
  //   }

  //   const response = await fetch(`${API_URL}/api/v1/notifications/read-all`, {
  //     method: "PUT",
  //     headers: {
  //       Authorization: `Bearer ${session.accessToken}`,
  //       "Content-Type": "application/json",
  //     },
  //   });

  //   const data = await response.json();
  //   return NextResponse.json(data);
  // } catch (error) {
  //   console.error("Error marking all notifications as read:", error);
  //   return NextResponse.json(
  //     { success: false, message: "Error marking all notifications as read" },
  //     { status: 500 }
  //   );
  // }
  console.log(req);
  return NextResponse.json({
    success: true,
    message: "Notifications marked as read",
  });
}
