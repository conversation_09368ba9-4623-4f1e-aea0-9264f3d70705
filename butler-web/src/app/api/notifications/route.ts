import { NextRequest, NextResponse } from "next/server";
// import { getServerSession } from "next-auth";
// import { authOptions } from "@/lib/auth";
// import { API_URL } from "@/lib/constants";

export async function GET(req: NextRequest) {
  // try {
  //   const session = await getServerSession(authOptions);

  //   if (!session || !session.user) {
  //     return NextResponse.json(
  //       { success: false, message: "Unauthorized" },
  //       { status: 401 }
  //     );
  //   }

  //   const { searchParams } = new URL(req.url);
  //   const limit = searchParams.get("limit") || "20";
  //   const skip = searchParams.get("skip") || "0";
  //   const unreadOnly = searchParams.get("unreadOnly") || "false";

  //   const response = await fetch(
  //     `${API_URL}/api/v1/notifications?limit=${limit}&skip=${skip}&unreadOnly=${unreadOnly}`,
  //     {
  //       headers: {
  //         Authorization: `Bearer ${session.accessToken}`,
  //         "Content-Type": "application/json",
  //       },
  //     }
  //   );

  //   const data = await response.json();
  //   return NextResponse.json(data);
  // } catch (error) {
  //   console.error("Error fetching notifications:", error);
  //   return NextResponse.json(
  //     { success: false, message: "Error fetching notifications" },
  //     { status: 500 }
  //   );
  // }
  console.log(req);
  return NextResponse.json({
    success: true,
    message: "Notifications fetched successfully",
  });
}

export async function PUT(req: NextRequest) {
  // try {
  //   const session = await getServerSession(authOptions);

  //   if (!session || !session.user) {
  //     return NextResponse.json(
  //       { success: false, message: "Unauthorized" },
  //       { status: 401 }
  //     );
  //   }

  //   const { id } = await req.json();

  //   if (!id) {
  //     return NextResponse.json(
  //       { success: false, message: "Notification ID is required" },
  //       { status: 400 }
  //     );
  //   }

  //   const response = await fetch(`${API_URL}/api/v1/notifications/${id}/read`, {
  //     method: "PUT",
  //     headers: {
  //       Authorization: `Bearer ${session.accessToken}`,
  //       "Content-Type": "application/json",
  //     },
  //   });

  //   const data = await response.json();
  //   return NextResponse.json(data);
  // } catch (error) {
  //   console.error("Error marking notification as read:", error);
  //   return NextResponse.json(
  //     { success: false, message: "Error marking notification as read" },
  //     { status: 500 }
  //   );
  // }
  console.log(req);
  return NextResponse.json({
    success: true,
    message: "Notification marked as read",
  });
}
