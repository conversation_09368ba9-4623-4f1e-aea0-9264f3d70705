import { NextRequest, NextResponse } from "next/server";

const BACKEND_URL =
  process.env.NEXT_PUBLIC_BASE_URL ||
  process.env.NEXT_PUBLIC_BACKEND_URL ||
  "http://localhost:3001";

export async function POST(request: NextRequest) {
  try {
    console.log("🎤 Frontend: Audio transcription request received");
    console.log("🔗 Backend URL:", BACKEND_URL);

    // Get the form data from the request
    const formData = await request.formData();

    // Log form data details for debugging
    const audioFile = formData.get("audio") as File;
    console.log("📁 Audio file details:", {
      name: audioFile?.name,
      size: audioFile?.size,
      type: audioFile?.type,
    });

    // Forward the request to the backend with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const backendResponse = await fetch(`${BACKEND_URL}/api/transcribe-audio`, {
      method: "POST",
      body: formData,
      signal: controller.signal,
      // Don't set Content-Type header, let fetch handle it for FormData
    });

    clearTimeout(timeoutId);

    const result = await backendResponse.json();

    if (backendResponse.ok) {
      console.log("✅ Frontend: Transcription successful");
      return NextResponse.json(result);
    } else {
      console.error("❌ Frontend: Transcription failed:", result);
      return NextResponse.json(result, { status: backendResponse.status });
    }
  } catch (error) {
    console.error("Frontend transcription API error:", error);

    // Handle specific error types
    let errorMessage = "Failed to process audio transcription";
    let errorDetails = "Unknown error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorDetails = error.message;

      // Handle network errors
      if (error.name === "AbortError") {
        errorMessage = "Request timeout - transcription took too long";
        errorDetails = "The transcription request timed out after 30 seconds";
        statusCode = 408;
      } else if (error.message.includes("fetch")) {
        errorMessage = "Cannot connect to transcription service";
        errorDetails = `Network error: ${error.message}. Backend URL: ${BACKEND_URL}`;
        statusCode = 503;
      } else if (error.message.includes("ECONNREFUSED")) {
        errorMessage = "Transcription service unavailable";
        errorDetails = "Backend server is not responding";
        statusCode = 503;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: errorDetails,
        backendUrl: BACKEND_URL, // Include for debugging
        timestamp: new Date().toISOString(),
      },
      { status: statusCode }
    );
  }
}

export async function GET() {
  try {
    console.log("🧪 Testing transcription service connection...");
    console.log("🔗 Backend URL:", BACKEND_URL);

    // Test endpoint with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const backendResponse = await fetch(
      `${BACKEND_URL}/api/transcribe-audio/test`,
      { signal: controller.signal }
    );

    clearTimeout(timeoutId);
    const result = await backendResponse.json();

    console.log("✅ Transcription service test result:", result);
    return NextResponse.json({
      ...result,
      backendUrl: BACKEND_URL,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Transcription service test failed:", error);

    let errorMessage = "Audio transcription service unavailable";
    let errorDetails = "Unknown error";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "Transcription service timeout";
        errorDetails = "Service did not respond within 10 seconds";
      } else if (error.message.includes("fetch")) {
        errorMessage = "Cannot connect to transcription service";
        errorDetails = `Network error: ${error.message}`;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: errorDetails,
        backendUrl: BACKEND_URL,
        timestamp: new Date().toISOString(),
      },
      { status: 503 }
    );
  }
}
