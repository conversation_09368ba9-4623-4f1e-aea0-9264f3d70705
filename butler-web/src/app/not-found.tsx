"use client";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";
import { useRouter } from "next/navigation";

const NotFound = () => {
  const router = useRouter();
  return (
    <div className="flex justify-center items-center h-screen w-full">
      <div>
        <h1 className="text-4xl font-bold">404 - Page Not Found</h1>
        <Button>
          <div onClick={() => router.back()}>Go Back</div>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
