"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTheme } from "@/contexts/ThemeContext";
import { useRouter } from "next/navigation";
import { Formik, Form, Field, FormikHelpers } from "formik";
import * as Yup from "yup";
import { Icon } from "@iconify/react";
import { superAdminLogin } from "@/server/super-admin";

interface SuperAdminLoginFormValues {
  email: string;
  password: string;
}

const validationSchema = Yup.object().shape({
  email: Yup.string()
    .email("Please enter a valid email address")
    .required("Email is required"),
  password: Yup.string()
    .min(6, "Password must be at least 6 characters")
    .required("Password is required"),
});

export default function SuperAdminLoginPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { theme } = useTheme();

  const initialValues: SuperAdminLoginFormValues = {
    email: "",
    password: "",
  };

  const handleSubmit = async (
    values: SuperAdminLoginFormValues,
    { setSubmitting, setFieldError }: FormikHelpers<SuperAdminLoginFormValues>
  ) => {
    try {
      setLoading(true);
      const response = await superAdminLogin(values.email, values.password);
      const data = response.data;

      if (data?.token) {
        // Set token in localStorage
        localStorage.setItem("super-auth-token", data.token);

        // Set token in cookie for middleware authentication
        // Set cookie with max-age of 10 years (315360000 seconds) for long-lasting token
        document.cookie = `super-auth-token=${data.token}; path=/; max-age=315360000; secure; samesite=strict`;

        router.push("/super-admin/dashboard");
      } else {
        setFieldError("email", "Invalid credentials");
        setFieldError("password", "Invalid credentials");
      }
    } catch (error) {
      console.error("Login error:", error);
      setFieldError("email", "An error occurred during login");
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="bg-white p-8 rounded-lg shadow-md w-96 border">
        <div className="flex items-center gap-2 mb-6">
          <Icon icon="lucide:shield" className="w-6 h-6" />
          <h1 className="text-2xl font-bold">Super Admin Login</h1>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form className="flex flex-col gap-4">
              {/* Email Field */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium mb-1"
                >
                  Email
                </label>
                <Field
                  as={Input}
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Enter your email"
                  className={`w-full ${
                    errors.email && touched.email ? "border-red-500" : ""
                  }`}
                />
                {errors.email && touched.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                )}
              </div>

              {/* Password Field */}
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium mb-1"
                >
                  Password
                </label>
                <div className="relative">
                  <Field
                    as={Input}
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    placeholder="Enter your password"
                    className={`w-full ${
                      errors.password && touched.password
                        ? "border-red-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2"
                  >
                    <Icon
                      icon={showPassword ? "lucide:eye-off" : "lucide:eye"}
                      className="w-5 h-5 text-gray-500"
                    />
                  </button>
                </div>
                {errors.password && touched.password && (
                  <p className="text-red-500 text-sm mt-1">{errors.password}</p>
                )}
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isSubmitting || loading}
                style={{ backgroundColor: theme.primaryColor }}
                className="w-full mt-2"
              >
                {isSubmitting || loading ? (
                  <div className="flex items-center gap-2">
                    <Icon
                      icon="lucide:loader-2"
                      className="w-4 h-4 animate-spin"
                    />
                    <span>Logging in...</span>
                  </div>
                ) : (
                  <span>Login</span>
                )}
              </Button>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
}
