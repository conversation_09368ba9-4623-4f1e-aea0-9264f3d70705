"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTheme } from "@/contexts/ThemeContext";
import { useRouter } from "next/navigation";
import { Formik, Form, Field, FormikHelpers } from "formik";
import * as Yup from "yup";
import { Icon } from "@iconify/react";
import { useAuth } from "@/contexts/AuthContext";
import FirstLoginDialog from "@/components/custom/auth/FirstLoginDialog";

// Define TypeScript interface for form values
interface LoginFormValues {
  email: string;
  password: string;
}

// Create validation schema using Yup
const validationSchema = Yup.object().shape({
  email: Yup.string()
    .email("Please enter a valid email address")
    .required("Email is required"),
  password: Yup.string()
    .min(6, "Password must be at least 6 characters")
    .required("Password is required"),
});

export default function LoginPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showFirstLoginDialog, setShowFirstLoginDialog] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const { theme } = useTheme();
  const { login: authLogin } = useAuth();

  const initialValues: LoginFormValues = {
    email: "",
    password: "",
  };

  const handleSubmit = async (
    values: LoginFormValues,
    { setSubmitting, setFieldError }: FormikHelpers<LoginFormValues>
  ) => {
    try {
      setLoading(true);

      // Use the AuthContext login function
      const loginResult = await authLogin(
        values.email,
        values.password,
        "admin"
      );

      if (loginResult.success) {
        // Check if this is a first-time login
        if (loginResult.isFirstTimeLogin) {
          // Show the first login dialog
          setUserEmail(values.email);
          setShowFirstLoginDialog(true);
        } else {
          // Regular login, redirect to dashboard
          router.push("/admin/analytics");
        }
      } else {
        setFieldError("email", "Invalid credentials");
        setFieldError("password", "Invalid credentials");
      }
    } catch (error) {
      console.error("Login error:", error);
      setFieldError("email", "An error occurred during login");
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  // Handle close of first login dialog
  const handleFirstLoginClose = () => {
    setShowFirstLoginDialog(false);
    router.push("/admin/analytics");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      {/* First Login Dialog */}
      {showFirstLoginDialog && (
        <FirstLoginDialog
          open={showFirstLoginDialog}
          onClose={handleFirstLoginClose}
          userType="admin"
          email={userEmail}
        />
      )}

      <div className="bg-white p-8 rounded-lg shadow-md w-96 border">
        <div className="flex items-center gap-2 mb-6">
          <Icon icon="lucide:lock" className="w-6 h-6" />
          <h1 className="text-2xl font-bold">Login</h1>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form className="flex flex-col gap-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium mb-1"
                >
                  Email
                </label>
                <Field
                  as={Input}
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Enter your email"
                  className={`w-full ${
                    errors.email && touched.email ? "border-red-500" : ""
                  }`}
                />
                {errors.email && touched.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium mb-1"
                >
                  Password
                </label>
                <div className="relative">
                  <Field
                    as={Input}
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    placeholder="Enter your password"
                    className={`w-full ${
                      errors.password && touched.password
                        ? "border-red-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2"
                  >
                    <Icon
                      icon={showPassword ? "lucide:eye-off" : "lucide:eye"}
                      className="w-5 h-5 text-gray-500"
                    />
                  </button>
                </div>
                {errors.password && touched.password && (
                  <p className="text-red-500 text-sm mt-1">{errors.password}</p>
                )}
              </div>

              <Button
                type="submit"
                disabled={isSubmitting || loading}
                style={{ backgroundColor: theme.primaryColor }}
                className="w-full mt-2"
              >
                {isSubmitting || loading ? (
                  <div className="flex items-center gap-2">
                    <Icon
                      icon="lucide:loader-2"
                      className="w-4 h-4 animate-spin"
                    />
                    <span>Logging in...</span>
                  </div>
                ) : (
                  <span>Login</span>
                )}
              </Button>

              <div className="flex justify-between text-sm mt-4">
                <button
                  type="button"
                  onClick={() => router.push("/forgot-password")}
                  className="text-blue-600 hover:underline"
                >
                  Forgot Password?
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
}
