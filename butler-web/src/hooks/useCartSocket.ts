/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useEffect, useRef, useState } from "react";
import { io, Socket } from "socket.io-client";

interface CartSocketHook {
  isConnected: boolean;
  error: string | null;
  lastUpdate: Date | null;
}

interface CartUpdateData {
  items: any[];
  appliedOffers: any[];
  appliedCoupons: any[];
  subtotal: number;
  totalOfferDiscount: number;
  totalCouponDiscount: number;
  totalDiscount: number;
  taxAmount: number;
  deliveryFee: number;
  packagingFee: number;
  finalTotal: number;
  updatedAt: string;
}

interface CartOperationData {
  operation: string;
  dish: any;
  quantity: number;
  success: boolean;
  message: string;
}

const useCartSocket = (
  userId: string | null,
  foodChainId: string | null,
  outletId: string | null,
  onCartUpdate?: (data: CartUpdateData) => void,
  onCartOperation?: (data: CartOperationData) => void
): CartSocketHook => {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const socketRef = useRef<Socket | null>(null);

  // Use refs to store callbacks to prevent unnecessary reconnections
  const onCartUpdateRef = useRef(onCartUpdate);
  const onCartOperationRef = useRef(onCartOperation);

  // Update refs when callbacks change
  useEffect(() => {
    onCartUpdateRef.current = onCartUpdate;
  }, [onCartUpdate]);

  useEffect(() => {
    onCartOperationRef.current = onCartOperation;
  }, [onCartOperation]);

  useEffect(() => {
    // Only connect if we have all required parameters
    if (!userId || !foodChainId || !outletId) {
      console.log("🔌 Cart socket: Missing required parameters");
      return;
    }

    // Don't create a new connection if one already exists and is connected
    if (socketRef.current && socketRef.current.connected) {
      console.log("🔌 Cart socket: Already connected, skipping...");
      return;
    }

    const BACKEND_URL =
      process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3001";

    console.log("🔌 Connecting to cart socket...");

    // Create socket connection with optimized settings
    const socket = io(`${BACKEND_URL}/cart-updates`, {
      transports: ["websocket", "polling"],
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 2000,
      reconnectionDelayMax: 5000,
      withCredentials: true,
      timeout: 15000,
      forceNew: false, // Reuse existing connection if possible
      autoConnect: true,
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on("connect", () => {
      console.log("✅ Cart socket connected");
      setIsConnected(true);
      setError(null);

      // Join the cart room
      socket.emit("join-cart", {
        userId,
        foodChainId,
        outletId,
      });
    });

    socket.on("disconnect", () => {
      console.log("❌ Cart socket disconnected");
      setIsConnected(false);
    });

    socket.on("connect_error", (err) => {
      console.error("❌ Cart socket connection error:", err);
      setError(`Connection error: ${err.message}`);
      setIsConnected(false);
    });

    // Cart update event handler
    socket.on("cart-update", (data) => {
      console.log("🛒 Cart update received:", data);
      setLastUpdate(new Date());

      if (onCartUpdateRef.current && data.data) {
        onCartUpdateRef.current(data.data);
      }
    });

    // Cart operation event handler
    socket.on("cart-operation", (data) => {
      console.log("🤖 Cart operation received:", data);
      setLastUpdate(new Date());

      if (onCartOperationRef.current) {
        onCartOperationRef.current(data);
      }
    });

    // Error event handler
    socket.on("error", (err) => {
      console.error("❌ Cart socket error:", err);
      setError(err.message || "Socket error");
    });

    // Cleanup function
    return () => {
      console.log("🔌 Cleaning up cart socket...");

      if (socketRef.current) {
        if (socketRef.current.connected) {
          socketRef.current.emit("leave-cart", {
            userId,
            foodChainId,
            outletId,
          });
        }

        socketRef.current.removeAllListeners();
        socketRef.current.disconnect();
        socketRef.current = null;
      }

      setIsConnected(false);
      setError(null);
    };
  }, [userId, foodChainId, outletId]);

  return {
    isConnected,
    error,
    lastUpdate,
  };
};

export default useCartSocket;
