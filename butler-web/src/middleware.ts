import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  // Get the auth tokens from cookies
  const authToken = request.cookies.get("auth-token")?.value;
  const superAuthToken = request.cookies.get("super-auth-token")?.value;

  // Get the current path information
  const isAdminPath = request.nextUrl.pathname.includes("/admin");
  const isSuperAdminPath = request.nextUrl.pathname.includes("/super-admin");
  const isLoginPath = request.nextUrl.pathname === "/login";
  const isSuperAdminLoginPath =
    request.nextUrl.pathname === "/super-admin-login";

  // Check if the current path is an auth page (login pages)
  // const isAuthPage = isLoginPath || isSuperAdminLoginPath;

  // Super Admin Routes Logic
  if (isSuperAdminPath && !isSuperAdminLoginPath) {
    if (!superAuthToken) {
      return NextResponse.redirect(new URL("/super-admin-login", request.url));
    }
  }

  // Admin Routes Logic
  if (isAdminPath) {
    if (!authToken) {
      return NextResponse.redirect(new URL("/login", request.url));
    }
  }

  // Redirect authenticated users away from login pages
  if (isLoginPath && authToken) {
    return NextResponse.redirect(new URL("/admin/analytics", request.url));
  }

  if (isSuperAdminLoginPath && superAuthToken) {
    return NextResponse.redirect(
      new URL("/super-admin/dashboard", request.url)
    );
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
};
