export interface OrderItem {
  dishId?: {
    _id: string;
    name: string;
    price: number;
  } | null;
  dishName?: string; // Stored dish name for deleted dishes
  quantity: number;
  price: number;
  isServed?: boolean;
  servedQuantity?: number;
  servedAt?: string;
  servedBy?: {
    _id: string;
    name: string;
  };
}

export interface Payment {
  _id: string;
  orderId: string;
  razorpayOrderId: string;
  razorpayPaymentId?: string;
  razorpayPaymentLinkId: string;
  paymentLink: string;
  amount: number;
  status: "created" | "paid" | "failed" | "cancelled";
  createdAt: string;
  updatedAt: string;
}

export interface OrderAssignment {
  userId?: {
    _id: string;
    name: string;
    role?: string;
  };
  role?: "chef" | "delivery" | "manager";
  assignedAt?: string;
}

export interface Order {
  _id: string;
  orderNumber: string;
  userId: {
    _id: string;
    name: string;
    phone: string;
    email?: string;
  };
  outletId: {
    _id: string;
    name: string;
    address: string;
  };
  items: OrderItem[];
  status:
    | "pending"
    | "confirmed"
    | "preparing"
    | "ready"
    | "completed"
    | "cancelled"
    | "rejected"
    | "modified";
  priority: "low" | "normal" | "high" | "urgent";
  assignedTo?: OrderAssignment;
  kitchenNotes?: string;
  modificationReason?: string;
  cancellationReason?: string;
  paymentStatus: "pending" | "requested" | "paid" | "failed";
  paymentMethod: "cash" | "online";
  totalAmount: number;
  couponCode?: string;
  couponDiscount?: number;
  appliedOffers?: Array<{
    offerId: string;
    offerName: string;
    offerType: string;
    discount: number;
    freeItems?: Array<{
      dishId: string;
      dishName: string;
      quantity: number;
      price: number;
    }>;
  }>;
  offerDiscount?: number;
  finalAmount: number;
  specialInstructions?: string;
  tableNumber?: string;
  createdAt: string;
  updatedAt: string;
}
