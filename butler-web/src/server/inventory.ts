/* eslint-disable @typescript-eslint/no-explicit-any */
import { getAdminToken, server } from ".";

// Get all inventory items
export const getAllInventoryItems = async (
  page = 1,
  limit = 20,
  search = "",
  category = "",
  outletId = "",
  lowStock = false
) => {
  const token = getAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/inventory?page=${page}&limit=${limit}&search=${search}&category=${category}&outletId=${outletId}&lowStock=${lowStock}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Error fetching inventory items:", error);
    throw error;
  }
};

// Get inventory summary
export const getInventorySummary = async (outletId = "") => {
  const token = getAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/inventory/summary${outletId ? `?outletId=${outletId}` : ""}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Error fetching inventory summary:", error);
    throw error;
  }
};

// Get inventory categories
export const getInventoryCategories = async () => {
  const token = getAdminToken();
  try {
    const response = await server.get("/v1/admin/inventory/categories", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching inventory categories:", error);
    throw error;
  }
};

// Get low stock items
export const getLowStockItems = async (outletId = "") => {
  const token = getAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/inventory/low-stock${outletId ? `?outletId=${outletId}` : ""}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Error fetching low stock items:", error);
    throw error;
  }
};

// Get a single inventory item
export const getInventoryItem = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/admin/inventory/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching inventory item:", error);
    throw error;
  }
};

// Create a new inventory item
export const createInventoryItem = async (itemData: any) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/inventory", itemData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error creating inventory item:", error);
    throw error;
  }
};

// Update an inventory item
export const updateInventoryItem = async (id: string, itemData: any) => {
  const token = getAdminToken();
  try {
    const response = await server.put(`/v1/admin/inventory/${id}`, itemData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error updating inventory item:", error);
    throw error;
  }
};

// Delete an inventory item
export const deleteInventoryItem = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(`/v1/admin/inventory/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error deleting inventory item:", error);
    throw error;
  }
};

// Update inventory quantity
export const updateInventoryQuantity = async (
  id: string,
  quantity: number,
  type: string,
  reason?: string,
  transferToOutletId?: string
) => {
  const token = getAdminToken();
  try {
    const payload: any = { quantity, type };
    if (reason) payload.reason = reason;
    if (transferToOutletId) payload.transferToOutletId = transferToOutletId;

    const response = await server.post(
      `/v1/admin/inventory/${id}/quantity`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Error updating inventory quantity:", error);
    throw error;
  }
};

// Get inventory transactions
export const getInventoryTransactions = async (
  page = 1,
  limit = 20,
  itemId = "",
  outletId = "",
  type = "",
  startDate = "",
  endDate = ""
) => {
  const token = getAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/inventory-transactions?page=${page}&limit=${limit}&itemId=${itemId}&outletId=${outletId}&type=${type}&startDate=${startDate}&endDate=${endDate}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Error fetching inventory transactions:", error);
    throw error;
  }
};

// Link inventory to dish
export const linkInventoryToDish = async (
  dishId: string,
  ingredients: any[]
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/dishes/${dishId}/ingredients`,
      { ingredients },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Error linking inventory to dish:", error);
    throw error;
  }
};
