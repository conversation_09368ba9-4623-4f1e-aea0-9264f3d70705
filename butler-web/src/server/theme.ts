import { getAdminToken, server } from ".";

export const getThemeConfig = async (foodChainId?: string) => {
  if (!foodChainId) return;
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/get-theme/${foodChainId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};
