import { server } from ".";

export const forgotPassword = async (email: string, role: string = "user") => {
  try {
    const response = await server.post("/v1/forget-password", {
      email,
      role,
    });
    return response.data;
  } catch (error) {
    console.error("Error requesting password reset:", error);
    return {
      success: false,
      message: "Failed to send password reset instructions",
    };
  }
};

export const resetPassword = async (
  token: string,
  password: string,
  role: string = "user"
) => {
  try {
    const response = await server.post("/v1/reset-password", {
      token,
      password,
      role,
    });
    return response.data;
  } catch (error) {
    console.error("Error resetting password:", error);
    return {
      success: false,
      message: "Failed to reset password",
    };
  }
};

interface ChangePasswordParams {
  oldPassword: string;
  newPassword: string;
  isFirstLogin?: boolean;
  userType: "admin" | "user";
}

export const changePassword = async ({
  oldPassword,
  newPassword,
  isFirstLogin = false,
  userType,
}: ChangePasswordParams) => {
  try {
    let endpoint = "";
    let token = "";

    if (userType === "admin") {
      endpoint = "/v1/admin/change-password";
      token = localStorage.getItem("auth-token") || "";
    } else {
      endpoint = "/v1/user/change-password";
      token = localStorage.getItem("user-token") || "";
    }

    const response = await server.post(
      endpoint,
      {
        oldPassword,
        newPassword,
        isFirstLogin,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error changing password:", error);
    return {
      success: false,
      message: "Failed to change password",
    };
  }
};
