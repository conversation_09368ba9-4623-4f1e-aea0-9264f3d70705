/* eslint-disable @typescript-eslint/no-explicit-any */
import { server } from ".";

// Get user's cart
export const getCart = async (foodChainId: string, outletId: string) => {
  try {
    const token = localStorage.getItem("user-token");
    if (!token) {
      throw new Error("No authentication token found");
    }
    const response = await server.get(
      `/v1/user/cart?foodChainId=${foodChainId}&outletId=${outletId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error("Error fetching cart:", error);
    return {
      success: false,
      message: "Failed to fetch cart",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Add item to cart
export const addToCart = async (
  dishId: string,
  quantity: number,
  foodChainId: string,
  outletId: string
) => {
  try {
    const token = localStorage.getItem("user-token");
    if (!token) {
      throw new Error("No authentication token found");
    }

    const response = await server.post(
      `/v1/user/cart/add`,
      {
        dishId,
        quantity,
        foodChainId,
        outletId,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error("Error adding item to cart:", error);
    return {
      success: false,
      message: "Failed to add item to cart",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Remove item from cart
export const removeFromCart = async (
  dishId: string,
  foodChainId: string,
  outletId: string
) => {
  try {
    const token = localStorage.getItem("user-token");
    if (!token) {
      throw new Error("No authentication token found");
    }
    const response = await server.delete(
      `/v1/user/cart/remove/${dishId}?foodChainId=${foodChainId}&outletId=${outletId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error("Error removing item from cart:", error);
    return {
      success: false,
      message: "Failed to remove item from cart",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Update item quantity in cart
export const updateCartItemQuantity = async (
  dishId: string,
  quantity: number,
  foodChainId: string,
  outletId: string
) => {
  try {
    const token = localStorage.getItem("user-token");
    if (!token) {
      throw new Error("No authentication token found");
    }

    console.log("API Call - Update cart item:", {
      dishId,
      quantity,
      foodChainId,
      outletId,
      token: token ? "Token exists" : "No token",
    });

    const response = await server.put(
      `/v1/user/cart/update/${dishId}`,
      {
        quantity,
        foodChainId,
        outletId,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    console.log("API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error updating cart item quantity:", error);
    // Log more detailed error information
    if (error && typeof error === "object" && "response" in error) {
      const axiosError = error as any;
      console.error("Error response data:", axiosError.response?.data);
      console.error("Error response status:", axiosError.response?.status);
    }
    return {
      success: false,
      message: "Failed to update cart item quantity",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Clear cart
export const clearCart = async (foodChainId: string, outletId: string) => {
  try {
    const token = localStorage.getItem("user-token");
    if (!token) {
      throw new Error("No authentication token found");
    }

    const response = await server.delete(
      `/v1/user/cart/clear?foodChainId=${foodChainId}&outletId=${outletId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error("Error clearing cart:", error);
    return {
      success: false,
      message: "Failed to clear cart",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Sync cart from localStorage to backend
export const syncCart = async (
  items: any[],
  foodChainId: string,
  outletId: string,
  appliedOffers: any[] = []
) => {
  try {
    const token = localStorage.getItem("user-token");
    if (!token) {
      throw new Error("No authentication token found");
    }

    const response = await server.post(
      `/v1/user/cart/sync`,
      {
        items,
        foodChainId,
        outletId,
        appliedOffers,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error("Error syncing cart:", error);
    return {
      success: false,
      message: "Failed to sync cart",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Helper function to get cart key for localStorage
export const getCartKey = (foodChainId: string, outletId?: string): string => {
  if (!foodChainId) return "cart"; // Fallback to original key
  return `cart_${foodChainId}${outletId ? `_${outletId}` : ""}`;
};

// Helper function to migrate localStorage cart to backend
export const migrateLocalStorageCart = async (
  foodChainId: string,
  outletId: string
) => {
  try {
    const cartKey = getCartKey(foodChainId, outletId);
    const storedCart = localStorage.getItem(cartKey);

    if (storedCart) {
      const parsedData = JSON.parse(storedCart);
      const items = parsedData.items || parsedData; // Handle both old and new formats
      const appliedOffers = parsedData.appliedOffers || [];

      if (items.length > 0) {
        const result = await syncCart(
          items,
          foodChainId,
          outletId,
          appliedOffers
        );

        if (result.success) {
          // Clear localStorage after successful sync
          localStorage.removeItem(cartKey);
          localStorage.removeItem("cart"); // Also remove legacy cart
          return result;
        }
      }
    }

    // If no localStorage cart, just get backend cart
    return await getCart(foodChainId, outletId);
  } catch (error) {
    console.error("Error migrating localStorage cart:", error);
    return {
      success: false,
      message: "Failed to migrate cart",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem("user-token");
  return !!token;
};

// Helper function to get user ID from token
export const getUserIdFromToken = (): string | null => {
  try {
    const token = localStorage.getItem("user-token");
    if (!token) return null;

    // Decode JWT token to get user ID
    const payload = JSON.parse(atob(token.split(".")[1]));
    return payload.userId || payload.id || null;
  } catch (error) {
    console.error("Error decoding token:", error);
    return null;
  }
};
