import axios from "axios";

export const server = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api`,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add response interceptor
server.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 403) {
      // Clear all auth tokens
      localStorage.removeItem("auth-token");
      localStorage.removeItem("super-auth-token");
      localStorage.removeItem("chainId");

      // Clear cookies
      document.cookie =
        "auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      document.cookie =
        "super-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

      // Check which type of user was authenticated
      const path = window.location.pathname;
      if (path.startsWith("/super-admin")) {
        window.location.href = "/super-admin-login";
      } else if (path.startsWith("/admin")) {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

export const getAdminToken = () => {
  const token = localStorage.getItem("auth-token");
  return token;
};

export const getSuperAdminToken = () => {
  const token = localStorage.getItem("super-auth-token");
  return token;
};

export const getAdminFoodChainId = () => {
  const chainId = localStorage.getItem("chainId");
  return chainId;
};

export const getUserToken = () => {
  const token = localStorage.getItem("user-token");
  return token;
};

export const getUserId = () => {
  const userId = localStorage.getItem("userId");
  return userId;
};
