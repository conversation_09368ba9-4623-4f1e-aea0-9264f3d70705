import { Food<PERSON>hain, ThemeConfig } from "@/app/type";
import { server } from ".";
import { getSuperAdminToken } from "./index";

export const superAdminLogin = async (email: string, password: string) => {
  try {
    const response = await server.post("/v1/super-admin/login", {
      email,
      password,
    });
    return response.data;
  } catch (error) {
    console.error("Login failed:", error);
    return error;
  }
};

export const getAllChains = async () => {
  try {
    const response = await server.get("/v1/super-admin/food-chains", {
      headers: {
        Authorization: `Bearer ${getSuperAdminToken()}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const createNewChain = async (chain: {
  name: string;
  contact: string;
  theme: ThemeConfig;
  tagline: string;
  email: string;
}) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post(
      "/v1/super-admin/create-food-chain",
      chain,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const createChainAdmin = async (admin: {
  email: string;
  password: string;
  name: string;
  foodChainId: string;
  phone: string;
}) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post("/v1/super-admin/register", admin, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const getSingleChain = async (chainId: string) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.get(`/v1/super-admin/food-chain/${chainId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const createFoodChainAdmin = async (admin: {
  email: string;
  password: string;
  name: string;
  foodChainId: string;
  phone: string;
}) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post("/v1/super-admin/register", admin, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const updateChain = async (chain: FoodChain) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.put(
      `/v1/super-admin/food-chain/${chain._id}`,
      chain,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const getSuperAdminDashboard = async () => {
  const token = getSuperAdminToken();
  try {
    const response = await server.get("/v1/super-admin/dashboard", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    return error;
  }
};

export const getSystemAnalytics = async (
  startDate?: string,
  endDate?: string
) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.get("/v1/super-admin/analytics", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        startDate,
        endDate,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching analytics data:", error);
    return error;
  }
};

export const getFinancialReports = async (
  startDate?: string,
  endDate?: string,
  foodChainId?: string
) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.get("/v1/super-admin/financial-reports", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        startDate,
        endDate,
        foodChainId,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching financial reports:", error);
    return error;
  }
};

// Food Chain Registration Request Management
export const getAllRegistrationRequests = async (params: {
  status?: string;
  page?: number;
  limit?: number;
}) => {
  const token = getSuperAdminToken();
  try {
    const queryParams = new URLSearchParams();
    if (params.status) queryParams.append("status", params.status);
    if (params.page) queryParams.append("page", params.page.toString());
    if (params.limit) queryParams.append("limit", params.limit.toString());

    const response = await server.get(
      `/v1/super-admin/registration-requests?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching registration requests:", error);
    return error;
  }
};

export const updateRegistrationRequestStatus = async (
  id: string,
  status: string,
  adminNotes?: string
) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.put(
      `/v1/super-admin/registration-requests/${id}/status`,
      {
        status,
        adminNotes,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating registration request status:", error);
    return error;
  }
};

export const createFoodChainFromRequest = async (
  id: string,
  data: {
    tagline?: string;
    theme?: {
      primaryColor?: string;
      secondaryColor?: string;
      accentColor?: string;
      logoUrl?: string;
      favIcon?: string;
    };
  }
) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post(
      `/v1/super-admin/registration-requests/${id}/create-food-chain`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error creating food chain from request:", error);
    return error;
  }
};
