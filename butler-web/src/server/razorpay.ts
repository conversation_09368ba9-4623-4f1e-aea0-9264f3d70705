/* eslint-disable @typescript-eslint/no-explicit-any */
import { server } from ".";
import { getAdminToken } from "./index";

/**
 * Get Razorpay setup status for a food chain
 * @param foodChainId - The food chain ID
 */
export const getRazorpaySetupStatus = async (foodChainId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/payments/food-chain/${foodChainId}/razorpay-status`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching Razorpay setup status:", error);
    throw error;
  }
};

/**
 * Update food chain information and create Razorpay account
 * @param foodChainId - The food chain ID
 * @param data - The food chain data to update
 * @param currentStep - The current step in the setup process
 * @param completeSetup - Whether to complete the setup and create the account
 */
export const updateFoodChainAndCreateRazorpayAccount = async (
  foodChainId: string,
  data: any,
  currentStep: string,
  completeSetup: boolean = false
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/payments/food-chain/${foodChainId}/razorpay-setup`,
      {
        ...data,
        currentStep,
        completeSetup,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating food chain information:", error);
    throw error;
  }
};

/**
 * Add a stakeholder to a food chain's Razorpay account
 * @param foodChainId - The food chain ID
 * @param stakeholderData - The stakeholder data
 */
export const addStakeholder = async (
  foodChainId: string,
  stakeholderData: any
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/payments/food-chain/${foodChainId}/stakeholder`,
      stakeholderData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding stakeholder:", error);
    throw error;
  }
};

/**
 * Request Route configuration for a food chain's Razorpay account
 * @param foodChainId - The food chain ID
 * @param acceptTerms - Whether the user accepts the terms and conditions
 */
export const requestRouteConfiguration = async (
  foodChainId: string,
  acceptTerms: boolean
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/payments/food-chain/${foodChainId}/route-activation`,
      {
        acceptTerms,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error requesting Route configuration:", error);
    throw error;
  }
};

/**
 * Transfer funds to a food chain's Razorpay account
 * @param foodChainId - The food chain ID
 * @param paymentId - The payment ID
 * @param amount - The amount to transfer
 * @param description - The transfer description
 */
export const transferFunds = async (
  foodChainId: string,
  paymentId: string,
  amount: number,
  description: string
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/payments/food-chain/${foodChainId}/transfer`,
      {
        paymentId,
        amount,
        description,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error transferring funds:", error);
    throw error;
  }
};
