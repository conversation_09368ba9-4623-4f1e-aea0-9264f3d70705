import { server } from ".";
import { getAdminToken, getSuperAdminToken } from "./index";

/**
 * Get all subscription plans
 * @param activeOnly - Whether to return only active plans
 */
export const getSubscriptionPlans = async (activeOnly = true) => {
  const token = getAdminToken() || getSuperAdminToken();
  try {
    const response = await server.get(`/v1/subscription/plans`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        active: activeOnly,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching subscription plans:", error);
    throw error;
  }
};

/**
 * Create a new subscription plan (Super Admin only)
 * @param planData - The plan data
 */
export const createSubscriptionPlan = async (planData: {
  name: string;
  description?: string;
  price: number;
  interval?: string;
  features?: string[];
  isDefault?: boolean;
  isActive?: boolean;
}) => {
  const token = getSuperAdminToken();
  if (!token) {
    return {
      success: false,
      message:
        "Authentication token not found. Please log in as a super admin.",
    };
  }

  try {
    const response = await server.post(
      `/v1/super-admin/subscription/plans`,
      planData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: unknown) {
    const err = error as Error & { response?: { data?: { message?: string } } };
    console.error("Error creating subscription plan:", error);
    return {
      success: false,
      message:
        err.response?.data?.message ||
        "Failed to create subscription plan. Please try again.",
    };
  }
};

/**
 * Update a subscription plan (Super Admin only)
 * @param planId - The plan ID
 * @param planData - The updated plan data
 */
export const updateSubscriptionPlan = async (
  planId: string,
  planData: {
    name?: string;
    description?: string;
    price?: number;
    interval?: string;
    features?: string[];
    isActive?: boolean;
    isDefault?: boolean;
  }
) => {
  const token = getSuperAdminToken();
  if (!token) {
    return {
      success: false,
      message:
        "Authentication token not found. Please log in as a super admin.",
    };
  }

  try {
    const response = await server.put(
      `/v1/super-admin/subscription/plans/${planId}`,
      planData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: unknown) {
    const err = error as Error & { response?: { data?: { message?: string } } };
    console.error("Error updating subscription plan:", error);
    return {
      success: false,
      message:
        err.response?.data?.message ||
        "Failed to update subscription plan. Please try again.",
    };
  }
};

/**
 * Create a new subscription for a food chain (Super Admin only)
 * @param subscriptionData - The subscription data
 */
export const createSubscription = async (subscriptionData: {
  foodChainId: string;
  planId: string;
  outletCount?: number;
  autoRenew?: boolean;
}) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post(
      `/v1/super-admin/subscription`,
      subscriptionData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error creating subscription:", error);
    throw error;
  }
};

/**
 * Get all subscriptions (Super Admin only)
 * @param status - Filter by status
 * @param page - Page number
 * @param limit - Items per page
 */
export const getAllSubscriptions = async (
  status?: string,
  page = 1,
  limit = 10
) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.get(`/v1/super-admin/subscriptions`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        status,
        page,
        limit,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    throw error;
  }
};

/**
 * Get food chain subscription (Admin or Super Admin)
 * @param foodChainId - The food chain ID
 */
export const getFoodChainSubscription = async (foodChainId: string) => {
  const token = getAdminToken() || getSuperAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/subscription/food-chain/${foodChainId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching food chain subscription:", error);
    throw error;
  }
};

/**
 * Get invoice details
 * @param invoiceId - The invoice ID
 */
export const getInvoiceDetails = async (invoiceId: string) => {
  const token = getAdminToken() || getSuperAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/subscription/invoices/${invoiceId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching invoice details:", error);
    throw error;
  }
};

/**
 * Create a new invoice for an existing subscription (Super Admin only)
 * @param subscriptionId - The subscription ID
 * @param invoiceData - The invoice data
 */
export const createInvoice = async (
  subscriptionId: string,
  invoiceData: {
    amount?: number;
    dueDate?: string;
    notes?: string;
  }
) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post(
      `/v1/super-admin/subscription/${subscriptionId}/invoice`,
      invoiceData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error creating invoice:", error);
    throw error;
  }
};

/**
 * Verify subscription payment
 * @param paymentData - The payment data from Razorpay
 */
export const verifySubscriptionPayment = async (paymentData: {
  razorpay_payment_id: string;
  razorpay_payment_link_id: string;
  razorpay_signature: string;
}) => {
  const token = getAdminToken() || getSuperAdminToken();
  try {
    const response = await server.post(
      `/v1/subscription/payments/verify`,
      paymentData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error verifying subscription payment:", error);
    throw error;
  }
};

/**
 * Exempt a subscription from the next payment (Super Admin only)
 * @param subscriptionId - The subscription ID
 * @param reason - The reason for exemption
 */
export const exemptFromNextPayment = async (
  subscriptionId: string,
  reason?: string
) => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post(
      `/v1/super-admin/subscription/${subscriptionId}/exempt`,
      { reason },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error exempting subscription from payment:", error);
    throw error;
  }
};

/**
 * Trigger monthly subscription billing (Super Admin only)
 */
export const triggerMonthlyBilling = async () => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post(
      `/v1/super-admin/subscription/trigger-billing`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error triggering monthly billing:", error);
    throw error;
  }
};

/**
 * Check for trial periods ending soon (Super Admin only)
 */
export const checkTrialPeriods = async () => {
  const token = getSuperAdminToken();
  try {
    const response = await server.post(
      `/v1/super-admin/subscription/check-trials`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error checking trial periods:", error);
    throw error;
  }
};

/**
 * Change a subscription plan (Admin or Super Admin)
 * @param subscriptionId - The subscription ID
 * @param planId - The new plan ID
 */
export const upgradeSubscriptionPlan = async (
  subscriptionId: string,
  planId: string
) => {
  const token = getAdminToken() || getSuperAdminToken();
  if (!token) {
    return {
      success: false,
      message: "Authentication token not found. Please log in.",
    };
  }

  try {
    const response = await server.post(
      `/v1/admin/subscription/${subscriptionId}/upgrade`,
      { planId },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: unknown) {
    const err = error as Error & { response?: { data?: { message?: string } } };
    console.error("Error changing subscription plan:", error);
    return {
      success: false,
      message:
        err.response?.data?.message ||
        "Failed to change subscription plan. Please try again.",
    };
  }
};
