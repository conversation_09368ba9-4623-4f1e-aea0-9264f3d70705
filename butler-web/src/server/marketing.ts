/* eslint-disable @typescript-eslint/no-explicit-any */
import { server } from ".";
import { getAdminToken } from ".";

// Coupon API calls
export const getAllCoupons = async (params?: {
  status?: string;
  search?: string;
}) => {
  const token = getAdminToken();
  try {
    let url = "/v1/admin/coupons";
    if (params) {
      const queryParams = new URLSearchParams();
      if (params.status) queryParams.append("status", params.status);
      if (params.search) queryParams.append("search", params.search);
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }

    const response = await server.get(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching coupons:", error);
    return { success: false, message: "Failed to fetch coupons" };
  }
};

export const getCouponById = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/admin/coupons/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching coupon:", error);
    return { success: false, message: "Failed to fetch coupon" };
  }
};

export const createCoupon = async (couponData: any) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/coupons", couponData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error creating coupon:", error);
    return { success: false, message: "Failed to create coupon" };
  }
};

export const updateCoupon = async (id: string, couponData: any) => {
  const token = getAdminToken();
  try {
    const response = await server.put(`/v1/admin/coupons/${id}`, couponData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error updating coupon:", error);
    return { success: false, message: "Failed to update coupon" };
  }
};

export const deleteCoupon = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(`/v1/admin/coupons/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error deleting coupon:", error);
    return { success: false, message: "Failed to delete coupon" };
  }
};

export const validateCoupon = async (data: {
  code: string;
  orderId?: string;
  customerId?: string;
  outletId?: string;
  amount: number;
}) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/coupons/validate", data, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error validating coupon:", error);
    return { success: false, message: "Failed to validate coupon" };
  }
};

// Offer API calls
export const getAllOffers = async (params?: {
  status?: string;
  type?: string;
  search?: string;
}) => {
  const token = getAdminToken();
  try {
    let url = "/v1/admin/offers";
    if (params) {
      const queryParams = new URLSearchParams();
      if (params.status) queryParams.append("status", params.status);
      if (params.type) queryParams.append("type", params.type);
      if (params.search) queryParams.append("search", params.search);
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }

    const response = await server.get(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching offers:", error);
    return { success: false, message: "Failed to fetch offers" };
  }
};

export const getOfferById = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/admin/offers/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching offer:", error);
    return { success: false, message: "Failed to fetch offer" };
  }
};

export const createOffer = async (offerData: any) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/offers", offerData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error creating offer:", error);
    return { success: false, message: "Failed to create offer" };
  }
};

export const updateOffer = async (id: string, offerData: any) => {
  const token = getAdminToken();
  try {
    const response = await server.put(`/v1/admin/offers/${id}`, offerData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error updating offer:", error);
    return { success: false, message: "Failed to update offer" };
  }
};

export const deleteOffer = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(`/v1/admin/offers/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error deleting offer:", error);
    return { success: false, message: "Failed to delete offer" };
  }
};

// Get applicable offers for an order (public endpoint)
export const getApplicableOffers = async (orderData: {
  outletId: string;
  orderAmount: number;
  customerId?: string;
  items: any[];
  foodChainId?: string;
}) => {
  try {
    const response = await server.post("/v1/offers/applicable", orderData);
    return response.data;
  } catch (error: any) {
    console.error("Error getting applicable offers:", error);
    return error.response?.data || { success: false, message: "Network error" };
  }
};

// Get active offers for display (public endpoint)
export const getActiveOffersForCustomers = async (params?: {
  outletId?: string;
  foodChainId?: string;
}) => {
  try {
    let url = "/v1/offers";
    if (params) {
      const queryParams = new URLSearchParams();
      if (params.outletId) queryParams.append("outletId", params.outletId);
      if (params.foodChainId)
        queryParams.append("foodChainId", params.foodChainId);
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }

    const response = await server.get(url);
    return response.data;
  } catch (error: any) {
    console.error("Error getting active offers:", error);
    return error.response?.data || { success: false, message: "Network error" };
  }
};

// Get offer analytics
export const getOfferAnalytics = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/admin/offers/${id}/analytics`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error getting offer analytics:", error);
    return error.response?.data || { success: false, message: "Network error" };
  }
};

// Campaign API calls
export const getAllCampaigns = async (params?: {
  status?: string;
  type?: string;
  search?: string;
}) => {
  const token = getAdminToken();
  try {
    let url = "/admin/campaigns";
    if (params) {
      const queryParams = new URLSearchParams();
      if (params.status) queryParams.append("status", params.status);
      if (params.type) queryParams.append("type", params.type);
      if (params.search) queryParams.append("search", params.search);
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }

    const response = await server.get(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching campaigns:", error);
    return { success: false, message: "Failed to fetch campaigns" };
  }
};

export const getCampaignById = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/admin/campaigns/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching campaign:", error);
    return { success: false, message: "Failed to fetch campaign" };
  }
};

export const createCampaign = async (campaignData: any) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/admin/campaigns", campaignData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error creating campaign:", error);
    return { success: false, message: "Failed to create campaign" };
  }
};

export const updateCampaign = async (id: string, campaignData: any) => {
  const token = getAdminToken();
  try {
    const response = await server.put(`/admin/campaigns/${id}`, campaignData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error updating campaign:", error);
    return { success: false, message: "Failed to update campaign" };
  }
};

export const deleteCampaign = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(`/admin/campaigns/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error deleting campaign:", error);
    return { success: false, message: "Failed to delete campaign" };
  }
};

export const sendCampaign = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/admin/campaigns/${id}/send`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error sending campaign:", error);
    return { success: false, message: "Failed to send campaign" };
  }
};

export const cancelCampaign = async (id: string) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/admin/campaigns/${id}/cancel`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error cancelling campaign:", error);
    return { success: false, message: "Failed to cancel campaign" };
  }
};

export const getCampaignRecipients = async (
  id: string,
  params?: {
    status?: string;
    page?: number;
    limit?: number;
  }
) => {
  const token = getAdminToken();
  try {
    let url = `/admin/campaigns/${id}/recipients`;
    if (params) {
      const queryParams = new URLSearchParams();
      if (params.status) queryParams.append("status", params.status);
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }

    const response = await server.get(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching campaign recipients:", error);
    return { success: false, message: "Failed to fetch campaign recipients" };
  }
};
