# Voice Input Troubleshooting Guide

## Common Issues and Solutions

### 1. "Failed to process audio transcription" with "fetch failed" error

**Symptoms:**
- Voice recording works locally but fails in production
- Error message: `{"success": false, "error": "Failed to process audio transcription", "details": "fetch failed"}`
- Mobile devices cannot connect to transcription service

**Root Cause:**
The frontend is trying to connect to `localhost:3001` in production, which doesn't work on mobile devices or deployed environments.

**Solution:**
1. **Update Environment Variables:**
   ```bash
   # In butler-web/.env
   NEXT_PUBLIC_BASE_URL=https://butler-be.vercel.app
   # or
   NEXT_PUBLIC_BASE_URL=https://butler-be.onrender.com
   ```

2. **Verify Backend Deployment:**
   - Ensure your backend is deployed and accessible
   - Test the backend URL directly: `https://your-backend-url/api/transcribe-audio/test`

3. **For Local Development with Mobile Testing:**
   ```bash
   # Use your local IP address instead of localhost
   NEXT_PUBLIC_BASE_URL=http://*************:3001
   ```

### 2. Groq API Key Issues

**Symptoms:**
- "Invalid or missing Groq API key" error
- Transcription fails even with correct backend URL

**Solution:**
1. **Check Backend Environment:**
   ```bash
   # In butler-be/.env
   GROQ_API_KEY=your_actual_groq_api_key_here
   ```

2. **Verify API Key:**
   - Log into Groq Console
   - Generate a new API key if needed
   - Ensure the key has proper permissions

### 3. Mobile-Specific Issues

**Symptoms:**
- Works on desktop but not on mobile
- Microphone permission issues
- Audio recording fails

**Solutions:**
1. **HTTPS Requirement:**
   - Mobile browsers require HTTPS for microphone access
   - Use ngrok or deploy to test on mobile: `ngrok http 3000`

2. **Audio Format Compatibility:**
   - The app supports multiple formats: webm, mp4, wav, ogg, m4a, aac
   - Mobile devices may use different formats

3. **Network Configuration:**
   - Use actual IP addresses, not localhost
   - Ensure both frontend and backend are accessible from mobile

### 4. Testing Steps

**1. Test Backend Service:**
```bash
# Run the test script
cd butler-web
node test-transcription.js
```

**2. Test Frontend API:**
```bash
curl http://localhost:3000/api/transcribe-audio
```

**3. Test Mobile Access:**
```bash
# Get your local IP
ipconfig getifaddr en0  # macOS
# or
hostname -I  # Linux

# Update .env with your IP
NEXT_PUBLIC_BASE_URL=http://YOUR_IP:3001
```

### 5. Environment Configuration Examples

**Development (Local):**
```bash
# butler-web/.env
NEXT_PUBLIC_BASE_URL=http://localhost:3001
```

**Development (Mobile Testing):**
```bash
# butler-web/.env
NEXT_PUBLIC_BASE_URL=http://*************:3001
```

**Production (Vercel):**
```bash
# butler-web/.env
NEXT_PUBLIC_BASE_URL=https://butler-be.vercel.app
```

**Production (Render):**
```bash
# butler-web/.env
NEXT_PUBLIC_BASE_URL=https://butler-be.onrender.com
```

### 6. Debugging Commands

**Check Environment Variables:**
```bash
echo $NEXT_PUBLIC_BASE_URL
```

**Test Backend Connectivity:**
```bash
curl https://butler-be.vercel.app/api/transcribe-audio/test
```

**Check Frontend Build:**
```bash
npm run build
npm run start
```

### 7. Fallback Behavior

The app has built-in fallback mechanisms:
1. **Primary:** Groq AI transcription (high accuracy)
2. **Fallback:** Browser speech recognition (lower accuracy)
3. **Manual:** Text input as last resort

If AI transcription fails, the app automatically falls back to browser speech recognition.

### 8. Common Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "fetch failed" | Wrong backend URL | Update NEXT_PUBLIC_BASE_URL |
| "Request timeout" | Slow network/server | Check network connection |
| "Service unavailable" | Backend down | Verify backend deployment |
| "Invalid API key" | Wrong Groq key | Update GROQ_API_KEY |
| "Microphone access denied" | No permissions | Enable microphone in browser |

### 9. Mobile Browser Compatibility

**Supported Browsers:**
- ✅ Chrome (Android/iOS)
- ✅ Safari (iOS)
- ✅ Firefox (Android)
- ✅ Edge (Android/iOS)

**Requirements:**
- HTTPS connection (for microphone access)
- Modern browser with MediaRecorder API support
- Microphone permissions granted
