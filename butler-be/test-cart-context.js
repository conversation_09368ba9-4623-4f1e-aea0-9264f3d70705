import { getActualCartContext } from './services/context-service.js';
import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ MongoDB connected for testing');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Test the cart context function
const testCartContext = async () => {
  try {
    await connectDB();
    
    // Test with sample IDs (you'll need to replace these with actual IDs from your database)
    const userId = '66a123456789abcdef123456'; // Replace with actual user ID
    const foodChainId = '66a123456789abcdef123457'; // Replace with actual food chain ID  
    const outletId = '66a123456789abcdef123458'; // Replace with actual outlet ID
    
    console.log('🧪 Testing cart context with:');
    console.log('User ID:', userId);
    console.log('Food Chain ID:', foodChainId);
    console.log('Outlet ID:', outletId);
    
    const cartContext = await getActualCartContext(userId, foodChainId, outletId);
    
    console.log('\n📊 Cart Context Result:');
    console.log('Total Items:', cartContext.totalItems);
    console.log('Is Empty:', cartContext.isEmpty);
    console.log('Categories:', cartContext.categories);
    console.log('Total Value:', cartContext.totalValue);
    console.log('Subtotal:', cartContext.subtotal);
    console.log('Final Total:', cartContext.finalTotal);
    console.log('Items:', Object.keys(cartContext.items).length);
    
    if (Object.keys(cartContext.items).length > 0) {
      console.log('\n🛒 Cart Items:');
      Object.values(cartContext.items).forEach(item => {
        console.log(`- ${item.dish.name}: ${item.quantity} x ₹${item.dish.price} = ₹${item.totalPrice}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('✅ MongoDB disconnected');
    process.exit(0);
  }
};

// Run the test
testCartContext();
