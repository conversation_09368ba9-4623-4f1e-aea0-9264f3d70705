import mongoose from "mongoose";
import Offer from "./models/Offer.js";
import Dish from "./models/Dish.js";
import Order from "./models/Order.js";
import FoodChain from "./models/FoodChain.js";
import Outlet from "./models/Outlet.js";
import User from "./models/User.js";
import Category from "./models/Category.js";
import { validateAndApplyOffers } from "./services/offer-validation-service.js";
import dotenv from "dotenv";

dotenv.config();

const testOfferScenarios = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB Atlas");

    console.log("\n=== TESTING OFFER APPLICATION SCENARIOS ===\n");

    // Get sample data
    const sampleDishes = await Dish.find({}).limit(5);
    const sampleOutlet = await Outlet.findOne({});
    const sampleFoodChain = await FoodChain.findOne({});
    const sampleUser = await User.findOne({});

    if (!sampleDishes.length || !sampleOutlet || !sampleFoodChain) {
      console.log("❌ Missing sample data");
      return;
    }

    console.log(`Using outlet: ${sampleOutlet.name}`);
    console.log(`Using food chain: ${sampleFoodChain.name}`);
    console.log(
      `Available dishes: ${sampleDishes.map((d) => d.name).join(", ")}`
    );

    // 1. Test dayOfWeek offer - activate and test for current day
    console.log("\n1. TESTING DAY OF WEEK OFFER...");

    const dayOfWeekOffer = await Offer.findOne({ offerType: "dayOfWeek" });
    if (dayOfWeekOffer) {
      console.log(`Found dayOfWeek offer: ${dayOfWeekOffer.name}`);

      // Activate the offer and set it for today
      const today = new Date().getDay(); // 0 = Sunday, 6 = Saturday
      dayOfWeekOffer.isActive = true;
      dayOfWeekOffer.discountDetails.timeRestrictions.daysOfWeek = [today];
      dayOfWeekOffer.applicableOutlets = [sampleOutlet._id];
      dayOfWeekOffer.foodChainId = sampleFoodChain._id;
      await dayOfWeekOffer.save();

      console.log(`✅ Activated for today (day ${today})`);

      // Test with order
      const testOrder = {
        outletId: sampleOutlet._id,
        foodChainId: sampleFoodChain._id,
        items: [
          {
            dishId: sampleDishes[0]._id,
            quantity: 2,
            price: sampleDishes[0].price,
            name: sampleDishes[0].name,
          },
        ],
        totalAmount: sampleDishes[0].price * 2,
      };

      console.log(
        `Test order: ${testOrder.items[0].name} x2 = ₹${testOrder.totalAmount}`
      );

      const result = await validateAndApplyOffers(testOrder, sampleUser?._id);
      console.log(
        `Result: ${result.appliedOffers?.length || 0} offers applied, ₹${
          result.totalDiscount || 0
        } discount`
      );

      if (result.appliedOffers?.length) {
        result.appliedOffers.forEach((offer) => {
          console.log(`  - ${offer.name}: ₹${offer.discount} discount`);
        });
      }
    }

    // 2. Test discount offer
    console.log("\n2. TESTING DISCOUNT OFFER...");

    const discountOffer = await Offer.findOne({ offerType: "discount" });
    if (discountOffer) {
      console.log(`Found discount offer: ${discountOffer.name}`);

      // Ensure it's properly configured
      discountOffer.isActive = true;
      discountOffer.applicableOutlets = [sampleOutlet._id];
      discountOffer.foodChainId = sampleFoodChain._id;
      discountOffer.discountDetails.minimumOrderValue = 100; // ₹100 minimum
      await discountOffer.save();

      // Test with order above minimum
      const testOrder = {
        outletId: sampleOutlet._id,
        foodChainId: sampleFoodChain._id,
        items: [
          {
            dishId: sampleDishes[0]._id,
            quantity: 3,
            price: sampleDishes[0].price,
            name: sampleDishes[0].name,
          },
        ],
        totalAmount: sampleDishes[0].price * 3,
      };

      console.log(
        `Test order: ${testOrder.items[0].name} x3 = ₹${testOrder.totalAmount}`
      );

      const result = await validateAndApplyOffers(testOrder, sampleUser?._id);
      console.log(
        `Result: ${result.appliedOffers?.length || 0} offers applied, ₹${
          result.totalDiscount || 0
        } discount`
      );

      if (result.appliedOffers?.length) {
        result.appliedOffers.forEach((offer) => {
          console.log(`  - ${offer.name}: ₹${offer.discount} discount`);
        });
      }
    }

    // 3. Test BOGO offer - fix and test
    console.log("\n3. TESTING BOGO OFFER...");

    const bogoOffer = await Offer.findOne({ offerType: "BOGO" });
    if (bogoOffer) {
      console.log(`Found BOGO offer: ${bogoOffer.name}`);

      // Fix BOGO configuration
      bogoOffer.isActive = true;
      bogoOffer.applicableOutlets = [sampleOutlet._id];
      bogoOffer.foodChainId = sampleFoodChain._id;
      bogoOffer.discountDetails.buyQuantity = 2;
      bogoOffer.discountDetails.getQuantity = 1;
      bogoOffer.applicableDishes = [sampleDishes[0]._id]; // Apply to first dish
      await bogoOffer.save();

      console.log(`✅ Configured as Buy 2 Get 1 for ${sampleDishes[0].name}`);

      // Test with qualifying order
      const testOrder = {
        outletId: sampleOutlet._id,
        foodChainId: sampleFoodChain._id,
        items: [
          {
            dishId: sampleDishes[0]._id,
            quantity: 3, // Should get 1 free
            price: sampleDishes[0].price,
            name: sampleDishes[0].name,
          },
        ],
        totalAmount: sampleDishes[0].price * 3,
      };

      console.log(
        `Test order: ${testOrder.items[0].name} x3 = ₹${testOrder.totalAmount}`
      );

      const result = await validateAndApplyOffers(testOrder, sampleUser?._id);
      console.log(
        `Result: ${result.appliedOffers?.length || 0} offers applied, ₹${
          result.totalDiscount || 0
        } discount`
      );

      if (result.appliedOffers?.length) {
        result.appliedOffers.forEach((offer) => {
          console.log(`  - ${offer.name}: ₹${offer.discount} discount`);
        });
      }
    }

    // 4. Test combo offer - fix and test
    console.log("\n4. TESTING COMBO OFFER...");

    const comboOffer = await Offer.findOne({ offerType: "combo" });
    if (comboOffer) {
      console.log(`Found combo offer: ${comboOffer.name}`);

      // Fix combo configuration
      comboOffer.isActive = true;
      comboOffer.applicableOutlets = [sampleOutlet._id];
      comboOffer.foodChainId = sampleFoodChain._id;
      comboOffer.discountDetails.comboItems = [
        { dishId: sampleDishes[0]._id, quantity: 1 },
        { dishId: sampleDishes[1]._id, quantity: 1 },
      ];
      const originalPrice = sampleDishes[0].price + sampleDishes[1].price;
      comboOffer.discountDetails.comboPrice = Math.round(originalPrice * 0.8); // 20% off
      await comboOffer.save();

      console.log(
        `✅ Configured combo: ${sampleDishes[0].name} + ${sampleDishes[1].name} = ₹${comboOffer.discountDetails.comboPrice} (was ₹${originalPrice})`
      );

      // Test with combo order
      const testOrder = {
        outletId: sampleOutlet._id,
        foodChainId: sampleFoodChain._id,
        items: [
          {
            dishId: sampleDishes[0]._id,
            quantity: 1,
            price: sampleDishes[0].price,
            name: sampleDishes[0].name,
          },
          {
            dishId: sampleDishes[1]._id,
            quantity: 1,
            price: sampleDishes[1].price,
            name: sampleDishes[1].name,
          },
        ],
        totalAmount: sampleDishes[0].price + sampleDishes[1].price,
      };

      console.log(
        `Test order: ${testOrder.items[0].name} + ${testOrder.items[1].name} = ₹${testOrder.totalAmount}`
      );

      const result = await validateAndApplyOffers(testOrder, sampleUser?._id);
      console.log(
        `Result: ${result.appliedOffers?.length || 0} offers applied, ₹${
          result.totalDiscount || 0
        } discount`
      );

      if (result.appliedOffers?.length) {
        result.appliedOffers.forEach((offer) => {
          console.log(`  - ${offer.name}: ₹${offer.discount} discount`);
        });
      }
    }

    // 5. Test order creation with offers
    console.log("\n5. TESTING ORDER CREATION WITH OFFERS...");

    const orderWithOffers = {
      outletId: sampleOutlet._id,
      foodChainId: sampleFoodChain._id,
      items: [
        {
          dishId: sampleDishes[0]._id,
          quantity: 2,
          price: sampleDishes[0].price,
          name: sampleDishes[0].name,
        },
      ],
      totalAmount: sampleDishes[0].price * 2,
    };

    const finalResult = await validateAndApplyOffers(
      orderWithOffers,
      sampleUser?._id
    );

    console.log("\n=== FINAL TEST RESULTS ===");
    console.log(`Original Amount: ₹${orderWithOffers.totalAmount}`);
    console.log(`Applied Offers: ${finalResult.appliedOffers?.length || 0}`);
    console.log(`Total Discount: ₹${finalResult.totalDiscount || 0}`);
    console.log(
      `Final Amount: ₹${finalResult.finalAmount || orderWithOffers.totalAmount}`
    );

    if (finalResult.appliedOffers?.length) {
      console.log("\nApplied Offers:");
      finalResult.appliedOffers.forEach((offer, index) => {
        console.log(
          `${index + 1}. ${offer.name} (${offer.type}): ₹${offer.discount}`
        );
      });
    }

    console.log("\n=== OFFER SCENARIOS TEST COMPLETED ===");
    process.exit(0);
  } catch (error) {
    console.error("Error during testing:", error);
    process.exit(1);
  }
};

testOfferScenarios();
