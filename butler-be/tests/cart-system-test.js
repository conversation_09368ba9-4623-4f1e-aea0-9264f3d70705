/**
 * Cart System Test Script
 * 
 * This script tests the backend cart system, offer application, and integration with existing features.
 * 
 * To run this test:
 * 1. Make sure the server is running
 * 2. Run: node tests/cart-system-test.js
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import Cart from '../models/Cart.js';
import Offer from '../models/Offer.js';
import User from '../models/User.js';
import Dish from '../models/Dish.js';
import { validateAndApplyOffersToCart } from '../services/offer-validation-service.js';

dotenv.config();

// Configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001/api/v1';
let authToken = '';
let userId = '';
let foodChainId = '';
let outletId = '';
let testDishId = '';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123',
};

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB connected');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Disconnect from MongoDB
const disconnectDB = async () => {
  try {
    await mongoose.disconnect();
    console.log('MongoDB disconnected');
  } catch (error) {
    console.error('MongoDB disconnection error:', error);
  }
};

// Login and get auth token
const login = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/user/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    const data = await response.json();
    if (data.success) {
      authToken = data.token;
      userId = data.user._id;
      console.log('✅ Login successful');
      return true;
    } else {
      console.error('❌ Login failed:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Login error:', error);
    return false;
  }
};

// Get food chain and outlet IDs
const getFoodChainAndOutlet = async () => {
  try {
    const user = await User.findById(userId).populate('foodChain');
    if (!user) {
      console.error('❌ User not found');
      return false;
    }

    foodChainId = user.foodChain._id.toString();
    
    // Get first outlet
    const outlets = await mongoose.model('Outlet').find({ foodChain: foodChainId });
    if (outlets.length === 0) {
      console.error('❌ No outlets found for food chain');
      return false;
    }

    outletId = outlets[0]._id.toString();
    console.log('✅ Got food chain and outlet IDs');
    return true;
  } catch (error) {
    console.error('❌ Error getting food chain and outlet:', error);
    return false;
  }
};

// Get a test dish
const getTestDish = async () => {
  try {
    const dish = await Dish.findOne({ foodChain: foodChainId, isAvailable: true });
    if (!dish) {
      console.error('❌ No dishes found for food chain');
      return false;
    }

    testDishId = dish._id.toString();
    console.log('✅ Got test dish ID');
    return true;
  } catch (error) {
    console.error('❌ Error getting test dish:', error);
    return false;
  }
};

// Test cart creation
const testCartCreation = async () => {
  try {
    // Clear any existing cart
    await Cart.deleteMany({ userId });
    
    // Create cart
    const cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId);
    
    if (cart && cart._id) {
      console.log('✅ Cart creation successful');
      return true;
    } else {
      console.error('❌ Cart creation failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Cart creation error:', error);
    return false;
  }
};

// Test adding item to cart
const testAddToCart = async () => {
  try {
    // Get cart
    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId);
    }
    
    // Get dish
    const dish = await Dish.findById(testDishId);
    if (!dish) {
      console.error('❌ Test dish not found');
      return false;
    }
    
    // Add item to cart
    await cart.addItem({
      _id: dish._id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      description: dish.description,
      category: dish.category,
    }, 1);
    
    // Verify item was added
    cart = await Cart.findById(cart._id);
    const hasItem = cart.items.some(item => item.dishId.toString() === testDishId);
    
    if (hasItem) {
      console.log('✅ Add to cart successful');
      return true;
    } else {
      console.error('❌ Add to cart failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Add to cart error:', error);
    return false;
  }
};

// Test updating item quantity
const testUpdateItemQuantity = async () => {
  try {
    // Get cart
    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      console.error('❌ Cart not found');
      return false;
    }
    
    // Update item quantity
    await cart.updateItemQuantity(testDishId, 2);
    
    // Verify quantity was updated
    cart = await Cart.findById(cart._id);
    const item = cart.items.find(item => item.dishId.toString() === testDishId);
    
    if (item && item.quantity === 2) {
      console.log('✅ Update item quantity successful');
      return true;
    } else {
      console.error('❌ Update item quantity failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Update item quantity error:', error);
    return false;
  }
};

// Test offer application
const testOfferApplication = async () => {
  try {
    // Get cart
    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      console.error('❌ Cart not found');
      return false;
    }
    
    // Get an active offer
    const offer = await Offer.findOne({ 
      foodChainId, 
      isActive: true,
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    });
    
    if (!offer) {
      console.log('⚠️ No active offers found, skipping offer application test');
      return true;
    }
    
    // Apply offer to cart
    const result = await validateAndApplyOffersToCart(cart);
    
    if (result.success) {
      console.log('✅ Offer application successful');
      return true;
    } else {
      console.error('❌ Offer application failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Offer application error:', error);
    return false;
  }
};

// Test removing item from cart
const testRemoveFromCart = async () => {
  try {
    // Get cart
    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      console.error('❌ Cart not found');
      return false;
    }
    
    // Remove item from cart
    await cart.removeItem(testDishId);
    
    // Verify item was removed
    cart = await Cart.findById(cart._id);
    const hasItem = cart.items.some(item => item.dishId.toString() === testDishId);
    
    if (!hasItem) {
      console.log('✅ Remove from cart successful');
      return true;
    } else {
      console.error('❌ Remove from cart failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Remove from cart error:', error);
    return false;
  }
};

// Test clearing cart
const testClearCart = async () => {
  try {
    // Get cart
    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      console.error('❌ Cart not found');
      return false;
    }
    
    // Clear cart
    await cart.clearCart();
    
    // Verify cart was cleared
    cart = await Cart.findById(cart._id);
    
    if (cart.items.length === 0) {
      console.log('✅ Clear cart successful');
      return true;
    } else {
      console.error('❌ Clear cart failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Clear cart error:', error);
    return false;
  }
};

// Run all tests
const runTests = async () => {
  try {
    await connectDB();
    
    // Setup
    const loginSuccess = await login();
    if (!loginSuccess) return;
    
    const foodChainSuccess = await getFoodChainAndOutlet();
    if (!foodChainSuccess) return;
    
    const dishSuccess = await getTestDish();
    if (!dishSuccess) return;
    
    // Tests
    await testCartCreation();
    await testAddToCart();
    await testUpdateItemQuantity();
    await testOfferApplication();
    await testRemoveFromCart();
    await testClearCart();
    
    console.log('\n✅ All tests completed');
  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await disconnectDB();
  }
};

// Run the tests
runTests();
