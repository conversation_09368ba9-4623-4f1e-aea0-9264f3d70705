import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { 
  smartPreFilter, 
  enhancedSemanticSearch, 
  optimizedAIProcessing 
} from '../services/optimized-recommendation-service.js';
import { getOptimizedRecommendations } from '../services/recommendation-orchestrator.js';
import { getFallbackRecommendations } from '../services/fallback-recommendation-service.js';
import { 
  recordRecommendationPerformance, 
  getPerformanceReport,
  resetPerformanceMetrics 
} from '../services/performance-monitoring-service.js';
import { 
  recommendationCache,
  invalidateCache,
  getCacheStatistics 
} from '../services/intelligent-cache-service.js';

/**
 * Comprehensive Test Suite for Optimized Recommendation System
 * Tests performance, accuracy, caching, and fallback mechanisms
 */

// Mock data for testing
const mockDishes = [
  {
    _id: '507f1f77bcf86cd799439011',
    name: 'Margherita Pizza',
    description: 'Classic pizza with tomato sauce, mozzarella, and basil',
    price: 299,
    category: { name: 'Pizza' },
    isVeg: true,
    isAvailable: true,
    cuisine: 'Italian',
    tags: ['vegetarian', 'classic', 'cheese'],
    ratings: { average: 4.5, count: 120 }
  },
  {
    _id: '507f1f77bcf86cd799439012',
    name: 'Chicken Biryani',
    description: 'Aromatic basmati rice with spiced chicken',
    price: 399,
    category: { name: 'Rice' },
    isVeg: false,
    isAvailable: true,
    cuisine: 'Indian',
    tags: ['non-vegetarian', 'spicy', 'rice'],
    ratings: { average: 4.8, count: 200 }
  },
  {
    _id: '507f1f77bcf86cd799439013',
    name: 'Paneer Butter Masala',
    description: 'Creamy tomato curry with cottage cheese',
    price: 249,
    category: { name: 'Curry' },
    isVeg: true,
    isAvailable: true,
    cuisine: 'Indian',
    tags: ['vegetarian', 'creamy', 'paneer'],
    ratings: { average: 4.3, count: 85 }
  },
  {
    _id: '507f1f77bcf86cd799439014',
    name: 'Caesar Salad',
    description: 'Fresh romaine lettuce with caesar dressing',
    price: 199,
    category: { name: 'Salad' },
    isVeg: true,
    isAvailable: true,
    cuisine: 'Continental',
    tags: ['vegetarian', 'healthy', 'fresh'],
    ratings: { average: 4.0, count: 45 }
  },
  {
    _id: '507f1f77bcf86cd799439015',
    name: 'Fish Curry',
    description: 'Spicy fish curry with coconut milk',
    price: 349,
    category: { name: 'Curry' },
    isVeg: false,
    isAvailable: true,
    cuisine: 'Indian',
    tags: ['non-vegetarian', 'spicy', 'fish'],
    ratings: { average: 4.6, count: 95 }
  }
];

const mockUserId = '507f1f77bcf86cd799439020';
const mockContext = {
  outletId: '507f1f77bcf86cd799439021',
  language: 'en'
};

describe('Optimized Recommendation System Tests', () => {
  
  beforeEach(() => {
    // Reset performance metrics before each test
    resetPerformanceMetrics();
    
    // Clear caches
    invalidateCache.clearAll();
  });

  afterEach(() => {
    // Clean up after each test
    invalidateCache.clearAll();
  });

  describe('Stage 1: Smart Pre-filtering', () => {
    
    it('should filter dishes based on dietary preferences', async () => {
      const vegetarianQuery = 'Show me some vegetarian options';
      
      const result = await smartPreFilter(
        vegetarianQuery,
        mockDishes,
        mockUserId,
        mockContext
      );
      
      expect(result.dishes).toBeDefined();
      expect(result.dishes.length).toBeGreaterThan(0);
      expect(result.dishes.every(dish => dish.isVeg)).toBe(true);
      expect(result.filterStats.reductionPercentage).toBeDefined();
    });

    it('should filter dishes based on price range', async () => {
      const priceQuery = 'Something under 250 rupees';
      
      const result = await smartPreFilter(
        priceQuery,
        mockDishes,
        mockUserId,
        mockContext
      );
      
      expect(result.dishes).toBeDefined();
      expect(result.dishes.every(dish => dish.price <= 250)).toBe(true);
    });

    it('should use caching for repeated queries', async () => {
      const query = 'Show me pizza options';
      
      // First call
      const result1 = await smartPreFilter(query, mockDishes, mockUserId, mockContext);
      expect(result1.fromCache).toBeFalsy();
      
      // Second call should be cached
      const result2 = await smartPreFilter(query, mockDishes, mockUserId, mockContext);
      expect(result2.fromCache).toBe(true);
    });

    it('should handle empty query gracefully', async () => {
      const result = await smartPreFilter('', mockDishes, mockUserId, mockContext);
      
      expect(result.dishes).toBeDefined();
      expect(result.dishes.length).toBeGreaterThan(0);
      expect(result.filterStats.error).toBeFalsy();
    });
  });

  describe('Stage 2: Enhanced Semantic Search', () => {
    
    it('should find relevant dishes based on keywords', async () => {
      const query = 'spicy chicken dish';
      const preFiltered = mockDishes.slice(0, 3);
      
      const result = await enhancedSemanticSearch(query, preFiltered, mockContext);
      
      expect(result.dishes).toBeDefined();
      expect(result.dishes.length).toBeGreaterThan(0);
      expect(result.searchStats.queryProcessed).toBe(true);
    });

    it('should return popular dishes when no relevant matches found', async () => {
      const query = 'xyz unknown dish';
      const preFiltered = mockDishes.slice(0, 3);
      
      const result = await enhancedSemanticSearch(query, preFiltered, mockContext);
      
      expect(result.dishes).toBeDefined();
      expect(result.dishes.length).toBeGreaterThan(0);
    });

    it('should use caching for semantic search results', async () => {
      const query = 'italian food';
      const preFiltered = mockDishes.slice(0, 3);
      
      // First call
      const result1 = await enhancedSemanticSearch(query, preFiltered, mockContext);
      expect(result1.fromCache).toBeFalsy();
      
      // Second call should be cached
      const result2 = await enhancedSemanticSearch(query, preFiltered, mockContext);
      expect(result2.fromCache).toBe(true);
    });
  });

  describe('Stage 3: Optimized AI Processing', () => {
    
    it('should process limited dishes instead of full menu', async () => {
      const query = 'recommend something delicious';
      const relevantDishes = mockDishes.slice(0, 3);
      
      const result = await optimizedAIProcessing(query, relevantDishes, mockContext);
      
      expect(result.aiResponse).toBeDefined();
      expect(result.tokenUsage.dishesProcessed).toBe(relevantDishes.length);
      expect(result.tokenUsage.reductionFromFullMenu).toBeDefined();
    });

    it('should handle different languages', async () => {
      const query = 'कुछ अच्छा खाना सुझाएं';
      const relevantDishes = mockDishes.slice(0, 2);
      const hindiContext = { ...mockContext, language: 'hi' };
      
      const result = await optimizedAIProcessing(query, relevantDishes, hindiContext);
      
      expect(result.aiResponse).toBeDefined();
      expect(result.aiResponse.detectedLanguage).toBe('hi');
    });

    it('should use AI response caching', async () => {
      const query = 'suggest popular dishes';
      const relevantDishes = mockDishes.slice(0, 2);
      
      // First call
      const result1 = await optimizedAIProcessing(query, relevantDishes, mockContext);
      expect(result1.fromCache).toBeFalsy();
      
      // Second call should be cached
      const result2 = await optimizedAIProcessing(query, relevantDishes, mockContext);
      expect(result2.fromCache).toBe(true);
    });
  });

  describe('Complete Orchestration', () => {
    
    it('should complete all 3 stages successfully', async () => {
      const query = 'I want something vegetarian and spicy';
      
      const result = await getOptimizedRecommendations(
        query,
        mockDishes,
        mockUserId,
        mockContext
      );
      
      expect(result.recommendations).toBeDefined();
      expect(result.recommendations.length).toBeGreaterThan(0);
      expect(result.performance.stagesCompleted).toBe(3);
      expect(result.performance.dishesProcessed.original).toBe(mockDishes.length);
      expect(result.performance.tokenOptimization.reductionFromFullMenu).toBeDefined();
      expect(result.fallbackUsed).toBe(false);
    });

    it('should measure performance correctly', async () => {
      const query = 'recommend popular dishes';
      
      const result = await getOptimizedRecommendations(
        query,
        mockDishes,
        mockUserId,
        mockContext
      );
      
      expect(result.performance.responseTime).toBeGreaterThan(0);
      expect(result.performance.tokenOptimization.estimatedTokens).toBeGreaterThan(0);
      expect(result.performance.dishesProcessed.sentToAI).toBeLessThan(mockDishes.length);
    });
  });

  describe('Fallback Mechanisms', () => {
    
    it('should provide fallback recommendations when AI fails', async () => {
      const query = 'suggest something good';
      
      const result = await getFallbackRecommendations(
        query,
        mockDishes,
        mockUserId,
        mockContext
      );
      
      expect(result.recommendations).toBeDefined();
      expect(result.recommendations.length).toBeGreaterThan(0);
      expect(result.fallbackUsed).toBe(true);
      expect(result.fallbackMethod).toBeDefined();
    });

    it('should handle different fallback strategies', async () => {
      const queries = [
        'vegetarian options',
        'popular dishes',
        'spicy food',
        'cheap meals'
      ];
      
      for (const query of queries) {
        const result = await getFallbackRecommendations(
          query,
          mockDishes,
          mockUserId,
          mockContext
        );
        
        expect(result.recommendations.length).toBeGreaterThan(0);
        expect(result.fallbackMethod).toBeDefined();
      }
    });
  });

  describe('Performance Monitoring', () => {
    
    it('should record performance metrics correctly', () => {
      const performanceData = {
        responseTime: 1500,
        tokenUsage: { estimatedTokens: 150, tokensSaved: 850 },
        cacheHits: 2,
        cacheMisses: 1,
        dishesProcessed: { original: 200, sentToAI: 12 },
        recommendationCount: 5,
        fallbackUsed: false,
        optimizationStage: 3,
        userQuery: 'test query',
        userId: mockUserId
      };
      
      recordRecommendationPerformance(performanceData);
      
      const report = getPerformanceReport();
      
      expect(report.requestPatterns.totalRequests).toBe(1);
      expect(report.responseTimes.averageResponseTime).toBe(1500);
      expect(report.tokenUsage.totalTokensUsed).toBe(150);
      expect(report.tokenUsage.totalTokensSaved).toBe(850);
    });

    it('should calculate optimization metrics', () => {
      // Record multiple requests
      for (let i = 0; i < 5; i++) {
        recordRecommendationPerformance({
          responseTime: 1000 + i * 100,
          tokenUsage: { estimatedTokens: 100 + i * 10, tokensSaved: 900 - i * 10 },
          cacheHits: i % 2,
          cacheMisses: (i + 1) % 2,
          dishesProcessed: { original: 200, sentToAI: 10 + i },
          recommendationCount: 5,
          fallbackUsed: i === 4,
          optimizationStage: 3,
          userQuery: `test query ${i}`,
          userId: mockUserId
        });
      }
      
      const report = getPerformanceReport();
      
      expect(report.requestPatterns.totalRequests).toBe(5);
      expect(report.tokenUsage.tokenSavingsPercentage).toBeGreaterThan(80);
      expect(report.recommendationAccuracy.fallbackUsageRate).toBe(0.2);
    });
  });

  describe('Caching System', () => {
    
    it('should cache and retrieve data correctly', () => {
      const testData = { test: 'data', timestamp: new Date() };
      
      // Cache data
      const cached = recommendationCache.cacheQuery(
        mockUserId,
        'test query',
        testData,
        mockContext
      );
      
      expect(cached).toBe(true);
      
      // Retrieve data
      const retrieved = recommendationCache.getQuery(
        mockUserId,
        'test query',
        mockContext
      );
      
      expect(retrieved).toBeDefined();
      expect(retrieved.results.test).toBe('data');
    });

    it('should invalidate cache correctly', () => {
      // Cache some data
      recommendationCache.cacheQuery(mockUserId, 'test', { data: 'test' }, mockContext);
      
      // Verify it's cached
      let cached = recommendationCache.getQuery(mockUserId, 'test', mockContext);
      expect(cached).toBeDefined();
      
      // Invalidate user cache
      const invalidated = invalidateCache.userDataChanged(mockUserId);
      expect(invalidated).toBeGreaterThan(0);
      
      // Verify it's no longer cached
      cached = recommendationCache.getQuery(mockUserId, 'test', mockContext);
      expect(cached).toBeNull();
    });

    it('should provide cache statistics', () => {
      // Perform some cache operations
      recommendationCache.cacheQuery(mockUserId, 'query1', { data: 1 }, mockContext);
      recommendationCache.cacheQuery(mockUserId, 'query2', { data: 2 }, mockContext);
      recommendationCache.getQuery(mockUserId, 'query1', mockContext);
      recommendationCache.getQuery(mockUserId, 'nonexistent', mockContext);
      
      const stats = getCacheStatistics();
      
      expect(stats.hits.total).toBeGreaterThan(0);
      expect(stats.misses.total).toBeGreaterThan(0);
      expect(stats.sets.total).toBeGreaterThan(0);
    });
  });

  describe('Integration Tests', () => {
    
    it('should handle complete user journey', async () => {
      const userQueries = [
        'Show me vegetarian options',
        'Something spicy under 300 rupees',
        'Popular dishes',
        'Italian food'
      ];
      
      for (const query of userQueries) {
        const result = await getOptimizedRecommendations(
          query,
          mockDishes,
          mockUserId,
          mockContext
        );
        
        expect(result.recommendations).toBeDefined();
        expect(result.recommendations.length).toBeGreaterThan(0);
        expect(result.performance).toBeDefined();
        
        // Record performance for each request
        recordRecommendationPerformance({
          responseTime: result.performance.responseTime,
          tokenUsage: result.performance.tokenOptimization,
          cacheHits: Object.values(result.performance.cacheUsage || {}).filter(Boolean).length,
          cacheMisses: Object.values(result.performance.cacheUsage || {}).filter(hit => !hit).length,
          dishesProcessed: result.performance.dishesProcessed,
          recommendationCount: result.recommendations.length,
          fallbackUsed: result.fallbackUsed,
          optimizationStage: result.performance.stagesCompleted,
          userQuery: query,
          userId: mockUserId
        });
      }
      
      const finalReport = getPerformanceReport();
      
      expect(finalReport.requestPatterns.totalRequests).toBe(userQueries.length);
      expect(finalReport.tokenUsage.tokenSavingsPercentage).toBeGreaterThan(50);
    });
  });
});
