/**
 * Comprehensive Test Scenarios for AI Conversation System
 *
 * This file contains test cases for various user scenarios to ensure
 * the AI conversation system works reliably in all cases.
 */

import { detectCartOperation } from "../services/cart-service.js";
import { AgenticResponseGenerator } from "../services/agentic-response-service.js";

// Test data
const mockDishes = [
  {
    _id: "1",
    name: "Classic Chicken Wrap",
    price: 297,
    category: { name: "Wraps" },
    isAvailable: true,
  },
  {
    _id: "2",
    name: "Veggie Burger",
    price: 199,
    category: { name: "Burgers" },
    isAvailable: true,
  },
  {
    _id: "3",
    name: "Margherita Pizza",
    price: 399,
    category: { name: "Pizza" },
    isAvailable: true,
  },
];

/**
 * Test Scenarios for Intent Classification
 */
export const intentClassificationTests = [
  // Informational Queries (should NOT trigger cart operations)
  {
    category: "Informational - Recommendations",
    queries: [
      "What is the best thing available at the restaurant?",
      "What do you recommend?",
      "Show me your popular dishes",
      "What's good here?",
      "Tell me about your menu",
      "What are your specialties?",
    ],
    expectedIntentType: "information",
    expectedCartOperation: null,
    expectedResponse: "Should provide recommendations without cart operations",
  },

  {
    category: "Informational - Dietary",
    queries: [
      "Do you have vegetarian options?",
      "What vegan dishes do you have?",
      "Show me gluten-free items",
      "Any dairy-free options?",
      "What's spicy on the menu?",
    ],
    expectedIntentType: "information",
    expectedCartOperation: null,
    expectedResponse: "Should filter and show relevant dishes",
  },

  {
    category: "Informational - Pricing",
    queries: [
      "How much does the chicken wrap cost?",
      "What are your cheapest items?",
      "Show me budget-friendly options",
      "What's the price of pizza?",
    ],
    expectedIntentType: "information",
    expectedCartOperation: null,
    expectedResponse: "Should show pricing information",
  },

  // Action-Based Queries (should trigger cart operations)
  {
    category: "Action - Single Add",
    queries: [
      "Add chicken wrap to my cart",
      "I want a veggie burger",
      "Can I get a pizza?",
      "I'll take the chicken wrap",
      "Give me one margherita pizza",
    ],
    expectedIntentType: "action",
    expectedCartOperation: { operation: "add", quantity: 1 },
    expectedResponse: "Should add item to cart with quantity 1",
  },

  {
    category: "Action - Single Add with Quantity",
    queries: [
      "Add 2 chicken wraps to my cart",
      "I want 3 veggie burgers",
      "Can I get 2 pizzas?",
      "Give me 4 chicken wraps",
    ],
    expectedIntentType: "action",
    expectedCartOperation: { operation: "add", quantity: ">1" },
    expectedResponse: "Should add items with specified quantity",
  },

  {
    category: "Action - Remove",
    queries: [
      "Remove chicken wrap from my cart",
      "Delete the veggie burger",
      "I don't want the pizza anymore",
      "Take out the chicken wrap",
      "Cancel the veggie burger",
    ],
    expectedIntentType: "action",
    expectedCartOperation: { operation: "remove" },
    expectedResponse: "Should remove item from cart",
  },

  {
    category: "Action - Multiple Operations",
    queries: [
      "Add chicken wrap and remove veggie burger",
      "Remove pizza and add chicken wrap",
      "Add chicken wrap and veggie burger",
      "I want chicken wrap but remove the pizza",
    ],
    expectedIntentType: "action",
    expectedCartOperation: "multiple",
    expectedResponse: "Should handle multiple operations sequentially",
  },
];

/**
 * Test Scenarios for Cart Operation Detection
 */
export const cartOperationDetectionTests = [
  // Single operations
  {
    message: "Add chicken wrap to my cart",
    expected: { operation: "add", item: "chicken wrap", quantity: 1 },
  },
  {
    message: "Add 2 chicken wraps",
    expected: { operation: "add", item: "chicken wraps", quantity: 2 },
  },
  {
    message: "Remove veggie burger from cart",
    expected: { operation: "remove", item: "veggie burger", quantity: 1 },
  },
  {
    message: "Clear my cart",
    expected: { operation: "clear", item: null, quantity: 0 },
  },

  // Multiple operations
  {
    message: "Add chicken wrap and remove veggie burger",
    expected: [
      { operation: "add", item: "chicken wrap", quantity: 1 },
      { operation: "remove", item: "veggie burger", quantity: 1 },
    ],
  },
  {
    message: "Add chicken wrap and pizza",
    expected: [
      { operation: "add", item: "chicken wrap", quantity: 1 },
      { operation: "add", item: "pizza", quantity: 1 },
    ],
  },

  // Non-cart operations (should return null)
  {
    message: "What is the best dish?",
    expected: null,
  },
  {
    message: "Show me vegetarian options",
    expected: null,
  },
  {
    message: "How much does pizza cost?",
    expected: null,
  },
];

/**
 * Test Scenarios for Edge Cases
 */
export const edgeCaseTests = [
  {
    category: "Ambiguous Queries",
    queries: [
      "chicken wrap", // Should be treated as informational
      "pizza?", // Should be treated as informational
      "I like burgers", // Should be treated as informational
      "burgers are good", // Should be treated as informational
    ],
    expectedBehavior:
      "Should default to informational intent and show recommendations",
  },

  {
    category: "Typos and Variations",
    queries: [
      "Add chiken wrap", // Typo in dish name
      "I want veggi burger", // Typo in dish name
      "Give me piza", // Typo in dish name
      "Add chicken rap", // Similar sounding dish
    ],
    expectedBehavior: "Should use fuzzy matching to find closest dish",
  },

  {
    category: "Complex Sentences",
    queries: [
      "I think I want to add chicken wrap to my cart, but I'm not sure",
      "Maybe I should get a veggie burger, what do you think?",
      "Can you please add chicken wrap to my cart if it's available?",
    ],
    expectedBehavior: "Should extract the core intent despite complexity",
  },

  {
    category: "Quantity Edge Cases",
    queries: [
      "Add zero chicken wraps", // Should be treated as invalid
      "Add -1 veggie burger", // Should be treated as invalid
      "Add 100 pizzas", // Large quantity
      "Add one chicken wrap", // Text quantity
    ],
    expectedBehavior: "Should handle invalid quantities gracefully",
  },
];

/**
 * Test Scenarios for Message Duplication Prevention
 */
export const duplicationPreventionTests = [
  {
    scenario: "Rapid successive identical requests",
    sequence: [
      "Add chicken wrap to cart",
      "Add chicken wrap to cart", // Duplicate within 10 seconds
    ],
    expectedBehavior:
      "Second request should be detected as duplicate and not processed",
  },

  {
    scenario: "Similar but different requests",
    sequence: [
      "Add chicken wrap to cart",
      "Add 2 chicken wraps to cart", // Different quantity
    ],
    expectedBehavior: "Both requests should be processed as they are different",
  },

  {
    scenario: "Same dish, different operations",
    sequence: ["Add chicken wrap to cart", "Remove chicken wrap from cart"],
    expectedBehavior: "Both operations should be processed",
  },
];

/**
 * Test Runner Functions
 */
export const runIntentClassificationTests = async () => {
  console.log("🧪 Running Intent Classification Tests...");

  const generator = new AgenticResponseGenerator(
    "test-user",
    "test-outlet",
    "test-chain",
    "en"
  );

  for (const testGroup of intentClassificationTests) {
    console.log(`\n📋 Testing: ${testGroup.category}`);

    for (const query of testGroup.queries) {
      const intentType = generator.classifyIntentType(query.toLowerCase());
      const cartOperation =
        intentType === "action" ? detectCartOperation(query) : null;

      console.log(`Query: "${query}"`);
      console.log(
        `Expected Intent: ${testGroup.expectedIntentType}, Got: ${intentType}`
      );
      console.log(`Cart Operation: ${cartOperation ? "Detected" : "None"}`);

      // Validate results
      if (intentType !== testGroup.expectedIntentType) {
        console.error(`❌ FAIL: Intent mismatch for "${query}"`);
      } else if (
        testGroup.expectedCartOperation === null &&
        cartOperation !== null
      ) {
        console.error(`❌ FAIL: Unexpected cart operation for "${query}"`);
      } else {
        console.log(`✅ PASS`);
      }
    }
  }
};

export const runCartOperationDetectionTests = async () => {
  console.log("\n🧪 Running Cart Operation Detection Tests...");

  for (const test of cartOperationDetectionTests) {
    console.log(`\nTesting: "${test.message}"`);
    const result = await detectCartOperation(test.message);

    // Compare results
    if (JSON.stringify(result) === JSON.stringify(test.expected)) {
      console.log(`✅ PASS`);
    } else {
      console.error(
        `❌ FAIL: Expected ${JSON.stringify(
          test.expected
        )}, Got ${JSON.stringify(result)}`
      );
    }
  }
};

/**
 * Main test runner
 */
export const runAllTests = async () => {
  console.log("🚀 Starting AI Conversation System Tests...\n");

  await runIntentClassificationTests();
  await runCartOperationDetectionTests();

  console.log("\n✨ All tests completed!");
};

// Export for use in other test files
export default {
  intentClassificationTests,
  cartOperationDetectionTests,
  edgeCaseTests,
  duplicationPreventionTests,
  runAllTests,
};
