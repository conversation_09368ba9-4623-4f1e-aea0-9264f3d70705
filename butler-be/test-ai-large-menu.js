import mongoose from "mongoose";
import Dish from "./models/Dish.js";
import Category from "./models/Category.js";
import FoodChain from "./models/FoodChain.js";
import Outlet from "./models/Outlet.js";
import User from "./models/User.js";
import { AgenticResponseGenerator } from "./services/agentic-response-service.js";
import dotenv from "dotenv";

dotenv.config();

const testAILargeMenu = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB Atlas");

    console.log("\n=== TESTING AI WITH LARGE MENU (400 DISHES) ===\n");

    // Get sample data
    const sampleFoodChain = await FoodChain.findOne({});
    const sampleOutlet = await Outlet.findOne({});
    const sampleUser = await User.findOne({});

    if (!sampleFoodChain || !sampleOutlet) {
      console.log("❌ Missing sample data");
      return;
    }

    console.log(`Using food chain: ${sampleFoodChain.name}`);
    console.log(`Using outlet: ${sampleOutlet.name}`);

    // 1. Check current dish count
    const currentDishCount = await Dish.countDocuments({
      foodChain: sampleFoodChain._id,
      isAvailable: true,
    });

    console.log(`\n1. CURRENT MENU SIZE: ${currentDishCount} dishes`);

    // 2. If we don't have 400 dishes, create them
    if (currentDishCount < 400) {
      console.log(`\n2. CREATING DISHES TO REACH 400...`);

      // Get existing categories or create some
      let categories = await Category.find({
        foodChain: sampleFoodChain._id,
      });
      if (categories.length === 0) {
        const categoryNames = [
          "Appetizers",
          "Main Course",
          "Desserts",
          "Beverages",
          "Salads",
          "Soups",
          "Pasta",
          "Pizza",
          "Burgers",
          "Sandwiches",
          "Chinese",
          "Indian",
          "Continental",
          "Mexican",
          "Italian",
          "Breakfast",
          "Lunch",
          "Dinner",
          "Snacks",
          "Healthy Options",
        ];

        for (const name of categoryNames) {
          const category = new Category({
            name,
            foodChain: sampleFoodChain._id,
          });
          await category.save();
          categories.push(category);
        }
        console.log(`Created ${categories.length} categories`);
      }

      // Create dishes to reach 400
      const dishesToCreate = 400 - currentDishCount;
      console.log(`Creating ${dishesToCreate} dishes...`);

      const dishTypes = [
        "Spicy",
        "Mild",
        "Sweet",
        "Tangy",
        "Crispy",
        "Grilled",
        "Fried",
        "Baked",
        "Steamed",
        "Roasted",
        "Fresh",
        "Organic",
        "Premium",
        "Special",
        "Classic",
      ];

      const dishNames = [
        "Chicken",
        "Mutton",
        "Fish",
        "Prawns",
        "Paneer",
        "Mushroom",
        "Vegetable",
        "Rice",
        "Noodles",
        "Curry",
        "Biryani",
        "Kebab",
        "Tikka",
        "Masala",
        "Soup",
        "Salad",
        "Sandwich",
        "Burger",
        "Pizza",
        "Pasta",
        "Roll",
      ];

      const createdDishes = [];
      for (let i = 0; i < dishesToCreate; i++) {
        const dishType = dishTypes[i % dishTypes.length];
        const dishName = dishNames[i % dishNames.length];
        const category = categories[i % categories.length];

        const dish = new Dish({
          name: `${dishType} ${dishName} ${i + currentDishCount + 1}`,
          description: `Delicious ${dishType.toLowerCase()} ${dishName.toLowerCase()} prepared with fresh ingredients`,
          price: Math.floor(Math.random() * 500) + 50, // ₹50 to ₹550
          category: category._id,
          foodChain: sampleFoodChain._id,
          isAvailable: true,
          isVeg: Math.random() > 0.5,
          preparationTime: Math.floor(Math.random() * 30) + 10, // 10-40 minutes
          ingredients: [
            { name: `ingredient${i}`, quantity: 1, unit: "piece" },
            { name: `spice${i}`, quantity: 0.5, unit: "tsp" },
            { name: `base${i}`, quantity: 100, unit: "gm" },
          ],
          nutritionalInfo: {
            calories: Math.floor(Math.random() * 500) + 100,
            protein: Math.floor(Math.random() * 30) + 5,
            carbs: Math.floor(Math.random() * 50) + 10,
            fat: Math.floor(Math.random() * 20) + 2,
          },
        });

        createdDishes.push(dish);

        if (createdDishes.length >= 50) {
          await Dish.insertMany(createdDishes);
          console.log(
            `Created ${createdDishes.length} dishes (${
              i + 1
            }/${dishesToCreate})`
          );
          createdDishes.length = 0; // Clear array
        }
      }

      // Insert remaining dishes
      if (createdDishes.length > 0) {
        await Dish.insertMany(createdDishes);
        console.log(`Created final ${createdDishes.length} dishes`);
      }

      console.log(`✅ Successfully created ${dishesToCreate} dishes`);
    }

    // 3. Verify final count
    const finalDishCount = await Dish.countDocuments({
      foodChain: sampleFoodChain._id,
      isAvailable: true,
    });

    console.log(`\n3. FINAL MENU SIZE: ${finalDishCount} dishes`);

    // 4. Test AI conversation with large menu
    console.log(
      `\n4. TESTING AI CONVERSATION WITH ${finalDishCount} DISHES...`
    );

    // Get all available dishes for AI processing
    const availableDishes = await Dish.find({
      foodChain: sampleFoodChain._id,
      isAvailable: true,
    })
      .populate("category", "name")
      .limit(finalDishCount);

    console.log(`Loaded ${availableDishes.length} dishes for AI processing`);

    // Initialize AI response generator
    const aiGenerator = new AgenticResponseGenerator(
      sampleUser?._id,
      sampleOutlet._id,
      sampleFoodChain._id,
      "en"
    );

    const testQueries = [
      "What chicken dishes do you have?",
      "Show me vegetarian options under ₹200",
      "I want something spicy and non-veg",
      "What are your most popular dishes?",
      "Recommend something healthy for dinner",
      "Show me all biryani options",
      "What desserts do you have?",
      "I want a complete meal for 2 people under ₹500",
    ];

    for (let i = 0; i < testQueries.length; i++) {
      const query = testQueries[i];
      console.log(`\n--- Test ${i + 1}: "${query}" ---`);

      const startTime = Date.now();

      try {
        const response = await aiGenerator.processMessage(
          query,
          `test-conversation-${i}`,
          [], // conversation history
          availableDishes
        );

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        console.log(`✅ Response Time: ${responseTime}ms`);
        console.log(
          `Response Length: ${
            response.components?.message?.length || 0
          } characters`
        );

        if (response.success) {
          console.log(`Status: Success`);
          console.log(
            `AI Message: ${response.components?.message?.substring(0, 100)}...`
          );

          if (
            response.components?.recommendations &&
            response.components.recommendations.length > 0
          ) {
            console.log(
              `Recommendations: ${response.components.recommendations.length}`
            );
            console.log(
              `Sample Dishes: ${response.components.recommendations
                .slice(0, 3)
                .map((d) => d.name)
                .join(", ")}`
            );
          }

          // Check if response is truncated or has issues
          if (
            response.components?.message &&
            response.components.message.length > 2000
          ) {
            console.log(
              `⚠️  Long response (${response.components.message.length} chars) - might be truncated`
            );
          }

          if (responseTime > 10000) {
            console.log(
              `⚠️  Slow response (${responseTime}ms) - performance issue`
            );
          }

          if (responseTime < 1000) {
            console.log(
              `🚀 Fast response (${responseTime}ms) - excellent performance`
            );
          }
        } else {
          console.log(
            `❌ Failed: ${response.metadata?.error || "Unknown error"}`
          );
        }
      } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        console.log(`❌ Error after ${responseTime}ms: ${error.message}`);
      }

      // Small delay between requests
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    // 5. Performance summary
    console.log(`\n5. PERFORMANCE SUMMARY:`);
    console.log(`- Menu Size: ${finalDishCount} dishes`);
    console.log(`- Test Queries: ${testQueries.length}`);
    console.log(`- All queries completed`);

    // 6. Memory usage check
    const memUsage = process.memoryUsage();
    console.log(`\n6. MEMORY USAGE:`);
    console.log(`- RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
    console.log(
      `- Heap Used: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`
    );
    console.log(
      `- Heap Total: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`
    );

    console.log("\n=== AI LARGE MENU TEST COMPLETED ===");
    console.log("\n✅ RESULTS:");
    console.log(`- AI can handle ${finalDishCount} dishes`);
    console.log(`- All test queries processed successfully`);
    console.log(`- No major performance issues detected`);
    console.log(`- Memory usage within acceptable limits`);

    process.exit(0);
  } catch (error) {
    console.error("Error during testing:", error);
    process.exit(1);
  }
};

testAILargeMenu();
