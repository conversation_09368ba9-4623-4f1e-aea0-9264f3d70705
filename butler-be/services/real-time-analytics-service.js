import { WebSocketServer } from 'ws';
import { 
  getPerformanceReport, 
  performanceHealthCheck 
} from './performance-monitoring-service.js';
import { 
  getCacheStatistics 
} from './intelligent-cache-service.js';
import { 
  getABTestAnalysis 
} from './ab-testing-service.js';

/**
 * Real-time Analytics Service
 * Provides WebSocket-based live monitoring for optimization metrics
 */

// WebSocket server instance
let wss = null;
let analyticsInterval = null;
let connectedClients = new Set();

// Real-time metrics buffer
let realtimeMetrics = {
  requestsPerMinute: [],
  responseTimesPerMinute: [],
  tokenUsagePerMinute: [],
  cacheHitRatePerMinute: [],
  errorRatePerMinute: [],
  lastMinuteRequests: 0,
  lastMinuteTokens: 0,
  lastMinuteErrors: 0,
  bufferSize: 60 // Keep 60 minutes of data
};

/**
 * Initialize WebSocket server for real-time analytics
 * @param {Object} server - HTTP server instance
 * @param {number} port - WebSocket port (optional)
 */
export const initializeRealTimeAnalytics = (server, port = null) => {
  try {
    // Create WebSocket server
    if (port) {
      wss = new WebSocketServer({ port });
      console.log(`📊 Real-time analytics WebSocket server started on port ${port}`);
    } else {
      wss = new WebSocketServer({ server });
      console.log('📊 Real-time analytics WebSocket server attached to HTTP server');
    }

    // Handle WebSocket connections
    wss.on('connection', (ws, request) => {
      console.log('📱 New analytics client connected');
      connectedClients.add(ws);

      // Send initial data to new client
      sendInitialData(ws);

      // Handle client messages
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          handleClientMessage(ws, data);
        } catch (error) {
          console.error('Error parsing client message:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        console.log('📱 Analytics client disconnected');
        connectedClients.delete(ws);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        connectedClients.delete(ws);
      });
    });

    // Start real-time metrics collection
    startMetricsCollection();

    return { success: true, port: port || 'attached' };
  } catch (error) {
    console.error('Error initializing real-time analytics:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send initial dashboard data to new client
 */
const sendInitialData = async (ws) => {
  try {
    const performanceReport = getPerformanceReport();
    const cacheStats = getCacheStatistics();
    const abTestAnalysis = getABTestAnalysis();
    const healthCheck = performanceHealthCheck();

    const initialData = {
      type: 'initial_data',
      data: {
        performance: performanceReport,
        cache: cacheStats,
        abTest: abTestAnalysis,
        health: healthCheck,
        realtime: realtimeMetrics,
        timestamp: new Date()
      }
    };

    ws.send(JSON.stringify(initialData));
  } catch (error) {
    console.error('Error sending initial data:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Error loading initial data'
    }));
  }
};

/**
 * Handle messages from clients
 */
const handleClientMessage = async (ws, data) => {
  try {
    switch (data.type) {
      case 'subscribe':
        // Client wants to subscribe to specific metrics
        ws.subscriptions = data.metrics || ['all'];
        ws.send(JSON.stringify({
          type: 'subscription_confirmed',
          metrics: ws.subscriptions
        }));
        break;

      case 'get_current_metrics':
        // Client requests current metrics
        const currentMetrics = await getCurrentMetrics();
        ws.send(JSON.stringify({
          type: 'current_metrics',
          data: currentMetrics
        }));
        break;

      case 'get_ab_test_status':
        // Client requests A/B test status
        const abTestStatus = getABTestAnalysis();
        ws.send(JSON.stringify({
          type: 'ab_test_status',
          data: abTestStatus
        }));
        break;

      case 'ping':
        // Heartbeat from client
        ws.send(JSON.stringify({
          type: 'pong',
          timestamp: new Date()
        }));
        break;

      default:
        ws.send(JSON.stringify({
          type: 'error',
          message: `Unknown message type: ${data.type}`
        }));
    }
  } catch (error) {
    console.error('Error handling client message:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Error processing request'
    }));
  }
};

/**
 * Start collecting real-time metrics
 */
const startMetricsCollection = () => {
  // Collect metrics every minute
  analyticsInterval = setInterval(async () => {
    try {
      await collectAndBroadcastMetrics();
    } catch (error) {
      console.error('Error collecting real-time metrics:', error);
    }
  }, 60000); // 1 minute

  console.log('📊 Real-time metrics collection started');
};

/**
 * Collect current metrics and broadcast to clients
 */
const collectAndBroadcastMetrics = async () => {
  try {
    const performanceReport = getPerformanceReport();
    const cacheStats = getCacheStatistics();
    const currentTime = new Date();

    // Calculate per-minute metrics
    const currentRequests = performanceReport.requestPatterns.totalRequests;
    const currentTokens = performanceReport.tokenUsage.totalTokensUsed;
    const currentErrors = performanceReport.errorTracking.totalErrors;

    const requestsThisMinute = currentRequests - realtimeMetrics.lastMinuteRequests;
    const tokensThisMinute = currentTokens - realtimeMetrics.lastMinuteTokens;
    const errorsThisMinute = currentErrors - realtimeMetrics.lastMinuteErrors;

    // Update real-time metrics buffer
    updateMetricsBuffer({
      requestsPerMinute: requestsThisMinute,
      avgResponseTime: performanceReport.responseTimes.averageResponseTime,
      tokenUsage: tokensThisMinute,
      cacheHitRate: cacheStats.hitRate,
      errorRate: requestsThisMinute > 0 ? (errorsThisMinute / requestsThisMinute) * 100 : 0
    });

    // Update last minute counters
    realtimeMetrics.lastMinuteRequests = currentRequests;
    realtimeMetrics.lastMinuteTokens = currentTokens;
    realtimeMetrics.lastMinuteErrors = currentErrors;

    // Broadcast to all connected clients
    const updateData = {
      type: 'metrics_update',
      data: {
        timestamp: currentTime,
        realtime: realtimeMetrics,
        performance: {
          totalRequests: currentRequests,
          averageResponseTime: performanceReport.responseTimes.averageResponseTime,
          tokenSavingsPercentage: performanceReport.tokenUsage.tokenSavingsPercentage,
          cacheHitRate: cacheStats.hitRate,
          fallbackUsageRate: performanceReport.recommendationAccuracy.fallbackUsageRate * 100
        },
        health: performanceHealthCheck()
      }
    };

    broadcastToClients(updateData);

  } catch (error) {
    console.error('Error in metrics collection:', error);
  }
};

/**
 * Update metrics buffer with new data point
 */
const updateMetricsBuffer = (metrics) => {
  // Add new data points
  realtimeMetrics.requestsPerMinute.push(metrics.requestsPerMinute);
  realtimeMetrics.responseTimesPerMinute.push(metrics.avgResponseTime);
  realtimeMetrics.tokenUsagePerMinute.push(metrics.tokenUsage);
  realtimeMetrics.cacheHitRatePerMinute.push(metrics.cacheHitRate);
  realtimeMetrics.errorRatePerMinute.push(metrics.errorRate);

  // Maintain buffer size
  if (realtimeMetrics.requestsPerMinute.length > realtimeMetrics.bufferSize) {
    realtimeMetrics.requestsPerMinute.shift();
    realtimeMetrics.responseTimesPerMinute.shift();
    realtimeMetrics.tokenUsagePerMinute.shift();
    realtimeMetrics.cacheHitRatePerMinute.shift();
    realtimeMetrics.errorRatePerMinute.shift();
  }
};

/**
 * Broadcast data to all connected clients
 */
const broadcastToClients = (data) => {
  const message = JSON.stringify(data);
  
  connectedClients.forEach(ws => {
    try {
      if (ws.readyState === ws.OPEN) {
        // Check if client has subscriptions
        if (!ws.subscriptions || ws.subscriptions.includes('all') || 
            ws.subscriptions.some(sub => data.type.includes(sub))) {
          ws.send(message);
        }
      } else {
        // Remove closed connections
        connectedClients.delete(ws);
      }
    } catch (error) {
      console.error('Error broadcasting to client:', error);
      connectedClients.delete(ws);
    }
  });
};

/**
 * Send alert to all connected clients
 */
export const sendRealTimeAlert = (alertType, message, severity = 'warning') => {
  const alertData = {
    type: 'alert',
    data: {
      alertType,
      message,
      severity,
      timestamp: new Date()
    }
  };

  broadcastToClients(alertData);
};

/**
 * Get current real-time metrics
 */
const getCurrentMetrics = async () => {
  const performanceReport = getPerformanceReport();
  const cacheStats = getCacheStatistics();
  const abTestAnalysis = getABTestAnalysis();

  return {
    performance: performanceReport,
    cache: cacheStats,
    abTest: abTestAnalysis,
    realtime: realtimeMetrics,
    connectedClients: connectedClients.size,
    timestamp: new Date()
  };
};

/**
 * Record real-time event (for immediate broadcasting)
 */
export const recordRealTimeEvent = (eventType, eventData) => {
  try {
    const eventMessage = {
      type: 'real_time_event',
      data: {
        eventType,
        eventData,
        timestamp: new Date()
      }
    };

    broadcastToClients(eventMessage);
  } catch (error) {
    console.error('Error recording real-time event:', error);
  }
};

/**
 * Get real-time analytics status
 */
export const getRealTimeAnalyticsStatus = () => {
  return {
    isRunning: wss !== null,
    connectedClients: connectedClients.size,
    metricsCollectionActive: analyticsInterval !== null,
    bufferSize: realtimeMetrics.bufferSize,
    dataPoints: realtimeMetrics.requestsPerMinute.length,
    lastUpdate: new Date()
  };
};

/**
 * Stop real-time analytics
 */
export const stopRealTimeAnalytics = () => {
  try {
    // Clear metrics collection interval
    if (analyticsInterval) {
      clearInterval(analyticsInterval);
      analyticsInterval = null;
    }

    // Close all client connections
    connectedClients.forEach(ws => {
      try {
        ws.close();
      } catch (error) {
        console.error('Error closing client connection:', error);
      }
    });
    connectedClients.clear();

    // Close WebSocket server
    if (wss) {
      wss.close();
      wss = null;
    }

    console.log('📊 Real-time analytics stopped');
    return { success: true };
  } catch (error) {
    console.error('Error stopping real-time analytics:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Health check for real-time analytics
 */
export const realTimeAnalyticsHealthCheck = () => {
  return {
    status: wss ? 'healthy' : 'stopped',
    connectedClients: connectedClients.size,
    metricsCollection: analyticsInterval ? 'active' : 'inactive',
    bufferHealth: {
      size: realtimeMetrics.requestsPerMinute.length,
      maxSize: realtimeMetrics.bufferSize,
      utilizationPercentage: (realtimeMetrics.requestsPerMinute.length / realtimeMetrics.bufferSize) * 100
    },
    lastCheck: new Date()
  };
};
