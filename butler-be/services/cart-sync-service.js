import Cart from "../models/Cart.js";
import Dish from "../models/Dish.js";
import { validateAndApplyOffers } from "./offer-validation-service.js";

/**
 * Cart Synchronization Service
 * Handles syncing cart data between frontend localStorage and backend database
 */

/**
 * Sync cart from localStorage to backend
 * @param {string} userId - User ID
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @param {Array} localStorageItems - Items from localStorage
 * @param {Array} localStorageOffers - Applied offers from localStorage
 * @param {string} sessionId - Session ID
 * @returns {Promise<Object>} - Synced cart data
 */
export const syncCartFromLocalStorage = async (
  userId,
  foodChainId,
  outletId,
  localStorageItems = [],
  localStorageOffers = [],
  sessionId = null
) => {
  try {
    // Get or create cart
    let cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId, sessionId);

    // If localStorage has items and backend cart is empty, sync from localStorage
    if (localStorageItems.length > 0 && cart.items.length === 0) {
      await syncItemsFromLocalStorage(cart, localStorageItems);
    }
    // If backend cart has items and localStorage is empty, return backend cart
    else if (cart.items.length > 0 && localStorageItems.length === 0) {
      // Backend cart takes precedence
    }
    // If both have items, merge them (backend takes precedence for conflicts)
    else if (localStorageItems.length > 0 && cart.items.length > 0) {
      await mergeCartItems(cart, localStorageItems);
    }

    // Auto-apply offers after syncing
    await autoApplyOffers(cart);

    // Populate cart details
    await cart.populate([
      {
        path: "items.dishId",
        select: "name price image description isAvailable category",
        populate: {
          path: "category",
          select: "name",
        },
      },
      {
        path: "appliedOffers.offerId",
        select: "name offerType discountDetails",
      },
    ]);

    return {
      success: true,
      data: cart,
      message: "Cart synced successfully",
    };
  } catch (error) {
    console.error("Error syncing cart from localStorage:", error);
    throw error;
  }
};

/**
 * Sync items from localStorage to backend cart
 * @param {Object} cart - Backend cart instance
 * @param {Array} localStorageItems - Items from localStorage
 */
const syncItemsFromLocalStorage = async (cart, localStorageItems) => {
  for (const item of localStorageItems) {
    try {
      // Get dish details from database
      const dish = await Dish.findById(item._id || item.dishId).populate("category", "name");
      
      if (dish && dish.isAvailable) {
        await cart.addItem(
          {
            ...dish.toObject(),
            categoryName: dish.category?.name,
          },
          item.quantity || 1
        );
      }
    } catch (error) {
      console.error(`Error syncing item ${item._id || item.dishId}:`, error);
      // Continue with other items
    }
  }
};

/**
 * Merge cart items from localStorage with backend cart
 * @param {Object} cart - Backend cart instance
 * @param {Array} localStorageItems - Items from localStorage
 */
const mergeCartItems = async (cart, localStorageItems) => {
  for (const localItem of localStorageItems) {
    try {
      const dishId = localItem._id || localItem.dishId;
      const existingItem = cart.items.find(
        (item) => item.dishId.toString() === dishId.toString()
      );

      if (existingItem) {
        // Update quantity to the maximum of both
        const maxQuantity = Math.max(existingItem.quantity, localItem.quantity || 1);
        await cart.updateItemQuantity(dishId, maxQuantity);
      } else {
        // Add new item from localStorage
        const dish = await Dish.findById(dishId).populate("category", "name");
        if (dish && dish.isAvailable) {
          await cart.addItem(
            {
              ...dish.toObject(),
              categoryName: dish.category?.name,
            },
            localItem.quantity || 1
          );
        }
      }
    } catch (error) {
      console.error(`Error merging item ${localItem._id || localItem.dishId}:`, error);
      // Continue with other items
    }
  }
};

/**
 * Get cart data for frontend synchronization
 * @param {string} userId - User ID
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @returns {Promise<Object>} - Cart data formatted for frontend
 */
export const getCartForSync = async (userId, foodChainId, outletId) => {
  try {
    const cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    
    if (!cart) {
      return {
        success: true,
        data: {
          items: [],
          appliedOffers: [],
          subtotal: 0,
          totalDiscount: 0,
          finalTotal: 0,
        },
      };
    }

    // Populate cart details
    await cart.populate([
      {
        path: "items.dishId",
        select: "name price image description isAvailable category cuisine tags",
        populate: {
          path: "category",
          select: "name",
        },
      },
      {
        path: "appliedOffers.offerId",
        select: "name offerType discountDetails",
      },
    ]);

    // Format items for frontend compatibility
    const formattedItems = cart.items.map((item) => ({
      _id: item.dishId._id,
      name: item.dishName,
      price: item.price,
      quantity: item.quantity,
      image: item.dishImage,
      description: item.dishDescription,
      category: item.category,
      categoryName: item.categoryName,
      isAvailable: item.isAvailable,
      cuisine: item.dishId.cuisine,
      tags: item.dishId.tags,
      totalPrice: item.totalPrice,
    }));

    return {
      success: true,
      data: {
        items: formattedItems,
        appliedOffers: cart.appliedOffers,
        subtotal: cart.subtotal,
        totalOfferDiscount: cart.totalOfferDiscount,
        totalCouponDiscount: cart.totalCouponDiscount,
        totalDiscount: cart.totalDiscount,
        finalTotal: cart.finalTotal,
        taxAmount: cart.taxAmount,
        deliveryFee: cart.deliveryFee,
        packagingFee: cart.packagingFee,
        updatedAt: cart.updatedAt,
      },
    };
  } catch (error) {
    console.error("Error getting cart for sync:", error);
    throw error;
  }
};

/**
 * Handle real-time cart updates across devices
 * @param {string} userId - User ID
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @param {Object} io - Socket.io instance
 */
export const broadcastCartUpdate = async (userId, foodChainId, outletId, io) => {
  try {
    const cartData = await getCartForSync(userId, foodChainId, outletId);
    
    // Broadcast to all user's connected devices
    io.to(`user_${userId}`).emit("cartUpdated", {
      foodChainId,
      outletId,
      cart: cartData.data,
    });
  } catch (error) {
    console.error("Error broadcasting cart update:", error);
  }
};

/**
 * Clean up expired carts
 * @returns {Promise<Object>} - Cleanup result
 */
export const cleanupExpiredCarts = async () => {
  try {
    const result = await Cart.deleteMany({
      status: "active",
      expiresAt: { $lt: new Date() },
    });

    console.log(`Cleaned up ${result.deletedCount} expired carts`);
    
    return {
      success: true,
      deletedCount: result.deletedCount,
    };
  } catch (error) {
    console.error("Error cleaning up expired carts:", error);
    throw error;
  }
};

/**
 * Convert cart to order format
 * @param {string} userId - User ID
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @returns {Promise<Object>} - Order data
 */
export const convertCartToOrder = async (userId, foodChainId, outletId) => {
  try {
    const cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    
    if (!cart || cart.items.length === 0) {
      throw new Error("Cart is empty or not found");
    }

    // Format cart data for order creation
    const orderData = {
      userId: cart.userId,
      foodChainId: cart.foodChainId,
      outletId: cart.outletId,
      items: cart.items.map((item) => ({
        dishId: item.dishId,
        dishName: item.dishName,
        quantity: item.quantity,
        price: item.price,
        totalPrice: item.totalPrice,
      })),
      appliedOffers: cart.appliedOffers.map((offer) => ({
        offerId: offer.offerId,
        offerName: offer.offerName,
        discountAmount: offer.discountAmount,
      })),
      appliedCoupons: cart.appliedCoupons,
      subtotal: cart.subtotal,
      totalDiscount: cart.totalDiscount,
      finalTotal: cart.finalTotal,
      taxAmount: cart.taxAmount,
      deliveryFee: cart.deliveryFee,
      packagingFee: cart.packagingFee,
    };

    // Mark cart as converted
    cart.status = "converted";
    await cart.save();

    return {
      success: true,
      data: orderData,
    };
  } catch (error) {
    console.error("Error converting cart to order:", error);
    throw error;
  }
};

/**
 * Auto-apply offers to cart
 * @param {Object} cart - Cart instance
 */
const autoApplyOffers = async (cart) => {
  try {
    // Prepare order data for offer validation
    const orderData = {
      outletId: cart.outletId,
      orderAmount: cart.subtotal,
      customerId: cart.userId,
      items: cart.items.map((item) => ({
        dishId: item.dishId,
        dishName: item.dishName,
        quantity: item.quantity,
        price: item.price,
        category: item.category,
      })),
      foodChainId: cart.foodChainId,
    };

    // Get applicable offers and apply them
    const offerResult = await validateAndApplyOffers(orderData, cart.userId);

    if (offerResult.appliedOffers && offerResult.appliedOffers.length > 0) {
      // Clear existing offers
      cart.appliedOffers = [];

      // Apply new offers
      for (const appliedOffer of offerResult.appliedOffers) {
        await cart.applyOffer(
          appliedOffer.offer,
          appliedOffer.discountAmount,
          appliedOffer.applicableItems || []
        );
      }
    }
  } catch (error) {
    console.error("Error auto-applying offers:", error);
    // Don't throw error, just log it
  }
};
