import Order from "../models/Order.js";
import User from "../models/User.js";

/**
 * Personalization service for menu ordering and recommendations
 * Tracks user preferences and order history for personalized experiences
 */

/**
 * Get user's order history and preferences
 * @param {string} userId - User ID
 * @param {string} outletId - Outlet ID (optional)
 * @param {number} limit - Number of recent orders to analyze
 * @returns {Promise<Object>} - User preferences and order history
 */
export const getUserPreferences = async (userId, outletId = null, limit = 20) => {
  try {
    // Build query for orders
    const orderQuery = { userId };
    if (outletId) {
      orderQuery.outletId = outletId;
    }

    // Get recent orders
    const recentOrders = await Order.find(orderQuery)
      .populate('items.dishId', 'name category cuisine tags price isVeg')
      .sort({ createdAt: -1 })
      .limit(limit);

    // Analyze order patterns
    const dishFrequency = {};
    const categoryFrequency = {};
    const cuisineFrequency = {};
    const priceRangeFrequency = { low: 0, medium: 0, high: 0 };
    const vegPreference = { veg: 0, nonVeg: 0 };
    let totalOrders = recentOrders.length;
    let totalItems = 0;

    recentOrders.forEach(order => {
      order.items.forEach(item => {
        if (!item.dishId) return; // Skip if dish is deleted
        
        const dish = item.dishId;
        const dishId = dish._id.toString();
        const quantity = item.quantity;
        totalItems += quantity;

        // Track dish frequency
        dishFrequency[dishId] = (dishFrequency[dishId] || 0) + quantity;

        // Track category frequency
        if (dish.category) {
          const categoryName = dish.category.name || dish.category;
          categoryFrequency[categoryName] = (categoryFrequency[categoryName] || 0) + quantity;
        }

        // Track cuisine frequency
        if (dish.cuisine) {
          cuisineFrequency[dish.cuisine] = (cuisineFrequency[dish.cuisine] || 0) + quantity;
        }

        // Track price range
        const price = dish.price;
        if (price < 200) {
          priceRangeFrequency.low += quantity;
        } else if (price < 500) {
          priceRangeFrequency.medium += quantity;
        } else {
          priceRangeFrequency.high += quantity;
        }

        // Track veg preference
        if (dish.isVeg) {
          vegPreference.veg += quantity;
        } else {
          vegPreference.nonVeg += quantity;
        }
      });
    });

    // Calculate preferences as percentages
    const preferences = {
      favoriteCategories: Object.entries(categoryFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([category, count]) => ({
          category,
          percentage: Math.round((count / totalItems) * 100)
        })),
      
      favoriteCuisines: Object.entries(cuisineFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([cuisine, count]) => ({
          cuisine,
          percentage: Math.round((count / totalItems) * 100)
        })),
      
      pricePreference: {
        low: Math.round((priceRangeFrequency.low / totalItems) * 100),
        medium: Math.round((priceRangeFrequency.medium / totalItems) * 100),
        high: Math.round((priceRangeFrequency.high / totalItems) * 100)
      },
      
      vegPreference: {
        vegetarian: Math.round((vegPreference.veg / totalItems) * 100),
        nonVegetarian: Math.round((vegPreference.nonVeg / totalItems) * 100)
      },
      
      orderFrequency: totalOrders,
      averageItemsPerOrder: totalOrders > 0 ? Math.round(totalItems / totalOrders) : 0
    };

    return {
      preferences,
      dishFrequency,
      recentOrders: recentOrders.slice(0, 5), // Return only 5 most recent
      totalOrders,
      totalItems
    };
  } catch (error) {
    console.error("Error getting user preferences:", error);
    return {
      preferences: {
        favoriteCategories: [],
        favoriteCuisines: [],
        pricePreference: { low: 0, medium: 0, high: 0 },
        vegPreference: { vegetarian: 0, nonVegetarian: 0 },
        orderFrequency: 0,
        averageItemsPerOrder: 0
      },
      dishFrequency: {},
      recentOrders: [],
      totalOrders: 0,
      totalItems: 0
    };
  }
};

/**
 * Calculate personalized score for a dish based on user preferences
 * @param {Object} dish - Dish object
 * @param {Object} userPreferences - User preferences from getUserPreferences
 * @returns {number} - Personalization score (0-1)
 */
export const calculatePersonalizationScore = (dish, userPreferences) => {
  let score = 0;
  const { preferences, dishFrequency } = userPreferences;

  // Check if user has ordered this dish before (highest weight)
  const dishId = dish._id.toString();
  if (dishFrequency[dishId]) {
    score += 0.4; // High weight for previously ordered dishes
  }

  // Category preference
  const dishCategory = dish.category?.name || dish.category;
  if (dishCategory && preferences.favoriteCategories.length > 0) {
    const categoryMatch = preferences.favoriteCategories.find(
      cat => cat.category === dishCategory
    );
    if (categoryMatch) {
      score += (categoryMatch.percentage / 100) * 0.25;
    }
  }

  // Cuisine preference
  if (dish.cuisine && preferences.favoriteCuisines.length > 0) {
    const cuisineMatch = preferences.favoriteCuisines.find(
      cui => cui.cuisine === dish.cuisine
    );
    if (cuisineMatch) {
      score += (cuisineMatch.percentage / 100) * 0.2;
    }
  }

  // Price preference
  const price = dish.price;
  let priceCategory = 'medium';
  if (price < 200) priceCategory = 'low';
  else if (price >= 500) priceCategory = 'high';
  
  const pricePreference = preferences.pricePreference[priceCategory] || 0;
  score += (pricePreference / 100) * 0.1;

  // Veg preference
  const vegPreference = dish.isVeg 
    ? preferences.vegPreference.vegetarian 
    : preferences.vegPreference.nonVegetarian;
  score += (vegPreference / 100) * 0.05;

  return Math.min(score, 1); // Cap at 1
};

/**
 * Reorder dishes based on user preferences
 * @param {Array<Object>} dishes - Array of dishes
 * @param {string} userId - User ID
 * @param {string} outletId - Outlet ID (optional)
 * @returns {Promise<Array<Object>>} - Reordered dishes with personalization scores
 */
export const personalizeMenuOrder = async (dishes, userId, outletId = null) => {
  try {
    // Get user preferences
    const userPreferences = await getUserPreferences(userId, outletId);
    
    // Calculate personalization scores and sort
    const personalizedDishes = dishes.map(dish => ({
      ...dish,
      personalizationScore: calculatePersonalizationScore(dish, userPreferences)
    }));

    // Sort by personalization score (highest first), then by original order
    personalizedDishes.sort((a, b) => {
      if (b.personalizationScore !== a.personalizationScore) {
        return b.personalizationScore - a.personalizationScore;
      }
      // If scores are equal, maintain original order
      return 0;
    });

    return personalizedDishes;
  } catch (error) {
    console.error("Error personalizing menu order:", error);
    // Return original dishes if personalization fails
    return dishes.map(dish => ({
      ...dish,
      personalizationScore: 0
    }));
  }
};

/**
 * Get personalized recommendations for a user
 * @param {string} userId - User ID
 * @param {Array<Object>} availableDishes - Available dishes
 * @param {string} outletId - Outlet ID (optional)
 * @param {number} limit - Number of recommendations
 * @returns {Promise<Array<Object>>} - Personalized recommendations
 */
export const getPersonalizedRecommendations = async (
  userId, 
  availableDishes, 
  outletId = null, 
  limit = 5
) => {
  try {
    // Get personalized menu order
    const personalizedDishes = await personalizeMenuOrder(availableDishes, userId, outletId);
    
    // Filter dishes with good personalization scores
    const recommendations = personalizedDishes
      .filter(dish => dish.personalizationScore > 0.1)
      .slice(0, limit);

    // If not enough personalized recommendations, fill with popular dishes
    if (recommendations.length < limit) {
      const remaining = limit - recommendations.length;
      const popularDishes = availableDishes
        .filter(dish => !recommendations.find(rec => rec._id.toString() === dish._id.toString()))
        .sort((a, b) => (b.ratings?.average || 0) - (a.ratings?.average || 0))
        .slice(0, remaining)
        .map(dish => ({
          ...dish,
          personalizationScore: 0
        }));
      
      recommendations.push(...popularDishes);
    }

    return recommendations;
  } catch (error) {
    console.error("Error getting personalized recommendations:", error);
    return availableDishes.slice(0, limit).map(dish => ({
      ...dish,
      personalizationScore: 0
    }));
  }
};

/**
 * Update user preferences based on new order
 * @param {string} userId - User ID
 * @param {Object} order - Order object
 * @returns {Promise<void>}
 */
export const updateUserPreferencesFromOrder = async (userId, order) => {
  try {
    // This function can be called after order completion
    // to update user preferences in real-time
    // For now, preferences are calculated dynamically from order history
    console.log(`Updated preferences for user ${userId} based on order ${order._id}`);
  } catch (error) {
    console.error("Error updating user preferences:", error);
  }
};
