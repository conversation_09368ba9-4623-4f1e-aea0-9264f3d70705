import WebhookRetry from "../models/WebhookRetry.js";

/**
 * Save a failed webhook for retry
 * @param {Object} options - Webhook options
 * @param {String} options.event - Webhook event
 * @param {Object} options.payload - Webhook payload
 * @param {Object} options.headers - Webhook headers
 * @param {String} options.error - Error message
 * @param {Number} options.maxAttempts - Maximum retry attempts (default: 5)
 * @returns {Object} - Created webhook retry record
 */
export const saveWebhookForRetry = async (options) => {
  try {
    const {
      event,
      payload,
      headers,
      error,
      maxAttempts = 5,
    } = options;

    // Calculate next attempt time (exponential backoff)
    // First retry after 5 minutes, then 15 minutes, then 1 hour, then 4 hours, then 12 hours
    const nextAttemptAt = new Date();
    nextAttemptAt.setMinutes(nextAttemptAt.getMinutes() + 5);

    // Create webhook retry record
    const webhookRetry = new WebhookRetry({
      event,
      payload,
      headers,
      attempts: 0,
      maxAttempts,
      nextAttemptAt,
      status: "pending",
      error: error ? error.toString() : "Unknown error",
    });

    await webhookRetry.save();
    console.log(`Webhook saved for retry: ${webhookRetry._id}`);
    return webhookRetry;
  } catch (error) {
    console.error("Error saving webhook for retry:", error);
    return null;
  }
};

/**
 * Process pending webhook retries
 * @returns {Array} - Processed webhook retries
 */
export const processWebhookRetries = async () => {
  try {
    // Find pending webhook retries that are due for processing
    const pendingRetries = await WebhookRetry.find({
      status: "pending",
      nextAttemptAt: { $lte: new Date() },
    }).limit(10);

    if (pendingRetries.length === 0) {
      return [];
    }

    console.log(`Processing ${pendingRetries.length} pending webhook retries`);
    const results = [];

    for (const retry of pendingRetries) {
      try {
        // Mark as processing
        retry.status = "processing";
        retry.lastAttemptAt = new Date();
        retry.attempts += 1;
        await retry.save();

        // Import the webhook handler dynamically
        const { handleSubscriptionPaymentWebhook } = await import(
          "../controllers/subscription-controller.js"
        );

        // Create a mock request and response
        const req = {
          body: {
            event: retry.event,
            payload: retry.payload,
          },
          headers: retry.headers || {},
        };

        let success = false;
        const res = {
          status: (code) => ({
            json: (data) => {
              if (code >= 200 && code < 300) {
                success = true;
              }
              return data;
            },
          }),
        };

        // Process the webhook
        await handleSubscriptionPaymentWebhook(req, res);

        // Update retry status
        if (success) {
          retry.status = "completed";
          console.log(`Webhook retry completed successfully: ${retry._id}`);
        } else {
          // Calculate next attempt time with exponential backoff
          const nextAttemptAt = new Date();
          const backoffMinutes = Math.pow(3, retry.attempts) * 5; // 5, 15, 45, 135, 405 minutes
          nextAttemptAt.setMinutes(nextAttemptAt.getMinutes() + backoffMinutes);

          retry.nextAttemptAt = nextAttemptAt;
          retry.status = retry.attempts >= retry.maxAttempts ? "failed" : "pending";
          
          console.log(
            `Webhook retry ${retry.status === "failed" ? "failed permanently" : "scheduled for retry"}: ${retry._id}`
          );
        }

        await retry.save();
        results.push(retry);
      } catch (error) {
        console.error(`Error processing webhook retry ${retry._id}:`, error);
        
        // Update retry status
        retry.error = error.toString();
        
        // Calculate next attempt time with exponential backoff
        const nextAttemptAt = new Date();
        const backoffMinutes = Math.pow(3, retry.attempts) * 5;
        nextAttemptAt.setMinutes(nextAttemptAt.getMinutes() + backoffMinutes);

        retry.nextAttemptAt = nextAttemptAt;
        retry.status = retry.attempts >= retry.maxAttempts ? "failed" : "pending";
        
        await retry.save();
        results.push(retry);
      }
    }

    return results;
  } catch (error) {
    console.error("Error processing webhook retries:", error);
    return [];
  }
};

export default {
  saveWebhookForRetry,
  processWebhookRetries,
};
