import { Groq } from "groq-sdk";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import dotenv from "dotenv";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Groq client with validation
if (!process.env.GROQ_API_KEY) {
  console.error("❌ GROQ_API_KEY environment variable is not set");
}

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

/**
 * Transcribe audio using Groq's Whisper model
 * @param {Buffer} audioBuffer - Audio file buffer
 * @param {string} language - Language code (optional)
 * @param {string} originalFilename - Original filename for context
 * @returns {Promise<Object>} - Transcription result
 */
export const transcribeAudioWithGroq = async (
  audioBuffer,
  language = "en",
  originalFilename = "audio.webm"
) => {
  try {
    // Validate API key
    if (!process.env.GROQ_API_KEY) {
      throw new Error("GROQ_API_KEY is not configured");
    }

    console.log(
      `🎤 Starting Groq audio transcription for ${originalFilename}...`
    );

    // Create temporary file for Groq API
    const tempDir = path.join(__dirname, "../temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFilePath = path.join(
      tempDir,
      `temp_${Date.now()}_${originalFilename}`
    );

    try {
      // Write audio buffer to temporary file
      fs.writeFileSync(tempFilePath, audioBuffer);

      // Prepare transcription request
      const transcriptionOptions = {
        file: fs.createReadStream(tempFilePath),
        model: "whisper-large-v3", // Groq's Whisper model
        response_format: "json",
        temperature: 0.0, // More deterministic results
      };

      // Add language if specified and supported
      const supportedLanguages = [
        "en",
        "es",
        "fr",
        "de",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "zh",
        "ar",
        "hi",
      ];

      if (supportedLanguages.includes(language)) {
        transcriptionOptions.language = language;
      }

      console.log(
        `🔄 Sending audio to Groq Whisper (language: ${language})...`
      );

      // Call Groq transcription API
      const transcription = await groq.audio.transcriptions.create(
        transcriptionOptions
      );

      console.log(`✅ Groq transcription completed successfully`);

      return {
        success: true,
        transcript: transcription.text,
        language: transcription.language || language,
        duration: transcription.duration,
        method: "groq_whisper",
        confidence: 0.9, // Groq Whisper generally has high confidence
      };
    } finally {
      // Clean up temporary file
      try {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
      } catch (cleanupError) {
        console.warn("Failed to cleanup temp file:", cleanupError);
      }
    }
  } catch (error) {
    console.error("Groq transcription error:", error);

    // Provide more specific error information
    let errorMessage = error.message;
    let errorType = error.code || "unknown";

    if (error.message.includes("API key")) {
      errorMessage = "Invalid or missing Groq API key";
      errorType = "auth_error";
    } else if (error.message.includes("rate limit")) {
      errorMessage = "Groq API rate limit exceeded";
      errorType = "rate_limit";
    } else if (error.message.includes("timeout")) {
      errorMessage = "Groq API request timeout";
      errorType = "timeout";
    } else if (
      error.message.includes("network") ||
      error.message.includes("fetch")
    ) {
      errorMessage = "Network error connecting to Groq API";
      errorType = "network_error";
    } else if (error.message.includes("file")) {
      errorMessage = "Audio file processing error";
      errorType = "file_error";
    }

    // Return error details for fallback handling
    return {
      success: false,
      error: errorMessage,
      errorType: errorType,
      method: "groq_whisper",
      originalError: error.message,
    };
  }
};

/**
 * Fallback transcription using browser-based speech recognition results
 * This is used when Groq transcription fails
 * @param {string} browserTranscript - Transcript from browser speech recognition
 * @param {string} language - Language code
 * @returns {Promise<Object>} - Processed transcription result
 */
export const processBrowserTranscript = async (
  browserTranscript,
  language = "en"
) => {
  try {
    console.log(`🔄 Processing browser transcript as fallback...`);

    // Basic text cleaning and processing
    let cleanedTranscript = browserTranscript
      .trim()
      .replace(/\s+/g, " ") // Normalize whitespace
      .replace(/[^\w\s\u00C0-\u017F\u0100-\u017F\u1E00-\u1EFF]/g, "") // Keep alphanumeric and accented chars
      .toLowerCase();

    // Capitalize first letter
    if (cleanedTranscript.length > 0) {
      cleanedTranscript =
        cleanedTranscript.charAt(0).toUpperCase() + cleanedTranscript.slice(1);
    }

    return {
      success: true,
      transcript: cleanedTranscript,
      language: language,
      method: "browser_fallback",
      confidence: 0.7, // Lower confidence for browser transcription
    };
  } catch (error) {
    console.error("Browser transcript processing error:", error);
    return {
      success: false,
      error: error.message,
      method: "browser_fallback",
    };
  }
};

/**
 * Enhanced transcription with multiple fallback options
 * @param {Buffer} audioBuffer - Audio file buffer
 * @param {string} language - Language code
 * @param {string} originalFilename - Original filename
 * @param {string} browserFallback - Browser transcript as fallback
 * @returns {Promise<Object>} - Best available transcription result
 */
export const getEnhancedTranscription = async (
  audioBuffer,
  language = "en",
  originalFilename = "audio.webm",
  browserFallback = null
) => {
  console.log(`🎯 Starting enhanced transcription process...`);

  // Try Groq transcription first
  const groqResult = await transcribeAudioWithGroq(
    audioBuffer,
    language,
    originalFilename
  );

  if (
    groqResult.success &&
    groqResult.transcript &&
    groqResult.transcript.trim().length > 0
  ) {
    console.log(`✅ Using Groq transcription: "${groqResult.transcript}"`);
    return groqResult;
  }

  console.log(`⚠️ Groq transcription failed or empty, trying fallbacks...`);

  // Fallback to browser transcript if available
  if (browserFallback && browserFallback.trim().length > 0) {
    console.log(`🔄 Using browser transcript fallback...`);
    const browserResult = await processBrowserTranscript(
      browserFallback,
      language
    );

    if (browserResult.success) {
      console.log(`✅ Using browser fallback: "${browserResult.transcript}"`);
      return browserResult;
    }
  }

  // Final fallback - return error
  console.log(`❌ All transcription methods failed`);
  return {
    success: false,
    error: "All transcription methods failed",
    groqError: groqResult.error,
    method: "all_failed",
  };
};

/**
 * Validate audio file format and size
 * @param {Buffer} audioBuffer - Audio file buffer
 * @param {string} mimeType - MIME type of the audio file
 * @returns {Object} - Validation result
 */
export const validateAudioFile = (audioBuffer, mimeType) => {
  const maxSize = 25 * 1024 * 1024; // 25MB limit for Groq
  const supportedTypes = [
    "audio/webm",
    "audio/mp4",
    "audio/mpeg",
    "audio/wav",
    "audio/ogg",
    "audio/m4a", // Added for mobile compatibility
    "audio/aac", // Added for mobile compatibility
  ];

  if (!audioBuffer || audioBuffer.length === 0) {
    return {
      valid: false,
      error: "Empty audio file - no audio data received",
    };
  }

  // Check for minimum file size (mobile recordings might be very small)
  if (audioBuffer.length < 100) {
    return {
      valid: false,
      error: "Audio file too small - recording may have failed",
    };
  }

  if (audioBuffer.length > maxSize) {
    return {
      valid: false,
      error: `Audio file too large. Maximum size is ${maxSize / 1024 / 1024}MB`,
    };
  }

  // More lenient MIME type checking for mobile compatibility
  if (mimeType) {
    const isSupported = supportedTypes.some((type) =>
      mimeType.toLowerCase().includes(type.split("/")[1])
    );

    if (!isSupported) {
      console.warn(
        `📱 Unsupported MIME type: ${mimeType}, but proceeding anyway for mobile compatibility`
      );
    }
  } else {
    console.warn("📱 No MIME type provided, assuming webm format");
  }

  return {
    valid: true,
    size: audioBuffer.length,
    sizeFormatted: `${(audioBuffer.length / 1024).toFixed(1)}KB`,
    mimeType: mimeType || "audio/webm",
  };
};

/**
 * Get optimal language code for transcription
 * @param {string} userLanguage - User's preferred language
 * @returns {string} - Optimal language code for transcription
 */
export const getOptimalLanguageCode = (userLanguage) => {
  const languageMap = {
    en: "en",
    hi: "hi",
    es: "es",
    fr: "fr",
    de: "de",
    it: "it",
    pt: "pt",
    ru: "ru",
    ja: "ja",
    ko: "ko",
    zh: "zh",
    ar: "ar",
  };

  return languageMap[userLanguage] || "en";
};
