import User from "../models/User.js";
import Order from "../models/Order.js";
import Dish from "../models/Dish.js";
import { clearUserPreferencesCache } from "./enhanced-menu-personalization-service.js";

/**
 * Real-time preference learning service
 * Learns and adapts to user preferences based on interactions, search patterns, and ordering behavior
 */

// In-memory storage for real-time interactions (consider Redis for production)
const userInteractions = new Map();
const searchPatterns = new Map();
const sessionPreferences = new Map();

/**
 * Track user interaction with dishes
 * @param {string} userId - User ID
 * @param {string} dishId - Dish ID
 * @param {string} interactionType - Type of interaction (view, search, add_to_cart, order, etc.)
 * @param {Object} context - Additional context (time, session, etc.)
 */
export const trackUserInteraction = async (userId, dishId, interactionType, context = {}) => {
  try {
    if (!userId || !dishId || !interactionType) return;

    const timestamp = new Date();
    const sessionId = context.sessionId || 'default';
    
    // Initialize user interactions if not exists
    if (!userInteractions.has(userId)) {
      userInteractions.set(userId, {
        dishes: new Map(),
        categories: new Map(),
        cuisines: new Map(),
        priceRanges: new Map(),
        timePatterns: new Map(),
        lastUpdated: timestamp
      });
    }

    const userInteractionData = userInteractions.get(userId);
    
    // Get dish details for categorization
    const dish = await Dish.findById(dishId).populate('category', 'name');
    if (!dish) return;

    // Track dish-specific interactions
    if (!userInteractionData.dishes.has(dishId)) {
      userInteractionData.dishes.set(dishId, {
        views: 0,
        searches: 0,
        addToCarts: 0,
        orders: 0,
        lastInteraction: timestamp,
        interactionScore: 0
      });
    }

    const dishInteraction = userInteractionData.dishes.get(dishId);
    
    // Update interaction counts and scores
    switch (interactionType) {
      case 'view':
        dishInteraction.views++;
        dishInteraction.interactionScore += 1;
        break;
      case 'search':
        dishInteraction.searches++;
        dishInteraction.interactionScore += 2;
        break;
      case 'add_to_cart':
        dishInteraction.addToCarts++;
        dishInteraction.interactionScore += 5;
        break;
      case 'order':
        dishInteraction.orders++;
        dishInteraction.interactionScore += 10;
        break;
      case 'remove_from_cart':
        dishInteraction.interactionScore -= 2;
        break;
    }

    dishInteraction.lastInteraction = timestamp;

    // Track category preferences
    const categoryName = dish.category?.name || 'Other';
    if (!userInteractionData.categories.has(categoryName)) {
      userInteractionData.categories.set(categoryName, { score: 0, count: 0 });
    }
    const categoryData = userInteractionData.categories.get(categoryName);
    categoryData.count++;
    categoryData.score += getInteractionWeight(interactionType);

    // Track cuisine preferences
    if (dish.cuisine) {
      if (!userInteractionData.cuisines.has(dish.cuisine)) {
        userInteractionData.cuisines.set(dish.cuisine, { score: 0, count: 0 });
      }
      const cuisineData = userInteractionData.cuisines.get(dish.cuisine);
      cuisineData.count++;
      cuisineData.score += getInteractionWeight(interactionType);
    }

    // Track price range preferences
    const priceRange = getPriceRange(dish.price);
    if (!userInteractionData.priceRanges.has(priceRange)) {
      userInteractionData.priceRanges.set(priceRange, { score: 0, count: 0 });
    }
    const priceData = userInteractionData.priceRanges.get(priceRange);
    priceData.count++;
    priceData.score += getInteractionWeight(interactionType);

    // Track time-based patterns
    const timeSlot = getTimeSlot(timestamp.getHours());
    if (!userInteractionData.timePatterns.has(timeSlot)) {
      userInteractionData.timePatterns.set(timeSlot, { 
        categories: new Map(), 
        cuisines: new Map(), 
        totalInteractions: 0 
      });
    }
    const timePattern = userInteractionData.timePatterns.get(timeSlot);
    timePattern.totalInteractions++;
    
    // Update time-based category preferences
    if (!timePattern.categories.has(categoryName)) {
      timePattern.categories.set(categoryName, 0);
    }
    timePattern.categories.set(categoryName, timePattern.categories.get(categoryName) + 1);

    // Update time-based cuisine preferences
    if (dish.cuisine) {
      if (!timePattern.cuisines.has(dish.cuisine)) {
        timePattern.cuisines.set(dish.cuisine, 0);
      }
      timePattern.cuisines.set(dish.cuisine, timePattern.cuisines.get(dish.cuisine) + 1);
    }

    userInteractionData.lastUpdated = timestamp;

    // Update session preferences for immediate personalization
    updateSessionPreferences(userId, sessionId, dish, interactionType);

    // Periodically persist important interactions to database
    if (shouldPersistInteraction(interactionType)) {
      await persistInteractionToDatabase(userId, dishId, interactionType, context);
    }

    // Clear cache to ensure fresh recommendations
    if (['order', 'add_to_cart'].includes(interactionType)) {
      clearUserPreferencesCache(userId);
    }

  } catch (error) {
    console.error("Error tracking user interaction:", error);
  }
};

/**
 * Track user search patterns
 * @param {string} userId - User ID
 * @param {string} searchQuery - Search query
 * @param {Array} results - Search results
 * @param {Object} context - Additional context
 */
export const trackSearchPattern = async (userId, searchQuery, results = [], context = {}) => {
  try {
    if (!userId || !searchQuery) return;

    const timestamp = new Date();
    
    if (!searchPatterns.has(userId)) {
      searchPatterns.set(userId, {
        queries: [],
        preferences: new Map(),
        lastUpdated: timestamp
      });
    }

    const userSearchData = searchPatterns.get(userId);
    
    // Store search query with metadata
    userSearchData.queries.push({
      query: searchQuery.toLowerCase(),
      timestamp,
      resultCount: results.length,
      context
    });

    // Keep only recent searches (last 100)
    if (userSearchData.queries.length > 100) {
      userSearchData.queries = userSearchData.queries.slice(-100);
    }

    // Extract and track search preferences
    const searchTerms = extractSearchTerms(searchQuery);
    searchTerms.forEach(term => {
      if (!userSearchData.preferences.has(term)) {
        userSearchData.preferences.set(term, { count: 0, lastSearched: timestamp });
      }
      const termData = userSearchData.preferences.get(term);
      termData.count++;
      termData.lastSearched = timestamp;
    });

    userSearchData.lastUpdated = timestamp;

    // Track interactions with search results
    if (results.length > 0) {
      results.slice(0, 5).forEach(dish => {
        trackUserInteraction(userId, dish._id, 'search', context);
      });
    }

  } catch (error) {
    console.error("Error tracking search pattern:", error);
  }
};

/**
 * Get real-time user preferences
 * @param {string} userId - User ID
 * @param {string} sessionId - Session ID (optional)
 * @returns {Object} - Real-time preferences
 */
export const getRealTimePreferences = (userId, sessionId = null) => {
  try {
    const preferences = {
      dishes: {},
      categories: {},
      cuisines: {},
      priceRanges: {},
      timePatterns: {},
      searchPreferences: {},
      sessionPreferences: {},
      confidence: 0
    };

    // Get interaction-based preferences
    if (userInteractions.has(userId)) {
      const userInteractionData = userInteractions.get(userId);
      
      // Convert Maps to Objects for easier consumption
      preferences.dishes = Object.fromEntries(userInteractionData.dishes);
      preferences.categories = Object.fromEntries(userInteractionData.categories);
      preferences.cuisines = Object.fromEntries(userInteractionData.cuisines);
      preferences.priceRanges = Object.fromEntries(userInteractionData.priceRanges);
      
      // Convert time patterns
      preferences.timePatterns = {};
      userInteractionData.timePatterns.forEach((pattern, timeSlot) => {
        preferences.timePatterns[timeSlot] = {
          categories: Object.fromEntries(pattern.categories),
          cuisines: Object.fromEntries(pattern.cuisines),
          totalInteractions: pattern.totalInteractions
        };
      });

      // Calculate confidence based on interaction volume
      const totalInteractions = Array.from(userInteractionData.dishes.values())
        .reduce((sum, dish) => sum + dish.interactionScore, 0);
      preferences.confidence = Math.min(totalInteractions / 100, 1); // 0-1 scale
    }

    // Get search-based preferences
    if (searchPatterns.has(userId)) {
      const userSearchData = searchPatterns.get(userId);
      preferences.searchPreferences = Object.fromEntries(userSearchData.preferences);
    }

    // Get session-specific preferences
    if (sessionId && sessionPreferences.has(`${userId}_${sessionId}`)) {
      preferences.sessionPreferences = sessionPreferences.get(`${userId}_${sessionId}`);
    }

    return preferences;
  } catch (error) {
    console.error("Error getting real-time preferences:", error);
    return {
      dishes: {},
      categories: {},
      cuisines: {},
      priceRanges: {},
      timePatterns: {},
      searchPreferences: {},
      sessionPreferences: {},
      confidence: 0
    };
  }
};

/**
 * Update session preferences for immediate personalization
 * @param {string} userId - User ID
 * @param {string} sessionId - Session ID
 * @param {Object} dish - Dish object
 * @param {string} interactionType - Interaction type
 */
const updateSessionPreferences = (userId, sessionId, dish, interactionType) => {
  const sessionKey = `${userId}_${sessionId}`;
  
  if (!sessionPreferences.has(sessionKey)) {
    sessionPreferences.set(sessionKey, {
      categories: new Map(),
      cuisines: new Map(),
      priceRanges: new Map(),
      recentInteractions: [],
      startTime: new Date()
    });
  }

  const sessionData = sessionPreferences.get(sessionKey);
  
  // Track recent interactions
  sessionData.recentInteractions.push({
    dishId: dish._id,
    dishName: dish.name,
    category: dish.category?.name,
    cuisine: dish.cuisine,
    price: dish.price,
    interactionType,
    timestamp: new Date()
  });

  // Keep only recent interactions (last 20)
  if (sessionData.recentInteractions.length > 20) {
    sessionData.recentInteractions = sessionData.recentInteractions.slice(-20);
  }

  // Update session category preferences
  const categoryName = dish.category?.name || 'Other';
  sessionData.categories.set(categoryName, (sessionData.categories.get(categoryName) || 0) + 1);

  // Update session cuisine preferences
  if (dish.cuisine) {
    sessionData.cuisines.set(dish.cuisine, (sessionData.cuisines.get(dish.cuisine) || 0) + 1);
  }

  // Update session price range preferences
  const priceRange = getPriceRange(dish.price);
  sessionData.priceRanges.set(priceRange, (sessionData.priceRanges.get(priceRange) || 0) + 1);
};

/**
 * Helper functions
 */

const getInteractionWeight = (interactionType) => {
  const weights = {
    'view': 1,
    'search': 2,
    'add_to_cart': 5,
    'order': 10,
    'remove_from_cart': -2
  };
  return weights[interactionType] || 1;
};

const getPriceRange = (price) => {
  if (price < 200) return 'low';
  if (price < 500) return 'medium';
  return 'high';
};

const getTimeSlot = (hour) => {
  if (hour >= 6 && hour < 12) return 'morning';
  if (hour >= 12 && hour < 17) return 'afternoon';
  if (hour >= 17 && hour < 22) return 'evening';
  return 'night';
};

const extractSearchTerms = (query) => {
  // Simple term extraction - can be enhanced with NLP
  return query.toLowerCase()
    .split(/\s+/)
    .filter(term => term.length > 2)
    .map(term => term.replace(/[^\w]/g, ''));
};

const shouldPersistInteraction = (interactionType) => {
  return ['order', 'add_to_cart'].includes(interactionType);
};

const persistInteractionToDatabase = async (userId, dishId, interactionType, context) => {
  try {
    // This could be implemented to store important interactions in a dedicated collection
    // For now, we rely on the existing order system for persistence
    console.log(`Persisting interaction: ${userId} ${interactionType} ${dishId}`);
  } catch (error) {
    console.error("Error persisting interaction:", error);
  }
};

/**
 * Clean up old data periodically
 */
export const cleanupOldData = () => {
  const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
  
  // Clean up user interactions
  userInteractions.forEach((data, userId) => {
    if (data.lastUpdated < cutoffTime) {
      userInteractions.delete(userId);
    }
  });

  // Clean up search patterns
  searchPatterns.forEach((data, userId) => {
    if (data.lastUpdated < cutoffTime) {
      searchPatterns.delete(userId);
    }
  });

  // Clean up session preferences (older than 2 hours)
  const sessionCutoff = new Date(Date.now() - 2 * 60 * 60 * 1000);
  sessionPreferences.forEach((data, sessionKey) => {
    if (data.startTime < sessionCutoff) {
      sessionPreferences.delete(sessionKey);
    }
  });
};

// Run cleanup every hour
setInterval(cleanupOldData, 60 * 60 * 1000);
