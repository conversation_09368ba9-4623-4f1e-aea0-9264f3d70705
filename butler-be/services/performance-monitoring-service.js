import fs from 'fs/promises';
import path from 'path';

/**
 * Performance Monitoring Service
 * Tracks token usage, response times, cache hit rates, and recommendation accuracy
 * Provides detailed analytics and optimization insights
 */

// Performance metrics storage
let performanceMetrics = {
  // Token usage tracking
  tokenUsage: {
    totalTokensSaved: 0,
    totalTokensUsed: 0,
    averageTokensPerRequest: 0,
    peakTokenUsage: 0,
    tokenSavingsPercentage: 0
  },
  
  // Response time tracking
  responseTimes: {
    averageResponseTime: 0,
    medianResponseTime: 0,
    p95ResponseTime: 0,
    p99ResponseTime: 0,
    fastestResponse: Infinity,
    slowestResponse: 0
  },
  
  // Cache performance
  cachePerformance: {
    totalCacheHits: 0,
    totalCacheMisses: 0,
    cacheHitRate: 0,
    cacheEfficiency: {},
    cacheSizeOptimization: 0
  },
  
  // Recommendation accuracy
  recommendationAccuracy: {
    totalRecommendations: 0,
    successfulRecommendations: 0,
    userSatisfactionRate: 0,
    fallbackUsageRate: 0,
    averageRecommendationRelevance: 0
  },
  
  // System optimization
  systemOptimization: {
    menuReductionRate: 0,
    averageDishesProcessed: 0,
    optimizationEfficiency: 0,
    resourceUtilization: 0
  },
  
  // Request patterns
  requestPatterns: {
    totalRequests: 0,
    requestsPerHour: 0,
    peakRequestTime: null,
    commonQueryPatterns: {},
    userEngagementMetrics: {}
  },
  
  // Error tracking
  errorTracking: {
    totalErrors: 0,
    errorRate: 0,
    errorsByType: {},
    recoveryRate: 0
  },
  
  // Timestamps
  startTime: new Date(),
  lastReset: new Date(),
  lastUpdate: new Date()
};

// Recent performance data for trend analysis
let recentMetrics = {
  responseTimes: [],
  tokenUsage: [],
  cacheHitRates: [],
  requestCounts: [],
  maxHistorySize: 1000
};

/**
 * Record a recommendation request performance
 */
export const recordRecommendationPerformance = (performanceData) => {
  try {
    const {
      responseTime,
      tokenUsage,
      cacheHits,
      cacheMisses,
      dishesProcessed,
      recommendationCount,
      fallbackUsed,
      optimizationStage,
      userQuery,
      userId
    } = performanceData;

    // Update request count
    performanceMetrics.requestPatterns.totalRequests++;
    
    // Update response times
    updateResponseTimeMetrics(responseTime);
    
    // Update token usage
    if (tokenUsage) {
      updateTokenUsageMetrics(tokenUsage);
    }
    
    // Update cache performance
    updateCacheMetrics(cacheHits, cacheMisses);
    
    // Update recommendation metrics
    updateRecommendationMetrics({
      recommendationCount,
      fallbackUsed,
      dishesProcessed
    });
    
    // Update system optimization metrics
    updateOptimizationMetrics({
      dishesProcessed,
      optimizationStage
    });
    
    // Track query patterns
    trackQueryPattern(userQuery, userId);
    
    // Store recent data for trend analysis
    storeRecentMetrics({
      responseTime,
      tokenUsage: tokenUsage?.estimatedTokens || 0,
      cacheHitRate: cacheHits / Math.max(1, cacheHits + cacheMisses) * 100,
      timestamp: new Date()
    });
    
    performanceMetrics.lastUpdate = new Date();
    
  } catch (error) {
    console.error('Error recording performance metrics:', error);
    performanceMetrics.errorTracking.totalErrors++;
  }
};

/**
 * Update response time metrics
 */
const updateResponseTimeMetrics = (responseTime) => {
  const metrics = performanceMetrics.responseTimes;
  const totalRequests = performanceMetrics.requestPatterns.totalRequests;
  
  // Update average
  metrics.averageResponseTime = 
    (metrics.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests;
  
  // Update extremes
  metrics.fastestResponse = Math.min(metrics.fastestResponse, responseTime);
  metrics.slowestResponse = Math.max(metrics.slowestResponse, responseTime);
  
  // Add to recent data for percentile calculation
  recentMetrics.responseTimes.push(responseTime);
  if (recentMetrics.responseTimes.length > recentMetrics.maxHistorySize) {
    recentMetrics.responseTimes.shift();
  }
  
  // Calculate percentiles
  const sortedTimes = [...recentMetrics.responseTimes].sort((a, b) => a - b);
  const length = sortedTimes.length;
  
  if (length > 0) {
    metrics.medianResponseTime = sortedTimes[Math.floor(length * 0.5)];
    metrics.p95ResponseTime = sortedTimes[Math.floor(length * 0.95)];
    metrics.p99ResponseTime = sortedTimes[Math.floor(length * 0.99)];
  }
};

/**
 * Update token usage metrics
 */
const updateTokenUsageMetrics = (tokenUsage) => {
  const metrics = performanceMetrics.tokenUsage;
  const totalRequests = performanceMetrics.requestPatterns.totalRequests;
  
  const tokensUsed = tokenUsage.estimatedTokens || 0;
  const tokensSaved = tokenUsage.tokensSaved || 0;
  
  metrics.totalTokensUsed += tokensUsed;
  metrics.totalTokensSaved += tokensSaved;
  
  // Update average
  metrics.averageTokensPerRequest = metrics.totalTokensUsed / totalRequests;
  
  // Update peak usage
  metrics.peakTokenUsage = Math.max(metrics.peakTokenUsage, tokensUsed);
  
  // Calculate savings percentage
  const totalPotentialTokens = metrics.totalTokensUsed + metrics.totalTokensSaved;
  metrics.tokenSavingsPercentage = totalPotentialTokens > 0 ? 
    (metrics.totalTokensSaved / totalPotentialTokens) * 100 : 0;
};

/**
 * Update cache performance metrics
 */
const updateCacheMetrics = (hits = 0, misses = 0) => {
  const metrics = performanceMetrics.cachePerformance;
  
  metrics.totalCacheHits += hits;
  metrics.totalCacheMisses += misses;
  
  const totalCacheRequests = metrics.totalCacheHits + metrics.totalCacheMisses;
  metrics.cacheHitRate = totalCacheRequests > 0 ? 
    (metrics.totalCacheHits / totalCacheRequests) * 100 : 0;
};

/**
 * Update recommendation metrics
 */
const updateRecommendationMetrics = ({ recommendationCount, fallbackUsed, dishesProcessed }) => {
  const metrics = performanceMetrics.recommendationAccuracy;
  const totalRequests = performanceMetrics.requestPatterns.totalRequests;
  
  metrics.totalRecommendations += recommendationCount || 0;
  
  if (fallbackUsed) {
    const currentFallbackCount = metrics.fallbackUsageRate * (totalRequests - 1);
    metrics.fallbackUsageRate = (currentFallbackCount + 1) / totalRequests;
  } else {
    metrics.fallbackUsageRate = (metrics.fallbackUsageRate * (totalRequests - 1)) / totalRequests;
  }
};

/**
 * Update system optimization metrics
 */
const updateOptimizationMetrics = ({ dishesProcessed, optimizationStage }) => {
  const metrics = performanceMetrics.systemOptimization;
  const totalRequests = performanceMetrics.requestPatterns.totalRequests;
  
  if (dishesProcessed) {
    const originalDishCount = dishesProcessed.original || 200; // Assume 200 as baseline
    const processedDishCount = dishesProcessed.sentToAI || dishesProcessed.afterSemantic || originalDishCount;
    
    const reductionRate = ((originalDishCount - processedDishCount) / originalDishCount) * 100;
    
    metrics.menuReductionRate = 
      (metrics.menuReductionRate * (totalRequests - 1) + reductionRate) / totalRequests;
    
    metrics.averageDishesProcessed = 
      (metrics.averageDishesProcessed * (totalRequests - 1) + processedDishCount) / totalRequests;
  }
  
  // Calculate optimization efficiency
  const stageEfficiency = optimizationStage === 3 ? 1.0 : optimizationStage === 2 ? 0.8 : 0.6;
  metrics.optimizationEfficiency = 
    (metrics.optimizationEfficiency * (totalRequests - 1) + stageEfficiency) / totalRequests;
};

/**
 * Track query patterns for insights
 */
const trackQueryPattern = (userQuery, userId) => {
  if (!userQuery) return;
  
  const patterns = performanceMetrics.requestPatterns.commonQueryPatterns;
  const queryLength = userQuery.length;
  const queryType = categorizeQuery(userQuery);
  
  // Track query types
  if (!patterns[queryType]) {
    patterns[queryType] = 0;
  }
  patterns[queryType]++;
  
  // Track query lengths
  if (!patterns.queryLengths) {
    patterns.queryLengths = { short: 0, medium: 0, long: 0 };
  }
  
  if (queryLength < 20) patterns.queryLengths.short++;
  else if (queryLength < 50) patterns.queryLengths.medium++;
  else patterns.queryLengths.long++;
};

/**
 * Categorize query type for pattern analysis
 */
const categorizeQuery = (query) => {
  const lowerQuery = query.toLowerCase();
  
  if (lowerQuery.includes('veg') || lowerQuery.includes('vegetarian')) return 'dietary';
  if (lowerQuery.includes('spicy') || lowerQuery.includes('mild')) return 'taste';
  if (lowerQuery.includes('cheap') || lowerQuery.includes('expensive')) return 'price';
  if (lowerQuery.includes('popular') || lowerQuery.includes('recommend')) return 'recommendation';
  if (lowerQuery.includes('breakfast') || lowerQuery.includes('lunch') || lowerQuery.includes('dinner')) return 'meal_time';
  
  return 'general';
};

/**
 * Store recent metrics for trend analysis
 */
const storeRecentMetrics = (data) => {
  Object.keys(data).forEach(key => {
    if (key !== 'timestamp' && recentMetrics[key]) {
      recentMetrics[key].push(data[key]);
      if (recentMetrics[key].length > recentMetrics.maxHistorySize) {
        recentMetrics[key].shift();
      }
    }
  });
};

/**
 * Get comprehensive performance report
 */
export const getPerformanceReport = () => {
  const uptime = Date.now() - performanceMetrics.startTime.getTime();
  const hoursSinceStart = uptime / (1000 * 60 * 60);
  
  // Calculate requests per hour
  performanceMetrics.requestPatterns.requestsPerHour = 
    hoursSinceStart > 0 ? performanceMetrics.requestPatterns.totalRequests / hoursSinceStart : 0;
  
  return {
    ...performanceMetrics,
    uptime: {
      milliseconds: uptime,
      hours: hoursSinceStart,
      days: hoursSinceStart / 24
    },
    trends: calculateTrends(),
    insights: generateInsights(),
    recommendations: generateOptimizationRecommendations()
  };
};

/**
 * Calculate performance trends
 */
const calculateTrends = () => {
  const trends = {};
  
  // Response time trend
  if (recentMetrics.responseTimes.length > 10) {
    const recent = recentMetrics.responseTimes.slice(-10);
    const older = recentMetrics.responseTimes.slice(-20, -10);
    
    if (older.length > 0) {
      const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
      trends.responseTime = ((recentAvg - olderAvg) / olderAvg) * 100;
    }
  }
  
  // Token usage trend
  if (recentMetrics.tokenUsage.length > 10) {
    const recent = recentMetrics.tokenUsage.slice(-10);
    const older = recentMetrics.tokenUsage.slice(-20, -10);
    
    if (older.length > 0) {
      const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
      trends.tokenUsage = ((recentAvg - olderAvg) / olderAvg) * 100;
    }
  }
  
  return trends;
};

/**
 * Generate performance insights
 */
const generateInsights = () => {
  const insights = [];
  
  // Token savings insight
  if (performanceMetrics.tokenUsage.tokenSavingsPercentage > 70) {
    insights.push({
      type: 'success',
      message: `Excellent token optimization: ${performanceMetrics.tokenUsage.tokenSavingsPercentage.toFixed(1)}% savings achieved`
    });
  }
  
  // Cache performance insight
  if (performanceMetrics.cachePerformance.cacheHitRate > 60) {
    insights.push({
      type: 'success',
      message: `Good cache performance: ${performanceMetrics.cachePerformance.cacheHitRate.toFixed(1)}% hit rate`
    });
  } else if (performanceMetrics.cachePerformance.cacheHitRate < 30) {
    insights.push({
      type: 'warning',
      message: `Low cache hit rate: ${performanceMetrics.cachePerformance.cacheHitRate.toFixed(1)}% - consider cache optimization`
    });
  }
  
  // Response time insight
  if (performanceMetrics.responseTimes.averageResponseTime > 2000) {
    insights.push({
      type: 'warning',
      message: `High average response time: ${performanceMetrics.responseTimes.averageResponseTime.toFixed(0)}ms`
    });
  }
  
  // Fallback usage insight
  if (performanceMetrics.recommendationAccuracy.fallbackUsageRate > 0.2) {
    insights.push({
      type: 'warning',
      message: `High fallback usage: ${(performanceMetrics.recommendationAccuracy.fallbackUsageRate * 100).toFixed(1)}%`
    });
  }
  
  return insights;
};

/**
 * Generate optimization recommendations
 */
const generateOptimizationRecommendations = () => {
  const recommendations = [];
  
  if (performanceMetrics.cachePerformance.cacheHitRate < 40) {
    recommendations.push('Increase cache TTL values for better hit rates');
  }
  
  if (performanceMetrics.responseTimes.averageResponseTime > 1500) {
    recommendations.push('Consider implementing more aggressive pre-filtering');
  }
  
  if (performanceMetrics.tokenUsage.tokenSavingsPercentage < 60) {
    recommendations.push('Optimize AI prompt size and dish selection criteria');
  }
  
  if (performanceMetrics.recommendationAccuracy.fallbackUsageRate > 0.15) {
    recommendations.push('Improve AI service reliability or enhance fallback algorithms');
  }
  
  return recommendations;
};

/**
 * Reset performance metrics
 */
export const resetPerformanceMetrics = () => {
  const currentTime = new Date();
  
  performanceMetrics = {
    ...performanceMetrics,
    tokenUsage: { totalTokensSaved: 0, totalTokensUsed: 0, averageTokensPerRequest: 0, peakTokenUsage: 0, tokenSavingsPercentage: 0 },
    responseTimes: { averageResponseTime: 0, medianResponseTime: 0, p95ResponseTime: 0, p99ResponseTime: 0, fastestResponse: Infinity, slowestResponse: 0 },
    cachePerformance: { totalCacheHits: 0, totalCacheMisses: 0, cacheHitRate: 0, cacheEfficiency: {}, cacheSizeOptimization: 0 },
    recommendationAccuracy: { totalRecommendations: 0, successfulRecommendations: 0, userSatisfactionRate: 0, fallbackUsageRate: 0, averageRecommendationRelevance: 0 },
    systemOptimization: { menuReductionRate: 0, averageDishesProcessed: 0, optimizationEfficiency: 0, resourceUtilization: 0 },
    requestPatterns: { totalRequests: 0, requestsPerHour: 0, peakRequestTime: null, commonQueryPatterns: {}, userEngagementMetrics: {} },
    errorTracking: { totalErrors: 0, errorRate: 0, errorsByType: {}, recoveryRate: 0 },
    lastReset: currentTime,
    lastUpdate: currentTime
  };
  
  // Clear recent metrics
  Object.keys(recentMetrics).forEach(key => {
    if (Array.isArray(recentMetrics[key])) {
      recentMetrics[key] = [];
    }
  });
};

/**
 * Export performance data to file
 */
export const exportPerformanceData = async (filePath) => {
  try {
    const report = getPerformanceReport();
    await fs.writeFile(filePath, JSON.stringify(report, null, 2));
    return { success: true, filePath };
  } catch (error) {
    console.error('Error exporting performance data:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Performance monitoring health check
 */
export const performanceHealthCheck = () => {
  const report = getPerformanceReport();
  
  return {
    status: 'healthy',
    metrics: {
      totalRequests: report.requestPatterns.totalRequests,
      averageResponseTime: `${report.responseTimes.averageResponseTime.toFixed(2)}ms`,
      tokenSavings: `${report.tokenUsage.tokenSavingsPercentage.toFixed(1)}%`,
      cacheHitRate: `${report.cachePerformance.cacheHitRate.toFixed(1)}%`,
      fallbackRate: `${(report.recommendationAccuracy.fallbackUsageRate * 100).toFixed(1)}%`
    },
    uptime: report.uptime,
    lastUpdate: report.lastUpdate
  };
};
