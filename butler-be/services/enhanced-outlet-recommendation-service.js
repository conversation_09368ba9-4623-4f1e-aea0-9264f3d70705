import Order from "../models/Order.js";
import Outlet from "../models/Outlet.js";
import User from "../models/User.js";
import { getUserPreferences } from "./personalization-service.js";

/**
 * Enhanced outlet recommendation service with advanced scoring
 * Incorporates location patterns, time-based preferences, social proof, and real-time data
 */

/**
 * Get enhanced outlet recommendations
 * @param {string} userId - User ID (optional)
 * @param {string} city - City filter
 * @param {string} pincode - Pincode filter
 * @param {Object} userLocation - User's current location {lat, lng}
 * @param {number} limit - Number of recommendations
 * @returns {Promise<Array>} - Enhanced outlet recommendations
 */
export const getEnhancedOutletRecommendations = async (
  userId = null,
  city = null,
  pincode = null,
  userLocation = null,
  limit = 20
) => {
  try {
    // Build base query for active outlets
    const query = { status: "active" };

    if (city) {
      query.city = { $regex: city, $options: "i" };
    }

    if (pincode) {
      query.pincode = pincode;
    }

    // Get outlets with enhanced data
    let outlets = await Outlet.find(query)
      .populate("foodChain", "name theme")
      .populate("dishes", "name category cuisine isVeg price ratings")
      .select(
        "name address city pincode contact isCloudKitchen deliveryRadius dishes operatingHours location"
      )
      .limit(100); // Get more outlets for better filtering

    // Get user preferences if logged in
    let userPreferences = null;
    let userOrderHistory = [];
    if (userId) {
      try {
        userPreferences = await getUserPreferences(userId, null, 50);
        userOrderHistory = await Order.find({ userId })
          .populate("outletId", "name")
          .sort({ createdAt: -1 })
          .limit(100);
      } catch (error) {
        console.error("Error getting user preferences:", error);
      }
    }

    // Get real-time outlet data
    const outletAnalytics = await getOutletAnalytics(outlets.map((o) => o._id));

    // Calculate enhanced scores for each outlet
    const scoredOutlets = await Promise.all(
      outlets.map(async (outlet) => {
        const enhancedScore = await calculateEnhancedOutletScore(
          outlet,
          userPreferences,
          userOrderHistory,
          outletAnalytics[outlet._id.toString()],
          userLocation
        );

        return {
          ...outlet.toObject(),
          enhancedScore: enhancedScore.total,
          recommendationReasons: enhancedScore.reasons,
          matchingCuisines: enhancedScore.matchingCuisines,
          priceMatch: enhancedScore.priceMatch,
          distanceKm: enhancedScore.distanceKm,
          popularityScore: enhancedScore.popularityScore,
          qualityScore: enhancedScore.qualityScore,
          availabilityScore: enhancedScore.availabilityScore,
        };
      })
    );

    // Sort by enhanced score and apply final filtering
    const recommendations = scoredOutlets
      .filter((outlet) => outlet.enhancedScore > 0)
      .sort((a, b) => b.enhancedScore - a.enhancedScore)
      .slice(0, limit);

    return recommendations;
  } catch (error) {
    console.error("Error getting enhanced outlet recommendations:", error);
    // Fallback to basic recommendations
    return await getBasicOutletRecommendations(city, pincode, limit);
  }
};

/**
 * Calculate enhanced outlet score with multiple factors
 * @param {Object} outlet - Outlet object
 * @param {Object} userPreferences - User preferences
 * @param {Array} userOrderHistory - User's order history
 * @param {Object} outletAnalytics - Real-time outlet analytics
 * @param {Object} userLocation - User's location
 * @returns {Promise<Object>} - Enhanced score breakdown
 */
const calculateEnhancedOutletScore = async (
  outlet,
  userPreferences,
  userOrderHistory,
  outletAnalytics,
  userLocation
) => {
  let score = 0;
  const reasons = [];
  const matchingCuisines = [];
  let priceMatch = 0;
  let distanceKm = null;
  let popularityScore = 0;
  let qualityScore = 0;
  let availabilityScore = 0;

  // Base score for active outlet
  score += 10;

  // 1. User History Score (25% weight) - Highest priority for personalization
  if (userPreferences && userOrderHistory.length > 0) {
    const historyScore = calculateUserHistoryScore(
      outlet,
      userPreferences,
      userOrderHistory
    );
    score += historyScore.score * 0.25;
    reasons.push(...historyScore.reasons);
    matchingCuisines.push(...historyScore.matchingCuisines);
    priceMatch = historyScore.priceMatch;
  }

  // 2. Location & Distance Score (20% weight)
  if (userLocation && outlet.location) {
    const locationScore = calculateLocationScore(outlet, userLocation);
    score += locationScore.score * 0.2;
    distanceKm = locationScore.distanceKm;
    if (locationScore.reasons.length > 0) {
      reasons.push(...locationScore.reasons);
    }
  }

  // 3. Popularity & Social Proof Score (20% weight)
  if (outletAnalytics) {
    popularityScore = calculatePopularityScore(outletAnalytics);
    score += popularityScore * 0.2;
    if (popularityScore > 15) {
      reasons.push("Popular choice in your area");
    }
  }

  // 4. Quality Score (15% weight)
  qualityScore = calculateQualityScore(outlet);
  score += qualityScore * 0.15;
  if (qualityScore > 20) {
    reasons.push("High-quality dishes");
  }

  // 5. Availability & Operational Score (10% weight)
  availabilityScore = calculateAvailabilityScore(outlet);
  score += availabilityScore * 0.1;
  if (availabilityScore > 15) {
    reasons.push("Currently open and available");
  }

  // 6. Time-based Preferences (10% weight)
  if (userPreferences && userPreferences.timeBasedPreferences) {
    const timeScore = calculateTimeBasedOutletScore(
      outlet,
      userPreferences.timeBasedPreferences
    );
    score += timeScore.score * 0.1;
    if (timeScore.reasons.length > 0) {
      reasons.push(...timeScore.reasons);
    }
  }

  // Bonus factors
  if (
    outlet.isCloudKitchen &&
    userPreferences?.behaviorPatterns?.orderFrequency === "high"
  ) {
    score += 5;
    reasons.push("Convenient delivery option");
  }

  // Add slight randomness to avoid always showing same outlets
  score += Math.random() * 2;

  return {
    total: Math.round(score * 10) / 10,
    reasons: reasons.slice(0, 4), // Limit to top 4 reasons
    matchingCuisines: [...new Set(matchingCuisines)], // Remove duplicates
    priceMatch: Math.round(priceMatch),
    distanceKm,
    popularityScore: Math.round(popularityScore),
    qualityScore: Math.round(qualityScore),
    availabilityScore: Math.round(availabilityScore),
  };
};

/**
 * Calculate user history-based score
 * @param {Object} outlet - Outlet object
 * @param {Object} userPreferences - User preferences
 * @param {Array} userOrderHistory - User's order history
 * @returns {Object} - History score breakdown
 */
const calculateUserHistoryScore = (
  outlet,
  userPreferences,
  userOrderHistory
) => {
  let score = 0;
  const reasons = [];
  const matchingCuisines = [];
  let priceMatch = 0;

  // Check if user has visited this outlet before
  const outletId = outlet._id.toString();
  const visitCount = userOrderHistory.filter(
    (order) => order.outletId && order.outletId._id.toString() === outletId
  ).length;

  if (visitCount > 0) {
    score += Math.min(visitCount * 8, 30); // Cap at 30 points
    reasons.push(
      `You've ordered from here ${visitCount} time${
        visitCount > 1 ? "s" : ""
      } before`
    );
  }

  // Analyze outlet's dishes for preference matching
  if (outlet.dishes && outlet.dishes.length > 0) {
    const outletCuisines = {};
    const outletPrices = { low: 0, medium: 0, high: 0 };
    const outletVegOptions = { veg: 0, nonVeg: 0 };

    outlet.dishes.forEach((dish) => {
      // Count cuisines
      if (dish.cuisine) {
        outletCuisines[dish.cuisine] = (outletCuisines[dish.cuisine] || 0) + 1;
      }

      // Count price ranges
      const price = dish.price || 0;
      if (price < 200) {
        outletPrices.low++;
      } else if (price < 500) {
        outletPrices.medium++;
      } else {
        outletPrices.high++;
      }

      // Count veg options
      if (dish.isVeg) {
        outletVegOptions.veg++;
      } else {
        outletVegOptions.nonVeg++;
      }
    });

    // Score based on cuisine preferences
    if (userPreferences.preferences.favoriteCuisines.length > 0) {
      userPreferences.preferences.favoriteCuisines.forEach((userCuisine) => {
        if (outletCuisines[userCuisine.cuisine]) {
          const outletCoverage =
            (outletCuisines[userCuisine.cuisine] / outlet.dishes.length) * 100;
          const cuisineScore = (userCuisine.percentage * outletCoverage) / 100;
          score += cuisineScore * 0.3;

          if (cuisineScore > 5) {
            matchingCuisines.push(userCuisine.cuisine);
            reasons.push(
              `Offers ${userCuisine.cuisine} cuisine (${userCuisine.percentage}% of your orders)`
            );
          }
        }
      });
    }

    // Score based on price preferences
    const totalDishes = outlet.dishes.length;
    if (totalDishes > 0) {
      const outletPriceDistribution = {
        low: (outletPrices.low / totalDishes) * 100,
        medium: (outletPrices.medium / totalDishes) * 100,
        high: (outletPrices.high / totalDishes) * 100,
      };

      priceMatch =
        userPreferences.preferences.pricePreference.low *
          outletPriceDistribution.low +
        userPreferences.preferences.pricePreference.medium *
          outletPriceDistribution.medium +
        userPreferences.preferences.pricePreference.high *
          outletPriceDistribution.high;

      score += (priceMatch / 100) * 15;

      if (priceMatch > 40) {
        reasons.push(
          `Price range matches your preferences (${Math.round(
            priceMatch
          )}% match)`
        );
      }
    }

    // Score based on veg preference
    if (
      userPreferences.preferences.vegPreference.vegetarian > 70 &&
      outletVegOptions.veg > outletVegOptions.nonVeg
    ) {
      score += 10;
      reasons.push("Great vegetarian options");
    } else if (
      userPreferences.preferences.vegPreference.nonVegetarian > 70 &&
      outletVegOptions.nonVeg > outletVegOptions.veg
    ) {
      score += 10;
      reasons.push("Great non-vegetarian options");
    }
  }

  return { score, reasons, matchingCuisines, priceMatch };
};

/**
 * Calculate location-based score
 * @param {Object} outlet - Outlet object
 * @param {Object} userLocation - User's location {lat, lng}
 * @returns {Object} - Location score breakdown
 */
const calculateLocationScore = (outlet, userLocation) => {
  let score = 0;
  const reasons = [];
  let distanceKm = null;

  if (
    outlet.location &&
    outlet.location.coordinates &&
    userLocation.lat &&
    userLocation.lng
  ) {
    // Calculate distance using Haversine formula
    distanceKm = calculateDistance(
      userLocation.lat,
      userLocation.lng,
      outlet.location.coordinates[1], // latitude
      outlet.location.coordinates[0] // longitude
    );

    // Score based on distance (closer is better)
    if (distanceKm <= 2) {
      score += 25;
      reasons.push("Very close to you");
    } else if (distanceKm <= 5) {
      score += 20;
      reasons.push("Nearby location");
    } else if (distanceKm <= 10) {
      score += 15;
      reasons.push("Within delivery range");
    } else if (distanceKm <= 20) {
      score += 10;
    } else {
      score += 5; // Still give some points for being in the same city
    }

    // Bonus for cloud kitchens if distance is reasonable
    if (outlet.isCloudKitchen && distanceKm <= outlet.deliveryRadius) {
      score += 5;
      reasons.push("Delivers to your area");
    }
  }

  return { score, reasons, distanceKm };
};

/**
 * Calculate popularity score based on analytics
 * @param {Object} analytics - Outlet analytics data
 * @returns {number} - Popularity score
 */
const calculatePopularityScore = (analytics) => {
  if (!analytics) return 0;

  let score = 0;

  // Recent order volume (last 7 days)
  const recentOrders = analytics.recentOrderCount || 0;
  score += Math.min(recentOrders * 0.5, 15); // Cap at 15 points

  // Customer retention rate
  const retentionRate = analytics.customerRetentionRate || 0;
  score += (retentionRate / 100) * 10; // Up to 10 points

  // Average rating
  const avgRating = analytics.averageRating || 0;
  if (avgRating >= 4.5) score += 10;
  else if (avgRating >= 4.0) score += 7;
  else if (avgRating >= 3.5) score += 5;

  // Peak hour performance
  const peakPerformance = analytics.peakHourPerformance || 0;
  score += (peakPerformance / 100) * 5;

  return Math.min(score, 25); // Cap at 25 points
};

/**
 * Calculate quality score based on outlet data
 * @param {Object} outlet - Outlet object
 * @returns {number} - Quality score
 */
const calculateQualityScore = (outlet) => {
  let score = 0;

  // Menu variety
  const dishCount = outlet.dishes ? outlet.dishes.length : 0;
  score += Math.min(dishCount * 0.2, 10); // Up to 10 points for variety

  // Cuisine diversity
  if (outlet.dishes && outlet.dishes.length > 0) {
    const cuisines = new Set(
      outlet.dishes.map((dish) => dish.cuisine).filter(Boolean)
    );
    score += Math.min(cuisines.size * 2, 8); // Up to 8 points for diversity
  }

  // Average dish ratings
  if (outlet.dishes && outlet.dishes.length > 0) {
    const ratedDishes = outlet.dishes.filter(
      (dish) => dish.ratings && dish.ratings.average > 0
    );
    if (ratedDishes.length > 0) {
      const avgDishRating =
        ratedDishes.reduce((sum, dish) => sum + dish.ratings.average, 0) /
        ratedDishes.length;
      score += (avgDishRating / 5) * 10; // Up to 10 points
    }
  }

  // Balanced veg/non-veg options
  if (outlet.dishes && outlet.dishes.length > 5) {
    const vegCount = outlet.dishes.filter((dish) => dish.isVeg).length;
    const nonVegCount = outlet.dishes.length - vegCount;
    if (vegCount > 0 && nonVegCount > 0) {
      score += 5; // Bonus for balanced options
    }
  }

  return Math.min(score, 25); // Cap at 25 points
};

/**
 * Calculate availability score
 * @param {Object} outlet - Outlet object
 * @returns {number} - Availability score
 */
const calculateAvailabilityScore = (outlet) => {
  let score = 0;
  const now = new Date();
  const currentHour = now.getHours();
  const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

  // Check if outlet is currently open
  if (outlet.operatingHours && outlet.operatingHours.length > 0) {
    const todayHours = outlet.operatingHours.find((hours) => {
      // Map day numbers to match your schema
      const dayMap = {
        0: "sunday",
        1: "monday",
        2: "tuesday",
        3: "wednesday",
        4: "thursday",
        5: "friday",
        6: "saturday",
      };
      return hours.day === dayMap[currentDay];
    });

    if (todayHours && !todayHours.isClosed) {
      const openHour = parseInt(todayHours.openTime.split(":")[0]);
      const closeHour = parseInt(todayHours.closeTime.split(":")[0]);

      if (currentHour >= openHour && currentHour < closeHour) {
        score += 15; // Currently open
      } else if (
        Math.abs(currentHour - openHour) <= 2 ||
        Math.abs(currentHour - closeHour) <= 2
      ) {
        score += 10; // Opening/closing soon
      } else {
        score += 5; // Will be open today
      }
    }
  } else {
    score += 10; // Assume open if no hours specified
  }

  // Delivery availability for cloud kitchens
  if (outlet.isCloudKitchen) {
    score += 5; // Bonus for delivery availability
  }

  return Math.min(score, 20); // Cap at 20 points
};

/**
 * Calculate time-based outlet score
 * @param {Object} outlet - Outlet object
 * @param {Object} timeBasedPreferences - User's time-based preferences
 * @returns {Object} - Time-based score breakdown
 */
const calculateTimeBasedOutletScore = (outlet, timeBasedPreferences) => {
  let score = 0;
  const reasons = [];
  const now = new Date();
  const currentTimeSlot = getCurrentTimeSlot(now.getHours());
  const currentDayType =
    now.getDay() === 0 || now.getDay() === 6 ? "weekend" : "weekday";

  // Check if user has preferences for current time slot
  if (
    timeBasedPreferences.timeSlots &&
    timeBasedPreferences.timeSlots[currentTimeSlot]
  ) {
    const timeSlotData = timeBasedPreferences.timeSlots[currentTimeSlot];

    if (timeSlotData.count > 0 && outlet.dishes && outlet.dishes.length > 0) {
      // Check if outlet serves cuisines user prefers at this time
      const preferredCuisines = Object.keys(timeSlotData.cuisines);
      const outletCuisines = outlet.dishes
        .map((dish) => dish.cuisine)
        .filter(Boolean);

      const matchingCuisines = preferredCuisines.filter((cuisine) =>
        outletCuisines.includes(cuisine)
      );

      if (matchingCuisines.length > 0) {
        score += matchingCuisines.length * 3;
        reasons.push(`Great for ${currentTimeSlot} dining`);
      }
    }
  }

  // Check day of week preferences
  if (
    timeBasedPreferences.dayOfWeek &&
    timeBasedPreferences.dayOfWeek[currentDayType]
  ) {
    const dayTypeData = timeBasedPreferences.dayOfWeek[currentDayType];

    if (dayTypeData.count > 0) {
      score += 5;
      reasons.push(`Perfect for ${currentDayType}s`);
    }
  }

  return { score, reasons };
};

/**
 * Get outlet analytics data
 * @param {Array} outletIds - Array of outlet IDs
 * @returns {Promise<Object>} - Analytics data by outlet ID
 */
const getOutletAnalytics = async (outletIds) => {
  try {
    const analytics = {};

    // Get recent order counts (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentOrderCounts = await Order.aggregate([
      {
        $match: {
          outletId: { $in: outletIds },
          createdAt: { $gte: sevenDaysAgo },
          status: { $in: ["completed", "delivered"] },
        },
      },
      {
        $group: {
          _id: "$outletId",
          recentOrderCount: { $sum: 1 },
          averageOrderValue: { $avg: "$finalAmount" },
        },
      },
    ]);

    // Get customer retention rates (customers who ordered more than once)
    const retentionData = await Order.aggregate([
      {
        $match: {
          outletId: { $in: outletIds },
          status: { $in: ["completed", "delivered"] },
        },
      },
      {
        $group: {
          _id: { outletId: "$outletId", userId: "$userId" },
          orderCount: { $sum: 1 },
        },
      },
      {
        $group: {
          _id: "$_id.outletId",
          totalCustomers: { $sum: 1 },
          returningCustomers: {
            $sum: { $cond: [{ $gt: ["$orderCount", 1] }, 1, 0] },
          },
        },
      },
      {
        $project: {
          customerRetentionRate: {
            $multiply: [
              { $divide: ["$returningCustomers", "$totalCustomers"] },
              100,
            ],
          },
        },
      },
    ]);

    // Initialize analytics for all outlets
    outletIds.forEach((id) => {
      analytics[id.toString()] = {
        recentOrderCount: 0,
        customerRetentionRate: 0,
        averageRating: 0,
        peakHourPerformance: 75, // Default value
      };
    });

    // Populate with actual data
    recentOrderCounts.forEach((data) => {
      const outletId = data._id.toString();
      analytics[outletId].recentOrderCount = data.recentOrderCount;
    });

    retentionData.forEach((data) => {
      const outletId = data._id.toString();
      analytics[outletId].customerRetentionRate = data.customerRetentionRate;
    });

    return analytics;
  } catch (error) {
    console.error("Error getting outlet analytics:", error);
    return {};
  }
};

/**
 * Calculate distance between two points using Haversine formula
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lon1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lon2 - Longitude of point 2
 * @returns {number} - Distance in kilometers
 */
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

/**
 * Get current time slot
 * @param {number} hour - Current hour (0-23)
 * @returns {string} - Time slot
 */
const getCurrentTimeSlot = (hour) => {
  if (hour >= 6 && hour < 12) return "morning";
  if (hour >= 12 && hour < 17) return "afternoon";
  if (hour >= 17 && hour < 22) return "evening";
  return "night";
};

/**
 * Fallback basic outlet recommendations
 * @param {string} city - City filter
 * @param {string} pincode - Pincode filter
 * @param {number} limit - Number of recommendations
 * @returns {Promise<Array>} - Basic recommendations
 */
const getBasicOutletRecommendations = async (city, pincode, limit) => {
  try {
    const query = { status: "active" };

    if (city) {
      query.city = { $regex: city, $options: "i" };
    }

    if (pincode) {
      query.pincode = pincode;
    }

    const outlets = await Outlet.find(query)
      .populate("foodChain", "name theme")
      .populate("dishes", "name category cuisine isVeg price")
      .select(
        "name address city pincode contact isCloudKitchen deliveryRadius dishes"
      )
      .limit(limit);

    return outlets.map((outlet) => ({
      ...outlet.toObject(),
      enhancedScore: 10,
      recommendationReasons: ["Available in your area"],
      matchingCuisines: [],
      priceMatch: 0,
    }));
  } catch (error) {
    console.error("Error getting basic outlet recommendations:", error);
    return [];
  }
};
