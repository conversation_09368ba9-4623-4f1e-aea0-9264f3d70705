import Dish from "../models/Dish.js";
import { generateEmbedding } from "./vector-service.js";

/**
 * MongoDB Atlas Vector Search Service
 * Leverages the butler-dishes vector search index for fast and relevant dish recommendations
 */

/**
 * Search dishes using MongoDB Atlas Vector Search
 * @param {string} query - The search query
 * @param {string} foodChainId - The food chain ID to filter by
 * @param {string} outletId - The outlet ID to filter by (optional)
 * @param {Object} options - Search options
 * @returns {Promise<Array<Object>>} - Array of relevant dishes with scores
 */
export const searchDishesWithAtlasVector = async (
  query,
  foodChainId,
  outletId = null,
  options = {}
) => {
  try {
    const {
      limit = 10,
      minScore = 0.1,
      includeMetadata = true,
      filters = {},
    } = options;

    console.log(
      `🔍 Atlas Vector Search: "${query}" for foodChain: ${foodChainId}`
    );

    // Generate embedding for the query using the same model as the index
    const queryEmbedding = await generateEmbedding(query);

    // Build the aggregation pipeline for Atlas Vector Search
    const pipeline = [
      {
        $vectorSearch: {
          index: "butler-dishes", // Your Atlas Vector Search index name
          path: "embedding", // Field containing the vector embeddings
          queryVector: queryEmbedding,
          numCandidates: Math.max(limit * 10, 100), // Search more candidates for better results
          limit: limit,
          filter: buildAtlasSearchFilter(foodChainId, outletId, filters),
        },
      },
      {
        $addFields: {
          vectorScore: { $meta: "vectorSearchScore" },
        },
      },
      {
        $match: {
          vectorScore: { $gte: minScore },
        },
      },
    ];

    // Add population stages if needed
    if (includeMetadata) {
      pipeline.push(
        {
          $lookup: {
            from: "categories",
            localField: "category",
            foreignField: "_id",
            as: "category",
          },
        },
        {
          $unwind: {
            path: "$category",
            preserveNullAndEmptyArrays: true,
          },
        }
      );
    }

    // Execute the vector search
    const results = await Dish.aggregate(pipeline);

    console.log(`✅ Atlas Vector Search found ${results.length} results`);

    // Transform results to match expected format
    return results.map((dish) => ({
      ...dish,
      relevanceScore: dish.vectorScore,
      searchMethod: "atlas_vector_search",
    }));
  } catch (error) {
    console.error("❌ Error in Atlas Vector Search:", error);

    // Return empty array on error - let fallback mechanisms handle it
    return [];
  }
};

/**
 * Build filter object for Atlas Vector Search
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID (optional)
 * @param {Object} additionalFilters - Additional filters
 * @returns {Object} - MongoDB filter object
 */
const buildAtlasSearchFilter = (
  foodChainId,
  outletId,
  additionalFilters = {}
) => {
  const filter = {
    foodChain: { $eq: foodChainId },
    isAvailable: { $eq: true },
  };

  // Add outlet filter if provided
  if (outletId) {
    filter.outlets = { $in: [outletId] };
  }

  // Add dietary filters
  if (additionalFilters.isVeg !== undefined) {
    filter.isVeg = { $eq: additionalFilters.isVeg };
  }

  // Add price range filter
  if (additionalFilters.priceRange) {
    const { min, max } = additionalFilters.priceRange;
    if (min !== undefined) filter.price = { ...filter.price, $gte: min };
    if (max !== undefined) filter.price = { ...filter.price, $lte: max };
  }

  // Add category filter
  if (additionalFilters.categoryId) {
    filter.category = { $eq: additionalFilters.categoryId };
  }

  // Add cuisine filter
  if (additionalFilters.cuisine) {
    filter.cuisine = { $regex: additionalFilters.cuisine, $options: "i" };
  }

  return filter;
};

/**
 * Get similar dishes using Atlas Vector Search with enhanced filtering
 * @param {string} query - Search query
 * @param {Array} availableDishes - Pre-filtered available dishes
 * @param {Object} context - Additional context for filtering
 * @returns {Promise<Array>} - Filtered and ranked dishes
 */
export const getAtlasVectorRecommendations = async (
  query,
  availableDishes,
  context = {}
) => {
  try {
    const {
      foodChainId,
      outletId,
      userId,
      limit = 8,
      userPreferences = {},
    } = context;

    if (!foodChainId) {
      console.warn("⚠️ No foodChainId provided for Atlas Vector Search");
      return [];
    }

    // Extract dish IDs from available dishes for filtering
    const availableDishIds = availableDishes.map((dish) =>
      dish._id ? dish._id.toString() : dish.toString()
    );

    // Perform Atlas Vector Search
    const vectorResults = await searchDishesWithAtlasVector(
      query,
      foodChainId,
      outletId,
      {
        limit: Math.min(limit * 2, 20), // Get more results for better filtering
        minScore: 0.05,
        filters: {
          isVeg: userPreferences.isVeg,
          priceRange: userPreferences.priceRange,
          cuisine: userPreferences.preferredCuisine,
        },
      }
    );

    if (vectorResults.length === 0) {
      console.log("📭 No results from Atlas Vector Search");
      return [];
    }

    // Filter results to only include available dishes
    const filteredResults = vectorResults.filter((dish) =>
      availableDishIds.includes(dish._id.toString())
    );

    // Apply user history boost if available
    if (userPreferences.dishFrequency) {
      filteredResults.forEach((dish) => {
        const historyScore =
          userPreferences.dishFrequency[dish._id.toString()] || 0;
        dish.relevanceScore = dish.relevanceScore * 0.7 + historyScore * 0.3;
      });
    }

    // Sort by relevance score and limit results
    const finalResults = filteredResults
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);

    console.log(
      `🎯 Atlas Vector Search returned ${finalResults.length} filtered results`
    );

    return finalResults;
  } catch (error) {
    console.error("❌ Error in Atlas Vector Recommendations:", error);
    return [];
  }
};

/**
 * Check if Atlas Vector Search is available and properly configured
 * @returns {Promise<boolean>} - True if Atlas Vector Search is available
 */
export const isAtlasVectorSearchAvailable = async () => {
  try {
    // Test with a simple query to check if the index exists
    const testPipeline = [
      {
        $vectorSearch: {
          index: "butler-dishes",
          path: "embedding",
          queryVector: new Array(384).fill(0.1), // Test vector
          numCandidates: 1,
          limit: 1,
        },
      },
      { $limit: 1 },
    ];

    await Dish.aggregate(testPipeline);
    console.log("✅ Atlas Vector Search is available");
    return true;
  } catch (error) {
    console.warn("⚠️ Atlas Vector Search not available:", error.message);
    return false;
  }
};

/**
 * Hybrid search combining Atlas Vector Search with traditional text search
 * @param {string} query - Search query
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @param {Object} options - Search options
 * @returns {Promise<Array>} - Combined search results
 */
export const hybridDishSearch = async (
  query,
  foodChainId,
  outletId,
  options = {}
) => {
  try {
    const { limit = 10, vectorWeight = 0.7, textWeight = 0.3 } = options;

    // Perform both vector and text search in parallel
    const [vectorResults, textResults] = await Promise.all([
      searchDishesWithAtlasVector(query, foodChainId, outletId, {
        limit: Math.ceil(limit * 1.5),
      }),
      performTextSearch(query, foodChainId, outletId, {
        limit: Math.ceil(limit * 1.5),
      }),
    ]);

    // Combine and deduplicate results
    const combinedResults = new Map();

    // Add vector search results
    vectorResults.forEach((dish) => {
      combinedResults.set(dish._id.toString(), {
        ...dish,
        vectorScore: dish.relevanceScore || 0,
        textScore: 0,
        combinedScore: (dish.relevanceScore || 0) * vectorWeight,
      });
    });

    // Add text search results and combine scores
    textResults.forEach((dish) => {
      const dishId = dish._id.toString();
      if (combinedResults.has(dishId)) {
        const existing = combinedResults.get(dishId);
        existing.textScore = dish.relevanceScore || 0;
        existing.combinedScore =
          existing.vectorScore * vectorWeight +
          (dish.relevanceScore || 0) * textWeight;
      } else {
        combinedResults.set(dishId, {
          ...dish,
          vectorScore: 0,
          textScore: dish.relevanceScore || 0,
          combinedScore: (dish.relevanceScore || 0) * textWeight,
        });
      }
    });

    // Sort by combined score and return top results
    return Array.from(combinedResults.values())
      .sort((a, b) => b.combinedScore - a.combinedScore)
      .slice(0, limit)
      .map((dish) => ({
        ...dish,
        relevanceScore: dish.combinedScore,
        searchMethod: "hybrid_search",
      }));
  } catch (error) {
    console.error("❌ Error in hybrid search:", error);
    return [];
  }
};

/**
 * Simple text search fallback
 * @param {string} query - Search query
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @param {Object} options - Search options
 * @returns {Promise<Array>} - Text search results
 */
const performTextSearch = async (
  query,
  foodChainId,
  outletId,
  options = {}
) => {
  try {
    const { limit = 10 } = options;

    const searchFilter = {
      foodChain: foodChainId,
      isAvailable: true,
      $or: [
        { name: { $regex: query, $options: "i" } },
        { description: { $regex: query, $options: "i" } },
        { tags: { $in: [new RegExp(query, "i")] } },
        { cuisine: { $regex: query, $options: "i" } },
      ],
    };

    if (outletId) {
      searchFilter.outlets = { $in: [outletId] };
    }

    const results = await Dish.find(searchFilter)
      .populate("category", "name")
      .limit(limit)
      .lean();

    return results.map((dish) => ({
      ...dish,
      relevanceScore: 0.5, // Default text search score
      searchMethod: "text_search",
    }));
  } catch (error) {
    console.error("❌ Error in text search:", error);
    return [];
  }
};
