import mongoose from 'mongoose';

/**
 * Recommendation Analytics and Feedback Service
 * Tracks recommendation effectiveness, user feedback, and provides insights for continuous improvement
 */

// Schema for recommendation analytics
const RecommendationAnalyticsSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  outletId: { type: mongoose.Schema.Types.ObjectId, ref: 'Outlet' },
  sessionId: { type: String, required: true },
  recommendationType: { 
    type: String, 
    enum: ['dish', 'outlet', 'menu_personalization'], 
    required: true 
  },
  recommendations: [{
    itemId: { type: mongoose.Schema.Types.ObjectId, required: true },
    itemType: { type: String, enum: ['dish', 'outlet'], required: true },
    itemName: { type: String, required: true },
    score: { type: Number, required: true },
    reason: { type: String },
    position: { type: Number, required: true }, // Position in recommendation list
    wasClicked: { type: Boolean, default: false },
    wasOrdered: { type: Boolean, default: false },
    clickedAt: { type: Date },
    orderedAt: { type: Date },
    userFeedback: {
      rating: { type: Number, min: 1, max: 5 },
      helpful: { type: Boolean },
      reason: { type: String },
      submittedAt: { type: Date }
    }
  }],
  context: {
    timeSlot: { type: String },
    dayOfWeek: { type: String },
    season: { type: String },
    userQuery: { type: String },
    previousOrders: { type: Number },
    userPreferenceConfidence: { type: Number }
  },
  performance: {
    responseTime: { type: Number }, // in milliseconds
    fromCache: { type: Boolean, default: false },
    algorithmsUsed: [{ type: String }]
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Schema for user feedback
const UserFeedbackSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  feedbackType: { 
    type: String, 
    enum: ['recommendation_quality', 'personalization_accuracy', 'general'], 
    required: true 
  },
  rating: { type: Number, min: 1, max: 5, required: true },
  comment: { type: String },
  context: {
    page: { type: String },
    feature: { type: String },
    sessionId: { type: String }
  },
  createdAt: { type: Date, default: Date.now }
});

const RecommendationAnalytics = mongoose.model('RecommendationAnalytics', RecommendationAnalyticsSchema);
const UserFeedback = mongoose.model('UserFeedback', UserFeedbackSchema);

/**
 * Track recommendation display
 * @param {Object} data - Recommendation tracking data
 */
export const trackRecommendationDisplay = async (data) => {
  try {
    const {
      userId,
      outletId,
      sessionId,
      recommendationType,
      recommendations,
      context,
      performance
    } = data;

    const analytics = new RecommendationAnalytics({
      userId,
      outletId,
      sessionId,
      recommendationType,
      recommendations: recommendations.map((rec, index) => ({
        itemId: rec._id || rec.id,
        itemType: recommendationType === 'outlet' ? 'outlet' : 'dish',
        itemName: rec.name,
        score: rec.enhancedScore || rec.personalizationScore || rec.score || 0,
        reason: rec.recommendationReason || rec.reason || 'Recommended for you',
        position: index + 1,
        wasClicked: false,
        wasOrdered: false
      })),
      context,
      performance
    });

    await analytics.save();
    return analytics._id;
  } catch (error) {
    console.error("Error tracking recommendation display:", error);
    return null;
  }
};

/**
 * Track recommendation interaction (click, view, etc.)
 * @param {string} analyticsId - Analytics record ID
 * @param {string} itemId - Item ID that was interacted with
 * @param {string} interactionType - Type of interaction
 */
export const trackRecommendationInteraction = async (analyticsId, itemId, interactionType) => {
  try {
    const updateData = {};
    const timestamp = new Date();

    if (interactionType === 'click') {
      updateData['recommendations.$.wasClicked'] = true;
      updateData['recommendations.$.clickedAt'] = timestamp;
    } else if (interactionType === 'order') {
      updateData['recommendations.$.wasOrdered'] = true;
      updateData['recommendations.$.orderedAt'] = timestamp;
    }

    await RecommendationAnalytics.updateOne(
      { 
        _id: analyticsId,
        'recommendations.itemId': itemId
      },
      { 
        $set: updateData,
        updatedAt: timestamp
      }
    );
  } catch (error) {
    console.error("Error tracking recommendation interaction:", error);
  }
};

/**
 * Submit user feedback for recommendations
 * @param {Object} feedbackData - Feedback data
 */
export const submitRecommendationFeedback = async (feedbackData) => {
  try {
    const {
      analyticsId,
      itemId,
      rating,
      helpful,
      reason,
      userId,
      feedbackType = 'recommendation_quality',
      comment,
      context
    } = feedbackData;

    // Update specific recommendation feedback
    if (analyticsId && itemId) {
      await RecommendationAnalytics.updateOne(
        { 
          _id: analyticsId,
          'recommendations.itemId': itemId
        },
        { 
          $set: {
            'recommendations.$.userFeedback': {
              rating,
              helpful,
              reason,
              submittedAt: new Date()
            },
            updatedAt: new Date()
          }
        }
      );
    }

    // Create general feedback record
    const feedback = new UserFeedback({
      userId,
      feedbackType,
      rating,
      comment,
      context
    });

    await feedback.save();
    return feedback._id;
  } catch (error) {
    console.error("Error submitting recommendation feedback:", error);
    return null;
  }
};

/**
 * Get recommendation analytics for a specific period
 * @param {Object} filters - Analytics filters
 * @returns {Promise<Object>} - Analytics data
 */
export const getRecommendationAnalytics = async (filters = {}) => {
  try {
    const {
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
      endDate = new Date(),
      userId,
      outletId,
      recommendationType
    } = filters;

    const matchStage = {
      createdAt: { $gte: startDate, $lte: endDate }
    };

    if (userId) matchStage.userId = new mongoose.Types.ObjectId(userId);
    if (outletId) matchStage.outletId = new mongoose.Types.ObjectId(outletId);
    if (recommendationType) matchStage.recommendationType = recommendationType;

    const analytics = await RecommendationAnalytics.aggregate([
      { $match: matchStage },
      { $unwind: '$recommendations' },
      {
        $group: {
          _id: null,
          totalRecommendations: { $sum: 1 },
          totalClicks: { $sum: { $cond: ['$recommendations.wasClicked', 1, 0] } },
          totalOrders: { $sum: { $cond: ['$recommendations.wasOrdered', 1, 0] } },
          avgScore: { $avg: '$recommendations.score' },
          avgResponseTime: { $avg: '$performance.responseTime' },
          cacheHitRate: { 
            $avg: { $cond: ['$performance.fromCache', 1, 0] } 
          },
          feedbackCount: {
            $sum: { 
              $cond: [
                { $ne: ['$recommendations.userFeedback.rating', null] }, 
                1, 
                0
              ] 
            }
          },
          avgFeedbackRating: {
            $avg: '$recommendations.userFeedback.rating'
          }
        }
      }
    ]);

    // Get top performing recommendations
    const topRecommendations = await RecommendationAnalytics.aggregate([
      { $match: matchStage },
      { $unwind: '$recommendations' },
      {
        $group: {
          _id: '$recommendations.itemId',
          itemName: { $first: '$recommendations.itemName' },
          itemType: { $first: '$recommendations.itemType' },
          totalShown: { $sum: 1 },
          totalClicks: { $sum: { $cond: ['$recommendations.wasClicked', 1, 0] } },
          totalOrders: { $sum: { $cond: ['$recommendations.wasOrdered', 1, 0] } },
          avgScore: { $avg: '$recommendations.score' },
          avgPosition: { $avg: '$recommendations.position' }
        }
      },
      {
        $addFields: {
          clickRate: { $divide: ['$totalClicks', '$totalShown'] },
          orderRate: { $divide: ['$totalOrders', '$totalShown'] }
        }
      },
      { $sort: { orderRate: -1 } },
      { $limit: 10 }
    ]);

    // Get performance by time slot
    const performanceByTimeSlot = await RecommendationAnalytics.aggregate([
      { $match: matchStage },
      { $unwind: '$recommendations' },
      {
        $group: {
          _id: '$context.timeSlot',
          totalRecommendations: { $sum: 1 },
          totalClicks: { $sum: { $cond: ['$recommendations.wasClicked', 1, 0] } },
          totalOrders: { $sum: { $cond: ['$recommendations.wasOrdered', 1, 0] } }
        }
      },
      {
        $addFields: {
          clickRate: { $divide: ['$totalClicks', '$totalRecommendations'] },
          orderRate: { $divide: ['$totalOrders', '$totalRecommendations'] }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    const result = {
      summary: analytics[0] || {
        totalRecommendations: 0,
        totalClicks: 0,
        totalOrders: 0,
        avgScore: 0,
        avgResponseTime: 0,
        cacheHitRate: 0,
        feedbackCount: 0,
        avgFeedbackRating: 0
      },
      topRecommendations,
      performanceByTimeSlot,
      period: { startDate, endDate }
    };

    // Calculate derived metrics
    if (result.summary.totalRecommendations > 0) {
      result.summary.clickThroughRate = result.summary.totalClicks / result.summary.totalRecommendations;
      result.summary.conversionRate = result.summary.totalOrders / result.summary.totalRecommendations;
      result.summary.feedbackRate = result.summary.feedbackCount / result.summary.totalRecommendations;
    }

    return result;
  } catch (error) {
    console.error("Error getting recommendation analytics:", error);
    return null;
  }
};

/**
 * Get user feedback analytics
 * @param {Object} filters - Feedback filters
 * @returns {Promise<Object>} - Feedback analytics
 */
export const getFeedbackAnalytics = async (filters = {}) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      endDate = new Date(),
      feedbackType
    } = filters;

    const matchStage = {
      createdAt: { $gte: startDate, $lte: endDate }
    };

    if (feedbackType) matchStage.feedbackType = feedbackType;

    const feedbackAnalytics = await UserFeedback.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$feedbackType',
          totalFeedback: { $sum: 1 },
          avgRating: { $avg: '$rating' },
          ratingDistribution: {
            $push: '$rating'
          }
        }
      }
    ]);

    // Calculate rating distribution
    const processedAnalytics = feedbackAnalytics.map(item => {
      const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      item.ratingDistribution.forEach(rating => {
        distribution[rating]++;
      });
      
      return {
        ...item,
        ratingDistribution: distribution
      };
    });

    return {
      feedbackAnalytics: processedAnalytics,
      period: { startDate, endDate }
    };
  } catch (error) {
    console.error("Error getting feedback analytics:", error);
    return null;
  }
};

/**
 * Generate recommendation insights for continuous improvement
 * @param {Object} filters - Analysis filters
 * @returns {Promise<Object>} - Insights and recommendations
 */
export const generateRecommendationInsights = async (filters = {}) => {
  try {
    const analytics = await getRecommendationAnalytics(filters);
    const feedbackAnalytics = await getFeedbackAnalytics(filters);
    
    if (!analytics) return null;

    const insights = {
      performance: {
        status: 'good',
        issues: [],
        recommendations: []
      },
      userSatisfaction: {
        status: 'good',
        issues: [],
        recommendations: []
      },
      systemHealth: {
        status: 'good',
        issues: [],
        recommendations: []
      }
    };

    // Analyze performance metrics
    if (analytics.summary.clickThroughRate < 0.1) {
      insights.performance.status = 'needs_improvement';
      insights.performance.issues.push('Low click-through rate');
      insights.performance.recommendations.push('Review recommendation relevance and positioning');
    }

    if (analytics.summary.conversionRate < 0.05) {
      insights.performance.status = 'needs_improvement';
      insights.performance.issues.push('Low conversion rate');
      insights.performance.recommendations.push('Improve recommendation quality and user targeting');
    }

    if (analytics.summary.avgResponseTime > 1000) {
      insights.systemHealth.status = 'needs_improvement';
      insights.systemHealth.issues.push('Slow response times');
      insights.systemHealth.recommendations.push('Optimize caching and database queries');
    }

    // Analyze user satisfaction
    if (analytics.summary.avgFeedbackRating < 3.5) {
      insights.userSatisfaction.status = 'needs_improvement';
      insights.userSatisfaction.issues.push('Low user satisfaction ratings');
      insights.userSatisfaction.recommendations.push('Review recommendation algorithms and user preferences');
    }

    if (analytics.summary.feedbackRate < 0.02) {
      insights.userSatisfaction.issues.push('Low feedback participation');
      insights.userSatisfaction.recommendations.push('Improve feedback collection mechanisms');
    }

    return {
      insights,
      analytics,
      feedbackAnalytics,
      generatedAt: new Date()
    };
  } catch (error) {
    console.error("Error generating recommendation insights:", error);
    return null;
  }
};

export { RecommendationAnalytics, UserFeedback };
