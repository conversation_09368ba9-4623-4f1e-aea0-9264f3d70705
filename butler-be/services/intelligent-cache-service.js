import NodeCache from 'node-cache';
import crypto from 'crypto';

/**
 * Intelligent Caching Service for Recommendations
 * Implements smart caching strategies with TTL, invalidation, and optimization
 */

// Multiple cache layers with different strategies
const caches = {
  // Quick responses for common queries (short TTL)
  quickResponse: new NodeCache({ 
    stdTTL: 180, // 3 minutes
    checkperiod: 30,
    useClones: false,
    maxKeys: 1000
  }),
  
  // User-specific caches (medium TTL)
  userSpecific: new NodeCache({ 
    stdTTL: 600, // 10 minutes
    checkperiod: 60,
    useClones: false,
    maxKeys: 5000
  }),
  
  // AI responses (longer TTL due to cost)
  aiResponses: new NodeCache({ 
    stdTTL: 1800, // 30 minutes
    checkperiod: 300,
    useClones: false,
    maxKeys: 2000
  }),
  
  // Menu metadata (very long TTL)
  menuMetadata: new NodeCache({ 
    stdTTL: 3600, // 1 hour
    checkperiod: 600,
    useClones: false,
    maxKeys: 500
  }),
  
  // Popular queries (adaptive TTL)
  popularQueries: new NodeCache({ 
    stdTTL: 900, // 15 minutes
    checkperiod: 120,
    useClones: false,
    maxKeys: 1500
  })
};

// Cache usage statistics
let cacheStats = {
  hits: { total: 0, byType: {} },
  misses: { total: 0, byType: {} },
  sets: { total: 0, byType: {} },
  invalidations: { total: 0, byType: {} },
  lastReset: new Date()
};

/**
 * Generate intelligent cache key based on multiple factors
 */
const generateCacheKey = (type, data) => {
  const keyData = {
    type,
    ...data,
    // Add timestamp for time-sensitive caches
    timeSlot: type.includes('time') ? Math.floor(Date.now() / (5 * 60 * 1000)) : undefined
  };
  
  const keyString = JSON.stringify(keyData, Object.keys(keyData).sort());
  return crypto.createHash('md5').update(keyString).digest('hex').substring(0, 16);
};

/**
 * Smart cache getter with fallback logic
 */
export const smartGet = (cacheType, key, fallbackCaches = []) => {
  const cache = caches[cacheType];
  if (!cache) return null;

  // Try primary cache
  let result = cache.get(key);
  if (result) {
    updateStats('hits', cacheType);
    return { ...result, cacheSource: cacheType };
  }

  // Try fallback caches
  for (const fallbackType of fallbackCaches) {
    const fallbackCache = caches[fallbackType];
    if (fallbackCache) {
      result = fallbackCache.get(key);
      if (result) {
        updateStats('hits', fallbackType);
        // Promote to primary cache with shorter TTL
        cache.set(key, result, Math.min(300, cache.options.stdTTL));
        return { ...result, cacheSource: fallbackType, promoted: true };
      }
    }
  }

  updateStats('misses', cacheType);
  return null;
};

/**
 * Smart cache setter with intelligent TTL
 */
export const smartSet = (cacheType, key, value, options = {}) => {
  const cache = caches[cacheType];
  if (!cache) return false;

  let ttl = options.ttl || cache.options.stdTTL;

  // Adaptive TTL based on value characteristics
  if (options.adaptive) {
    // Longer TTL for expensive operations
    if (value.tokenUsage && value.tokenUsage.estimatedTokens > 1000) {
      ttl = Math.max(ttl, 1800); // At least 30 minutes for expensive AI calls
    }
    
    // Shorter TTL for user-specific data
    if (key.includes('user_')) {
      ttl = Math.min(ttl, 600); // Max 10 minutes for user data
    }
    
    // Longer TTL for popular queries
    if (value.popularity && value.popularity > 10) {
      ttl = Math.max(ttl, 900); // At least 15 minutes for popular queries
    }
  }

  // Add metadata
  const cacheValue = {
    ...value,
    _cacheMetadata: {
      cachedAt: new Date(),
      ttl,
      cacheType,
      version: '1.0'
    }
  };

  const success = cache.set(key, cacheValue, ttl);
  if (success) {
    updateStats('sets', cacheType);
  }
  
  return success;
};

/**
 * Recommendation-specific cache operations
 */
export const recommendationCache = {
  // Cache user query results
  cacheQuery: (userId, query, results, context = {}) => {
    const key = generateCacheKey('query', { userId, query, outletId: context.outletId });
    return smartSet('userSpecific', key, {
      results,
      query,
      userId,
      context,
      popularity: context.popularity || 1
    }, { adaptive: true });
  },

  // Get cached query results
  getQuery: (userId, query, context = {}) => {
    const key = generateCacheKey('query', { userId, query, outletId: context.outletId });
    return smartGet('userSpecific', key, ['quickResponse', 'popularQueries']);
  },

  // Cache AI responses (expensive operations)
  cacheAIResponse: (prompt, response, tokenUsage = {}) => {
    const key = generateCacheKey('ai', { prompt: prompt.substring(0, 200) });
    return smartSet('aiResponses', key, {
      response,
      tokenUsage,
      promptHash: crypto.createHash('md5').update(prompt).digest('hex')
    }, { adaptive: true, ttl: 1800 });
  },

  // Get cached AI response
  getAIResponse: (prompt) => {
    const key = generateCacheKey('ai', { prompt: prompt.substring(0, 200) });
    const cached = smartGet('aiResponses', key);
    
    if (cached && cached.promptHash) {
      const currentHash = crypto.createHash('md5').update(prompt).digest('hex');
      if (cached.promptHash === currentHash) {
        return cached;
      }
    }
    
    return null;
  },

  // Cache pre-filter results
  cachePreFilter: (userId, query, filters, results) => {
    const key = generateCacheKey('prefilter', { userId, query, filters });
    return smartSet('quickResponse', key, {
      results,
      filters,
      generatedAt: new Date()
    });
  },

  // Get cached pre-filter results
  getPreFilter: (userId, query, filters) => {
    const key = generateCacheKey('prefilter', { userId, query, filters });
    return smartGet('quickResponse', key);
  },

  // Cache semantic search results
  cacheSemanticSearch: (query, results, searchStats) => {
    const key = generateCacheKey('semantic', { query });
    return smartSet('popularQueries', key, {
      results,
      searchStats,
      query
    });
  },

  // Get cached semantic search results
  getSemanticSearch: (query) => {
    const key = generateCacheKey('semantic', { query });
    return smartGet('popularQueries', key, ['quickResponse']);
  }
};

/**
 * Intelligent cache invalidation
 */
export const invalidateCache = {
  // Invalidate user-specific caches when user data changes
  userDataChanged: (userId) => {
    const userCache = caches.userSpecific;
    const keys = userCache.keys();
    let invalidated = 0;
    
    keys.forEach(key => {
      const value = userCache.get(key);
      if (value && value.userId === userId) {
        userCache.del(key);
        invalidated++;
      }
    });
    
    updateStats('invalidations', 'userSpecific', invalidated);
    return invalidated;
  },

  // Invalidate menu-related caches when menu changes
  menuChanged: (outletId, dishIds = []) => {
    let totalInvalidated = 0;
    
    // Invalidate all caches that might contain menu data
    Object.entries(caches).forEach(([cacheType, cache]) => {
      const keys = cache.keys();
      let invalidated = 0;
      
      keys.forEach(key => {
        const value = cache.get(key);
        if (value && (
          value.context?.outletId === outletId ||
          (dishIds.length > 0 && value.results?.some(dish => 
            dishIds.includes(dish._id?.toString())
          ))
        )) {
          cache.del(key);
          invalidated++;
        }
      });
      
      if (invalidated > 0) {
        updateStats('invalidations', cacheType, invalidated);
        totalInvalidated += invalidated;
      }
    });
    
    return totalInvalidated;
  },

  // Invalidate by pattern
  byPattern: (pattern, cacheTypes = Object.keys(caches)) => {
    let totalInvalidated = 0;
    
    cacheTypes.forEach(cacheType => {
      const cache = caches[cacheType];
      if (!cache) return;
      
      const keys = cache.keys();
      let invalidated = 0;
      
      keys.forEach(key => {
        if (key.includes(pattern)) {
          cache.del(key);
          invalidated++;
        }
      });
      
      if (invalidated > 0) {
        updateStats('invalidations', cacheType, invalidated);
        totalInvalidated += invalidated;
      }
    });
    
    return totalInvalidated;
  },

  // Clear all caches
  clearAll: () => {
    let totalCleared = 0;
    Object.entries(caches).forEach(([cacheType, cache]) => {
      const keyCount = cache.keys().length;
      cache.flushAll();
      updateStats('invalidations', cacheType, keyCount);
      totalCleared += keyCount;
    });
    return totalCleared;
  }
};

/**
 * Cache warming strategies
 */
export const warmCache = {
  // Warm popular queries
  popularQueries: async (popularQueries = []) => {
    // This would be called during low-traffic periods
    console.log(`Warming cache for ${popularQueries.length} popular queries`);
    // Implementation would pre-compute results for popular queries
  },

  // Warm user preferences
  userPreferences: async (activeUserIds = []) => {
    console.log(`Warming user preference cache for ${activeUserIds.length} users`);
    // Implementation would pre-load user preferences
  }
};

/**
 * Update cache statistics
 */
const updateStats = (operation, cacheType, count = 1) => {
  cacheStats[operation].total += count;
  if (!cacheStats[operation].byType[cacheType]) {
    cacheStats[operation].byType[cacheType] = 0;
  }
  cacheStats[operation].byType[cacheType] += count;
};

/**
 * Get comprehensive cache statistics
 */
export const getCacheStatistics = () => {
  const cacheDetails = {};
  
  Object.entries(caches).forEach(([type, cache]) => {
    const stats = cache.getStats();
    cacheDetails[type] = {
      keys: stats.keys,
      hits: stats.hits,
      misses: stats.misses,
      ksize: stats.ksize,
      vsize: stats.vsize
    };
  });

  return {
    ...cacheStats,
    cacheDetails,
    uptime: Date.now() - cacheStats.lastReset.getTime(),
    hitRate: cacheStats.hits.total / Math.max(1, cacheStats.hits.total + cacheStats.misses.total) * 100
  };
};

/**
 * Reset cache statistics
 */
export const resetCacheStatistics = () => {
  cacheStats = {
    hits: { total: 0, byType: {} },
    misses: { total: 0, byType: {} },
    sets: { total: 0, byType: {} },
    invalidations: { total: 0, byType: {} },
    lastReset: new Date()
  };
};

/**
 * Cache health check
 */
export const cacheHealthCheck = () => {
  const stats = getCacheStatistics();
  const health = {
    status: 'healthy',
    issues: [],
    recommendations: []
  };

  // Check hit rates
  if (stats.hitRate < 30) {
    health.issues.push('Low cache hit rate');
    health.recommendations.push('Consider adjusting TTL values or cache warming');
  }

  // Check memory usage
  Object.entries(stats.cacheDetails).forEach(([type, details]) => {
    if (details.keys > caches[type].options.maxKeys * 0.9) {
      health.issues.push(`${type} cache near capacity`);
      health.recommendations.push(`Consider increasing maxKeys for ${type} cache`);
    }
  });

  if (health.issues.length > 0) {
    health.status = 'warning';
  }

  return health;
};
