import Payment from "../models/Payment.js";
import Order from "../models/Order.js";
import { getPaymentLinkDetails } from "../utils/razorpay.js";
import { emitPaymentUpdate } from "../sockets/orderSocket.js";
import { autoTransferFundsToFoodChain } from "../controllers/razorpay-route-controller.js";
import {
  createOrderPaymentNotification,
  createFundTransferNotification,
} from "./payment-notification-service.js";
import connectDB from "../config/database.js";

/**
 * Check status of pending payments
 * @returns {Array} - Updated payments
 */
export const checkPendingPayments = async () => {
  try {
    await connectDB();

    // Find payments that are in 'created' status and older than 5 minutes
    const fiveMinutesAgo = new Date();
    fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5);

    const pendingPayments = await Payment.find({
      status: "created",
      createdAt: { $lt: fiveMinutesAgo },
      // Don't check payments that were recently checked
      $or: [
        { lastCheckedAt: { $exists: false } },
        { lastCheckedAt: { $lt: fiveMinutesAgo } },
      ],
    }).limit(20);

    if (pendingPayments.length === 0) {
      return [];
    }

    console.log(
      `Checking status of ${pendingPayments.length} pending payments`
    );
    const results = [];

    for (const payment of pendingPayments) {
      try {
        // Update last checked timestamp
        payment.lastCheckedAt = new Date();
        await payment.save();

        // Get payment link details from Razorpay
        const paymentLinkDetails = await getPaymentLinkDetails(
          payment.razorpayPaymentLinkId
        );

        // If payment status has changed, update it
        if (paymentLinkDetails.status !== payment.status) {
          const oldStatus = payment.status;
          payment.status = paymentLinkDetails.status;

          // If payment is paid, update payment ID
          if (
            paymentLinkDetails.status === "paid" &&
            paymentLinkDetails.payments &&
            paymentLinkDetails.payments.length > 0
          ) {
            payment.razorpayPaymentId =
              paymentLinkDetails.payments[0].payment_id;
          }

          await payment.save();
          console.log(
            `Updated payment ${payment._id} status from ${oldStatus} to ${payment.status}`
          );

          // If payment is paid, update order and initiate fund transfer
          if (payment.status === "paid") {
            const order = await Order.findById(payment.orderId).populate(
              "foodChainId"
            );
            if (order) {
              order.paymentStatus = "paid";
              await order.save();

              // Create payment success notification
              await createOrderPaymentNotification(order, payment, "paid");

              // Initiate fund transfer if payment is successful
              if (payment.razorpayPaymentId) {
                try {
                  const transferResult = await autoTransferFundsToFoodChain(
                    payment.razorpayPaymentId,
                    order
                  );

                  if (transferResult) {
                    console.log(
                      `Automatic fund transfer initiated for order ${order._id} using Razorpay Route`
                    );
                    // Create fund transfer notification
                    await createFundTransferNotification(
                      transferResult.fundTransfer,
                      order
                    );
                  } else {
                    console.log(
                      `Automatic fund transfer failed for order ${order._id}. Manual intervention may be required.`
                    );
                    // Create transfer failed notification
                    await createOrderPaymentNotification(
                      order,
                      payment,
                      "transfer_failed",
                      {
                        error:
                          "Automatic fund transfer failed. Manual intervention required.",
                      }
                    );
                  }
                } catch (transferError) {
                  console.error(
                    "Error initiating automatic fund transfer:",
                    transferError
                  );
                  // Create transfer error notification
                  await createOrderPaymentNotification(
                    order,
                    payment,
                    "transfer_failed",
                    {
                      error:
                        transferError.message ||
                        "Unknown error during fund transfer",
                    }
                  );
                }
              }

              // Emit socket event for real-time update
              await emitPaymentUpdate(payment.orderId);
            }
          } else if (
            payment.status === "failed" ||
            payment.status === "cancelled"
          ) {
            // Create payment failure notification
            const order = await Order.findById(payment.orderId);
            if (order) {
              order.paymentStatus = payment.status;
              await order.save();

              // Create payment failure notification
              await createOrderPaymentNotification(order, payment, "failed");

              // Emit socket event for real-time update
              await emitPaymentUpdate(payment.orderId);
            }
          }

          results.push({
            paymentId: payment._id,
            oldStatus,
            newStatus: payment.status,
            updated: true,
          });
        } else {
          results.push({
            paymentId: payment._id,
            status: payment.status,
            updated: false,
          });
        }
      } catch (error) {
        console.error(`Error checking payment ${payment._id}:`, error);
        results.push({
          paymentId: payment._id,
          error: error.message,
          updated: false,
        });
      }
    }

    return results;
  } catch (error) {
    console.error("Error checking pending payments:", error);
    return [];
  }
};

export default {
  checkPendingPayments,
};
