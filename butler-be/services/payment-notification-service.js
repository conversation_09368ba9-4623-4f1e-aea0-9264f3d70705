import User from "../models/User.js";
import Order from "../models/Order.js";
import Payment from "../models/Payment.js";
import FoodChain from "../models/FoodChain.js";
import FundTransfer from "../models/FundTransfer.js";
import Notification from "../models/Notification.js";
import { createPaymentNotification } from "./notification-service.js";
import connectDB from "../config/database.js";

/**
 * Create order payment notification for admin
 * @param {Object} order - The order object
 * @param {Object} payment - The payment object
 * @param {String} status - Payment status
 * @param {Object} metadata - Additional metadata
 * @returns {Object} - Created notification
 */
export const createOrderPaymentNotification = async (order, payment, status, metadata = {}) => {
  try {
    await connectDB();
    
    // Find admin user for this food chain
    const admin = await User.findOne({
      foodChain: order.foodChainId,
      role: "admin",
    });

    if (!admin) {
      console.error(`Admin not found for food chain: ${order.foodChainId}`);
      return null;
    }

    // Determine notification details based on status
    let title, message, priority;
    
    switch (status) {
      case "paid":
        title = "Payment Received";
        message = `Payment for order #${order.orderNumber} has been successfully received.`;
        priority = "normal";
        break;
      case "failed":
        title = "Payment Failed";
        message = `Payment for order #${order.orderNumber} has failed. Please check the payment status.`;
        priority = "high";
        break;
      case "transfer_failed":
        title = "Fund Transfer Failed";
        message = `Automatic fund transfer for order #${order.orderNumber} has failed. Manual intervention required.`;
        priority = "urgent";
        break;
      case "transfer_completed":
        title = "Fund Transfer Completed";
        message = `Funds for order #${order.orderNumber} have been successfully transferred to your account.`;
        priority = "normal";
        break;
      default:
        title = "Payment Update";
        message = `There's an update to the payment for order #${order.orderNumber}.`;
        priority = "normal";
    }

    // Create notification for admin
    const notification = await createPaymentNotification({
      userId: admin._id,
      title,
      message,
      type: "payment",
      priority,
      resourceType: "payment",
      resourceId: payment._id,
      metadata: {
        ...metadata,
        orderId: order._id,
        orderNumber: order.orderNumber,
        paymentId: payment._id,
        amount: payment.amount,
        status,
      },
      actionLink: `/admin/orders`,
      foodChainId: order.foodChainId,
    });

    return notification;
  } catch (error) {
    console.error("Error creating order payment notification:", error);
    return null;
  }
};

/**
 * Create fund transfer notification
 * @param {Object} fundTransfer - The fund transfer object
 * @param {Object} order - The order object
 * @returns {Object} - Created notification
 */
export const createFundTransferNotification = async (fundTransfer, order) => {
  try {
    await connectDB();
    
    // Find admin user for this food chain
    const admin = await User.findOne({
      foodChain: fundTransfer.foodChainId,
      role: "admin",
    });

    if (!admin) {
      console.error(`Admin not found for food chain: ${fundTransfer.foodChainId}`);
      return null;
    }

    // Determine notification details based on status
    let title, message, priority;
    
    switch (fundTransfer.status) {
      case "completed":
        title = "Fund Transfer Completed";
        message = order 
          ? `Funds for order #${order.orderNumber} have been successfully transferred to your account.`
          : `A fund transfer of ${fundTransfer.amount} has been completed to your account.`;
        priority = "normal";
        break;
      case "failed":
        title = "Fund Transfer Failed";
        message = order
          ? `Fund transfer for order #${order.orderNumber} has failed. Reason: ${fundTransfer.failureReason || "Unknown error"}`
          : `A fund transfer of ${fundTransfer.amount} has failed. Reason: ${fundTransfer.failureReason || "Unknown error"}`;
        priority = "high";
        break;
      case "processing":
        title = "Fund Transfer Processing";
        message = order
          ? `Fund transfer for order #${order.orderNumber} is being processed.`
          : `A fund transfer of ${fundTransfer.amount} is being processed.`;
        priority = "normal";
        break;
      default:
        title = "Fund Transfer Update";
        message = order
          ? `There's an update to the fund transfer for order #${order.orderNumber}.`
          : `There's an update to a fund transfer of ${fundTransfer.amount}.`;
        priority = "normal";
    }

    // Create notification for admin
    const notification = await createPaymentNotification({
      userId: admin._id,
      title,
      message,
      type: "payment",
      priority,
      resourceType: "payment",
      resourceId: fundTransfer._id,
      metadata: {
        fundTransferId: fundTransfer._id,
        amount: fundTransfer.amount,
        status: fundTransfer.status,
        orderId: order?._id,
        orderNumber: order?.orderNumber,
      },
      actionLink: `/admin/payments`,
      foodChainId: fundTransfer.foodChainId,
    });

    return notification;
  } catch (error) {
    console.error("Error creating fund transfer notification:", error);
    return null;
  }
};

export default {
  createOrderPaymentNotification,
  createFundTransferNotification,
};
