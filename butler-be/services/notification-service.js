import Notification from "../models/Notification.js";
import User from "../models/User.js";
import FoodChain from "../models/FoodChain.js";

/**
 * Create a payment notification for a user
 * @param {Object} options - Notification options
 * @param {String} options.userId - User ID to notify
 * @param {String} options.title - Notification title
 * @param {String} options.message - Notification message
 * @param {String} options.type - Notification type (default: "payment")
 * @param {String} options.priority - Notification priority (default: "normal")
 * @param {String} options.resourceType - Resource type (default: "payment")
 * @param {String} options.resourceId - Resource ID
 * @param {Object} options.metadata - Additional metadata
 * @param {String} options.actionLink - Action link
 * @param {String} options.foodChainId - Food chain ID
 * @returns {Object} - Created notification
 */
export const createPaymentNotification = async (options) => {
  try {
    const {
      userId,
      title,
      message,
      type = "payment",
      priority = "normal",
      resourceType = "payment",
      resourceId,
      metadata,
      actionLink,
      foodChainId,
    } = options;

    // Create notification
    const notification = await Notification.createNotification({
      userId,
      title,
      message,
      type,
      priority,
      resourceType,
      resourceId,
      metadata,
      actionLink,
      foodChainId,
    });

    return notification;
  } catch (error) {
    console.error("Error creating payment notification:", error);
    // Don't throw error to prevent disrupting the main flow
    return null;
  }
};

/**
 * Create invoice payment notification for food chain admin
 * @param {Object} invoice - The invoice object
 * @param {String} status - Payment status
 * @param {Object} metadata - Additional metadata
 * @returns {Object} - Created notification
 */
export const createInvoicePaymentNotification = async (invoice, status, metadata = {}) => {
  try {
    // Find food chain admin
    const foodChain = await FoodChain.findById(invoice.foodChainId);
    if (!foodChain) {
      console.error(`Food chain not found: ${invoice.foodChainId}`);
      return null;
    }

    // Find admin user for this food chain
    const admin = await User.findOne({
      foodChain: invoice.foodChainId,
      role: "admin",
    });

    if (!admin) {
      console.error(`Admin not found for food chain: ${invoice.foodChainId}`);
      return null;
    }

    // Determine notification details based on status
    let title, message, priority;
    
    switch (status) {
      case "paid":
        title = "Payment Successful";
        message = `Your payment for invoice #${invoice.invoiceNumber} has been successfully processed.`;
        priority = "normal";
        break;
      case "failed":
        title = "Payment Failed";
        message = `Your payment for invoice #${invoice.invoiceNumber} has failed. Please try again.`;
        priority = "high";
        break;
      case "partially_paid":
        title = "Partial Payment Received";
        message = `We've received partial payment for invoice #${invoice.invoiceNumber}. Please complete the remaining payment.`;
        priority = "normal";
        break;
      case "expired":
        title = "Invoice Expired";
        message = `Your invoice #${invoice.invoiceNumber} has expired. Please contact support for assistance.`;
        priority = "normal";
        break;
      case "cancelled":
        title = "Invoice Cancelled";
        message = `Invoice #${invoice.invoiceNumber} has been cancelled.`;
        priority = "normal";
        break;
      case "refunded":
        title = "Payment Refunded";
        message = `Your payment for invoice #${invoice.invoiceNumber} has been refunded.`;
        priority = "normal";
        break;
      case "partially_refunded":
        title = "Partial Refund Processed";
        message = `A partial refund has been processed for invoice #${invoice.invoiceNumber}.`;
        priority = "normal";
        break;
      default:
        title = "Invoice Update";
        message = `There's an update to your invoice #${invoice.invoiceNumber}.`;
        priority = "normal";
    }

    // Create notification for admin
    const notification = await createPaymentNotification({
      userId: admin._id,
      title,
      message,
      type: "payment",
      priority,
      resourceType: "payment",
      resourceId: invoice._id,
      metadata: {
        ...metadata,
        invoiceId: invoice._id,
        invoiceNumber: invoice.invoiceNumber,
        amount: invoice.amount,
        status,
      },
      actionLink: `/admin/subscription`,
      foodChainId: invoice.foodChainId,
    });

    return notification;
  } catch (error) {
    console.error("Error creating invoice payment notification:", error);
    return null;
  }
};

/**
 * Create subscription status notification
 * @param {Object} subscription - The subscription object
 * @param {String} status - Subscription status
 * @param {Object} metadata - Additional metadata
 * @returns {Object} - Created notification
 */
export const createSubscriptionStatusNotification = async (subscription, status, metadata = {}) => {
  try {
    // Find food chain admin
    const admin = await User.findOne({
      foodChain: subscription.foodChainId,
      role: "admin",
    });

    if (!admin) {
      console.error(`Admin not found for food chain: ${subscription.foodChainId}`);
      return null;
    }

    // Determine notification details based on status
    let title, message, priority;
    
    switch (status) {
      case "active":
        title = "Subscription Activated";
        message = `Your subscription has been activated successfully.`;
        priority = "normal";
        break;
      case "pending":
        title = "Subscription Pending";
        message = `Your subscription is pending payment. Please complete the payment to activate your subscription.`;
        priority = "high";
        break;
      case "expired":
        title = "Subscription Expired";
        message = `Your subscription has expired. Please renew to continue using our services.`;
        priority = "high";
        break;
      case "cancelled":
        title = "Subscription Cancelled";
        message = `Your subscription has been cancelled.`;
        priority = "normal";
        break;
      default:
        title = "Subscription Update";
        message = `There's an update to your subscription.`;
        priority = "normal";
    }

    // Create notification for admin
    const notification = await createPaymentNotification({
      userId: admin._id,
      title,
      message,
      type: "payment",
      priority,
      resourceType: "subscription",
      resourceId: subscription._id,
      metadata: {
        ...metadata,
        subscriptionId: subscription._id,
        status,
      },
      actionLink: `/admin/subscription`,
      foodChainId: subscription.foodChainId,
    });

    return notification;
  } catch (error) {
    console.error("Error creating subscription status notification:", error);
    return null;
  }
};

/**
 * Create trial period ending notification
 * @param {Object} subscription - The subscription object
 * @param {Number} daysRemaining - Days remaining in trial
 * @returns {Object} - Created notification
 */
export const createTrialEndingNotification = async (subscription, daysRemaining) => {
  try {
    // Find food chain admin
    const admin = await User.findOne({
      foodChain: subscription.foodChainId,
      role: "admin",
    });

    if (!admin) {
      console.error(`Admin not found for food chain: ${subscription.foodChainId}`);
      return null;
    }

    // Create notification for admin
    const notification = await createPaymentNotification({
      userId: admin._id,
      title: "Trial Period Ending Soon",
      message: `Your trial period will end in ${daysRemaining} days. You will be billed for your subscription after the trial period.`,
      type: "payment",
      priority: daysRemaining <= 3 ? "high" : "normal",
      resourceType: "subscription",
      resourceId: subscription._id,
      metadata: {
        subscriptionId: subscription._id,
        trialEndDate: subscription.trialEndDate,
        daysRemaining,
      },
      actionLink: `/admin/subscription`,
      foodChainId: subscription.foodChainId,
    });

    return notification;
  } catch (error) {
    console.error("Error creating trial ending notification:", error);
    return null;
  }
};

/**
 * Create invoice due notification
 * @param {Object} invoice - The invoice object
 * @param {Number} daysRemaining - Days remaining until due date
 * @returns {Object} - Created notification
 */
export const createInvoiceDueNotification = async (invoice, daysRemaining) => {
  try {
    // Find food chain admin
    const admin = await User.findOne({
      foodChain: invoice.foodChainId,
      role: "admin",
    });

    if (!admin) {
      console.error(`Admin not found for food chain: ${invoice.foodChainId}`);
      return null;
    }

    // Create notification for admin
    const notification = await createPaymentNotification({
      userId: admin._id,
      title: "Invoice Due Soon",
      message: `Your invoice #${invoice.invoiceNumber} is due in ${daysRemaining} days. Please make the payment to avoid service interruption.`,
      type: "payment",
      priority: daysRemaining <= 2 ? "high" : "normal",
      resourceType: "payment",
      resourceId: invoice._id,
      metadata: {
        invoiceId: invoice._id,
        invoiceNumber: invoice.invoiceNumber,
        amount: invoice.amount,
        dueDate: invoice.dueDate,
        daysRemaining,
      },
      actionLink: `/admin/subscription`,
      foodChainId: invoice.foodChainId,
    });

    return notification;
  } catch (error) {
    console.error("Error creating invoice due notification:", error);
    return null;
  }
};

export default {
  createPaymentNotification,
  createInvoicePaymentNotification,
  createSubscriptionStatusNotification,
  createTrialEndingNotification,
  createInvoiceDueNotification,
};
