import NodeCache from 'node-cache';
import Dish from "../models/Dish.js";
import Outlet from "../models/Outlet.js";

/**
 * Performance optimization service for handling large datasets
 * Implements caching, pagination, and efficient algorithms for 200+ menu items
 */

// Initialize cache with TTL (Time To Live) settings
const dishCache = new NodeCache({ 
  stdTTL: 300, // 5 minutes default TTL
  checkperiod: 60, // Check for expired keys every 60 seconds
  useClones: false // Don't clone objects for better performance
});

const outletCache = new NodeCache({ 
  stdTTL: 600, // 10 minutes for outlets
  checkperiod: 120,
  useClones: false
});

const userPreferenceCache = new NodeCache({ 
  stdTTL: 180, // 3 minutes for user preferences
  checkperiod: 60,
  useClones: false
});

/**
 * Optimized dish fetching with caching and pagination
 * @param {Object} query - MongoDB query object
 * @param {Object} options - Pagination and optimization options
 * @returns {Promise<Object>} - Paginated and cached results
 */
export const getOptimizedDishes = async (query, options = {}) => {
  try {
    const {
      page = 1,
      limit = 50,
      sortBy = 'name',
      sortOrder = 1,
      useCache = true,
      populateFields = ['category'],
      selectFields = null,
      enablePagination = true
    } = options;

    // Create cache key based on query and options
    const cacheKey = createCacheKey('dishes', { query, page, limit, sortBy, sortOrder });
    
    // Try to get from cache first
    if (useCache) {
      const cachedResult = dishCache.get(cacheKey);
      if (cachedResult) {
        return {
          ...cachedResult,
          fromCache: true,
          cacheHit: true
        };
      }
    }

    // Build aggregation pipeline for better performance
    const pipeline = [];
    
    // Match stage
    pipeline.push({ $match: query });
    
    // Lookup stages for population (more efficient than populate)
    if (populateFields.includes('category')) {
      pipeline.push({
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'category'
        }
      });
      pipeline.push({
        $unwind: {
          path: '$category',
          preserveNullAndEmptyArrays: true
        }
      });
    }
    
    // Add sorting
    const sortStage = {};
    sortStage[sortBy] = sortOrder;
    pipeline.push({ $sort: sortStage });
    
    // Add pagination if enabled
    if (enablePagination) {
      const skip = (page - 1) * limit;
      pipeline.push({ $skip: skip });
      pipeline.push({ $limit: limit });
    }
    
    // Add field selection if specified
    if (selectFields) {
      const projectStage = {};
      selectFields.split(' ').forEach(field => {
        if (field.startsWith('-')) {
          projectStage[field.substring(1)] = 0;
        } else {
          projectStage[field] = 1;
        }
      });
      pipeline.push({ $project: projectStage });
    }

    // Execute aggregation
    const dishes = await Dish.aggregate(pipeline);
    
    // Get total count for pagination (only if needed)
    let totalCount = null;
    if (enablePagination) {
      const countPipeline = [
        { $match: query },
        { $count: "total" }
      ];
      const countResult = await Dish.aggregate(countPipeline);
      totalCount = countResult[0]?.total || 0;
    }

    const result = {
      dishes,
      pagination: enablePagination ? {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        totalItems: totalCount,
        itemsPerPage: limit,
        hasNextPage: page * limit < totalCount,
        hasPrevPage: page > 1
      } : null,
      fromCache: false,
      cacheHit: false,
      executionTime: Date.now()
    };

    // Cache the result
    if (useCache && dishes.length > 0) {
      dishCache.set(cacheKey, result, 300); // Cache for 5 minutes
    }

    return result;
  } catch (error) {
    console.error("Error in optimized dish fetching:", error);
    throw error;
  }
};

/**
 * Optimized outlet fetching with caching
 * @param {Object} query - MongoDB query object
 * @param {Object} options - Optimization options
 * @returns {Promise<Object>} - Cached and optimized results
 */
export const getOptimizedOutlets = async (query, options = {}) => {
  try {
    const {
      limit = 50,
      useCache = true,
      includeAnalytics = false,
      sortBy = 'name',
      sortOrder = 1
    } = options;

    const cacheKey = createCacheKey('outlets', { query, limit, includeAnalytics, sortBy });
    
    if (useCache) {
      const cachedResult = outletCache.get(cacheKey);
      if (cachedResult) {
        return {
          ...cachedResult,
          fromCache: true
        };
      }
    }

    // Use aggregation for better performance
    const pipeline = [
      { $match: query },
      {
        $lookup: {
          from: 'foodchains',
          localField: 'foodChain',
          foreignField: '_id',
          as: 'foodChain'
        }
      },
      {
        $unwind: {
          path: '$foodChain',
          preserveNullAndEmptyArrays: true
        }
      }
    ];

    // Add dish count if needed
    if (includeAnalytics) {
      pipeline.push({
        $lookup: {
          from: 'dishes',
          localField: '_id',
          foreignField: 'outlets',
          as: 'dishes'
        }
      });
      pipeline.push({
        $addFields: {
          dishCount: { $size: '$dishes' }
        }
      });
    }

    // Add sorting and limiting
    const sortStage = {};
    sortStage[sortBy] = sortOrder;
    pipeline.push({ $sort: sortStage });
    pipeline.push({ $limit: limit });

    const outlets = await Outlet.aggregate(pipeline);

    const result = {
      outlets,
      fromCache: false,
      executionTime: Date.now()
    };

    if (useCache && outlets.length > 0) {
      outletCache.set(cacheKey, result, 600); // Cache for 10 minutes
    }

    return result;
  } catch (error) {
    console.error("Error in optimized outlet fetching:", error);
    throw error;
  }
};

/**
 * Batch processing for large datasets
 * @param {Array} items - Items to process
 * @param {Function} processor - Processing function
 * @param {Object} options - Batch options
 * @returns {Promise<Array>} - Processed results
 */
export const batchProcess = async (items, processor, options = {}) => {
  try {
    const {
      batchSize = 50,
      concurrency = 3,
      delayBetweenBatches = 0
    } = options;

    const results = [];
    const batches = [];
    
    // Split items into batches
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    // Process batches with controlled concurrency
    for (let i = 0; i < batches.length; i += concurrency) {
      const currentBatches = batches.slice(i, i + concurrency);
      
      const batchPromises = currentBatches.map(async (batch, index) => {
        try {
          return await processor(batch, i + index);
        } catch (error) {
          console.error(`Error processing batch ${i + index}:`, error);
          return null;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.filter(result => result !== null));

      // Add delay between batch groups if specified
      if (delayBetweenBatches > 0 && i + concurrency < batches.length) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }

    return results.flat();
  } catch (error) {
    console.error("Error in batch processing:", error);
    throw error;
  }
};

/**
 * Efficient search with indexing and caching
 * @param {string} searchTerm - Search term
 * @param {Object} options - Search options
 * @returns {Promise<Array>} - Search results
 */
export const efficientSearch = async (searchTerm, options = {}) => {
  try {
    const {
      collection = 'dishes',
      fields = ['name', 'description'],
      limit = 20,
      useCache = true,
      fuzzySearch = true
    } = options;

    const cacheKey = createCacheKey('search', { searchTerm, collection, fields, limit });
    
    if (useCache) {
      const cachedResult = dishCache.get(cacheKey);
      if (cachedResult) {
        return {
          ...cachedResult,
          fromCache: true
        };
      }
    }

    let pipeline = [];
    
    if (fuzzySearch) {
      // Use text search if available
      pipeline.push({
        $match: {
          $text: { $search: searchTerm }
        }
      });
      pipeline.push({
        $addFields: {
          score: { $meta: "textScore" }
        }
      });
      pipeline.push({
        $sort: { score: { $meta: "textScore" } }
      });
    } else {
      // Use regex search as fallback
      const regexConditions = fields.map(field => ({
        [field]: { $regex: searchTerm, $options: 'i' }
      }));
      
      pipeline.push({
        $match: {
          $or: regexConditions
        }
      });
    }

    pipeline.push({ $limit: limit });

    const Model = collection === 'dishes' ? Dish : Outlet;
    const results = await Model.aggregate(pipeline);

    const searchResult = {
      results,
      searchTerm,
      resultCount: results.length,
      fromCache: false,
      executionTime: Date.now()
    };

    if (useCache && results.length > 0) {
      dishCache.set(cacheKey, searchResult, 180); // Cache for 3 minutes
    }

    return searchResult;
  } catch (error) {
    console.error("Error in efficient search:", error);
    throw error;
  }
};

/**
 * Memory-efficient data streaming for very large datasets
 * @param {Object} query - MongoDB query
 * @param {Function} processor - Function to process each chunk
 * @param {Object} options - Streaming options
 */
export const streamLargeDataset = async (query, processor, options = {}) => {
  try {
    const {
      chunkSize = 100,
      model = Dish
    } = options;

    const cursor = model.find(query).cursor();
    let chunk = [];
    let processedCount = 0;

    for (let doc = await cursor.next(); doc != null; doc = await cursor.next()) {
      chunk.push(doc);
      
      if (chunk.length >= chunkSize) {
        await processor(chunk, processedCount);
        processedCount += chunk.length;
        chunk = []; // Clear chunk to free memory
      }
    }

    // Process remaining items
    if (chunk.length > 0) {
      await processor(chunk, processedCount);
    }

    return processedCount;
  } catch (error) {
    console.error("Error in streaming large dataset:", error);
    throw error;
  }
};

/**
 * Cache management utilities
 */
export const cacheUtils = {
  // Clear specific cache
  clearCache: (cacheType) => {
    switch (cacheType) {
      case 'dishes':
        dishCache.flushAll();
        break;
      case 'outlets':
        outletCache.flushAll();
        break;
      case 'preferences':
        userPreferenceCache.flushAll();
        break;
      case 'all':
        dishCache.flushAll();
        outletCache.flushAll();
        userPreferenceCache.flushAll();
        break;
    }
  },

  // Get cache statistics
  getCacheStats: () => ({
    dishes: dishCache.getStats(),
    outlets: outletCache.getStats(),
    preferences: userPreferenceCache.getStats()
  }),

  // Warm up cache with frequently accessed data
  warmUpCache: async () => {
    try {
      // Pre-load popular dishes
      const popularDishes = await Dish.find({ isAvailable: true })
        .sort({ 'ratings.average': -1 })
        .limit(100)
        .populate('category', 'name');
      
      dishCache.set('popular_dishes', popularDishes, 600);

      // Pre-load active outlets
      const activeOutlets = await Outlet.find({ status: 'active' })
        .populate('foodChain', 'name')
        .limit(50);
      
      outletCache.set('active_outlets', activeOutlets, 600);

      console.log("Cache warmed up successfully");
    } catch (error) {
      console.error("Error warming up cache:", error);
    }
  }
};

/**
 * Helper function to create consistent cache keys
 * @param {string} prefix - Cache key prefix
 * @param {Object} params - Parameters to include in key
 * @returns {string} - Generated cache key
 */
const createCacheKey = (prefix, params) => {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {});
  
  return `${prefix}_${Buffer.from(JSON.stringify(sortedParams)).toString('base64')}`;
};

/**
 * Performance monitoring utilities
 */
export const performanceMonitor = {
  // Track query execution times
  trackQueryTime: (queryName, startTime) => {
    const executionTime = Date.now() - startTime;
    console.log(`Query ${queryName} executed in ${executionTime}ms`);
    
    // Log slow queries (> 1 second)
    if (executionTime > 1000) {
      console.warn(`Slow query detected: ${queryName} took ${executionTime}ms`);
    }
    
    return executionTime;
  },

  // Memory usage monitoring
  getMemoryUsage: () => {
    const usage = process.memoryUsage();
    return {
      rss: Math.round(usage.rss / 1024 / 1024 * 100) / 100,
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024 * 100) / 100,
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100,
      external: Math.round(usage.external / 1024 / 1024 * 100) / 100
    };
  }
};

// Initialize cache warming on service start
setTimeout(() => {
  cacheUtils.warmUpCache();
}, 5000); // Wait 5 seconds after service start
