import crypto from 'crypto';
import { recordRecommendationPerformance } from './performance-monitoring-service.js';

/**
 * A/B Testing Service for Recommendation Optimization
 * Enables gradual rollout with performance comparison between old and new systems
 */

// A/B Testing Configuration
let abTestConfig = {
  enabled: true,
  rolloutPercentage: 10, // Start with 10% of users
  testName: 'recommendation_optimization_v1',
  startDate: new Date(),
  endDate: null,
  
  // User segmentation criteria
  segmentation: {
    byUserId: true,
    byOutlet: false,
    byFoodChain: false,
    excludeAdmins: true
  },
  
  // Metrics to track for comparison
  trackingMetrics: [
    'responseTime',
    'tokenUsage',
    'userSatisfaction',
    'recommendationAccuracy',
    'cacheHitRate',
    'fallbackUsage'
  ],
  
  // Minimum sample size for statistical significance
  minSampleSize: 100,
  
  // Auto-rollout thresholds
  autoRollout: {
    enabled: false,
    successThreshold: 0.15, // 15% improvement required
    confidenceLevel: 0.95
  }
};

// A/B Test Results Storage
let abTestResults = {
  control: {
    totalRequests: 0,
    totalResponseTime: 0,
    totalTokensUsed: 0,
    successfulRecommendations: 0,
    userSatisfactionSum: 0,
    cacheHits: 0,
    cacheMisses: 0,
    fallbackUsage: 0,
    errors: 0
  },
  
  treatment: {
    totalRequests: 0,
    totalResponseTime: 0,
    totalTokensUsed: 0,
    successfulRecommendations: 0,
    userSatisfactionSum: 0,
    cacheHits: 0,
    cacheMisses: 0,
    fallbackUsage: 0,
    errors: 0
  },
  
  lastUpdated: new Date()
};

/**
 * Determine if a user should receive the optimized recommendation system
 * @param {string} userId - User ID
 * @param {Object} context - Additional context (outletId, foodChainId, etc.)
 * @returns {boolean} - True if user should get optimized system
 */
export const shouldUseOptimizedSystem = (userId, context = {}) => {
  try {
    // Check if A/B testing is enabled
    if (!abTestConfig.enabled) {
      return false; // Default to old system if A/B testing is disabled
    }
    
    // Check if test is within date range
    const now = new Date();
    if (now < abTestConfig.startDate) {
      return false;
    }
    if (abTestConfig.endDate && now > abTestConfig.endDate) {
      return false;
    }
    
    // Exclude admins if configured
    if (abTestConfig.segmentation.excludeAdmins && context.userRole) {
      const adminRoles = ['admin', 'super_admin', 'manager'];
      if (adminRoles.includes(context.userRole)) {
        return false;
      }
    }
    
    // Determine assignment based on user ID hash
    const hash = crypto.createHash('md5').update(userId + abTestConfig.testName).digest('hex');
    const hashValue = parseInt(hash.substring(0, 8), 16);
    const percentage = (hashValue % 100) + 1;
    
    return percentage <= abTestConfig.rolloutPercentage;
    
  } catch (error) {
    console.error('Error in A/B test assignment:', error);
    return false; // Default to old system on error
  }
};

/**
 * Record A/B test metrics for analysis
 * @param {string} variant - 'control' or 'treatment'
 * @param {Object} metrics - Performance metrics to record
 */
export const recordABTestMetrics = (variant, metrics) => {
  try {
    if (!['control', 'treatment'].includes(variant)) {
      console.error('Invalid A/B test variant:', variant);
      return;
    }
    
    const results = abTestResults[variant];
    
    // Update counters
    results.totalRequests++;
    
    // Update response time
    if (metrics.responseTime) {
      results.totalResponseTime += metrics.responseTime;
    }
    
    // Update token usage
    if (metrics.tokenUsage) {
      results.totalTokensUsed += metrics.tokenUsage;
    }
    
    // Update success metrics
    if (metrics.successful) {
      results.successfulRecommendations++;
    }
    
    // Update user satisfaction (if provided)
    if (metrics.userSatisfaction) {
      results.userSatisfactionSum += metrics.userSatisfaction;
    }
    
    // Update cache metrics
    if (metrics.cacheHits) {
      results.cacheHits += metrics.cacheHits;
    }
    if (metrics.cacheMisses) {
      results.cacheMisses += metrics.cacheMisses;
    }
    
    // Update fallback usage
    if (metrics.fallbackUsed) {
      results.fallbackUsage++;
    }
    
    // Update error count
    if (metrics.error) {
      results.errors++;
    }
    
    abTestResults.lastUpdated = new Date();
    
  } catch (error) {
    console.error('Error recording A/B test metrics:', error);
  }
};

/**
 * Calculate A/B test statistics and performance comparison
 * @returns {Object} - Comprehensive A/B test analysis
 */
export const getABTestAnalysis = () => {
  try {
    const control = abTestResults.control;
    const treatment = abTestResults.treatment;
    
    // Calculate averages for control group
    const controlMetrics = {
      totalRequests: control.totalRequests,
      averageResponseTime: control.totalRequests > 0 ? control.totalResponseTime / control.totalRequests : 0,
      averageTokenUsage: control.totalRequests > 0 ? control.totalTokensUsed / control.totalRequests : 0,
      successRate: control.totalRequests > 0 ? (control.successfulRecommendations / control.totalRequests) * 100 : 0,
      averageUserSatisfaction: control.totalRequests > 0 ? control.userSatisfactionSum / control.totalRequests : 0,
      cacheHitRate: (control.cacheHits + control.cacheMisses) > 0 ? (control.cacheHits / (control.cacheHits + control.cacheMisses)) * 100 : 0,
      fallbackRate: control.totalRequests > 0 ? (control.fallbackUsage / control.totalRequests) * 100 : 0,
      errorRate: control.totalRequests > 0 ? (control.errors / control.totalRequests) * 100 : 0
    };
    
    // Calculate averages for treatment group
    const treatmentMetrics = {
      totalRequests: treatment.totalRequests,
      averageResponseTime: treatment.totalRequests > 0 ? treatment.totalResponseTime / treatment.totalRequests : 0,
      averageTokenUsage: treatment.totalRequests > 0 ? treatment.totalTokensUsed / treatment.totalRequests : 0,
      successRate: treatment.totalRequests > 0 ? (treatment.successfulRecommendations / treatment.totalRequests) * 100 : 0,
      averageUserSatisfaction: treatment.totalRequests > 0 ? treatment.userSatisfactionSum / treatment.totalRequests : 0,
      cacheHitRate: (treatment.cacheHits + treatment.cacheMisses) > 0 ? (treatment.cacheHits / (treatment.cacheHits + treatment.cacheMisses)) * 100 : 0,
      fallbackRate: treatment.totalRequests > 0 ? (treatment.fallbackUsage / treatment.totalRequests) * 100 : 0,
      errorRate: treatment.totalRequests > 0 ? (treatment.errors / treatment.totalRequests) * 100 : 0
    };
    
    // Calculate improvements (treatment vs control)
    const improvements = {
      responseTime: controlMetrics.averageResponseTime > 0 ? 
        ((controlMetrics.averageResponseTime - treatmentMetrics.averageResponseTime) / controlMetrics.averageResponseTime) * 100 : 0,
      tokenUsage: controlMetrics.averageTokenUsage > 0 ? 
        ((controlMetrics.averageTokenUsage - treatmentMetrics.averageTokenUsage) / controlMetrics.averageTokenUsage) * 100 : 0,
      successRate: treatmentMetrics.successRate - controlMetrics.successRate,
      userSatisfaction: treatmentMetrics.averageUserSatisfaction - controlMetrics.averageUserSatisfaction,
      cacheHitRate: treatmentMetrics.cacheHitRate - controlMetrics.cacheHitRate,
      fallbackRate: controlMetrics.fallbackRate - treatmentMetrics.fallbackRate,
      errorRate: controlMetrics.errorRate - treatmentMetrics.errorRate
    };
    
    // Determine statistical significance (simplified)
    const totalSampleSize = controlMetrics.totalRequests + treatmentMetrics.totalRequests;
    const hasMinimumSample = totalSampleSize >= abTestConfig.minSampleSize;
    
    // Overall assessment
    const positiveImprovements = Object.values(improvements).filter(imp => imp > 0).length;
    const totalMetrics = Object.keys(improvements).length;
    const overallSuccess = positiveImprovements / totalMetrics;
    
    return {
      config: abTestConfig,
      control: controlMetrics,
      treatment: treatmentMetrics,
      improvements,
      analysis: {
        totalSampleSize,
        hasMinimumSample,
        overallSuccessRate: overallSuccess,
        significantImprovement: hasMinimumSample && overallSuccess >= 0.6,
        recommendRollout: hasMinimumSample && overallSuccess >= 0.7 && improvements.tokenUsage > 50,
        testDuration: Date.now() - abTestConfig.startDate.getTime(),
        lastUpdated: abTestResults.lastUpdated
      },
      recommendations: generateRecommendations(improvements, hasMinimumSample, overallSuccess)
    };
    
  } catch (error) {
    console.error('Error calculating A/B test analysis:', error);
    return {
      error: error.message,
      timestamp: new Date()
    };
  }
};

/**
 * Generate recommendations based on A/B test results
 */
const generateRecommendations = (improvements, hasMinimumSample, overallSuccess) => {
  const recommendations = [];
  
  if (!hasMinimumSample) {
    recommendations.push('Continue test to reach minimum sample size for statistical significance');
  }
  
  if (improvements.tokenUsage > 70) {
    recommendations.push('Excellent token savings achieved - consider increasing rollout percentage');
  }
  
  if (improvements.responseTime > 30) {
    recommendations.push('Significant response time improvement - good for user experience');
  }
  
  if (improvements.errorRate < -5) {
    recommendations.push('Error rate increased - investigate and fix issues before full rollout');
  }
  
  if (overallSuccess >= 0.8) {
    recommendations.push('Strong positive results - recommend full rollout');
  } else if (overallSuccess >= 0.6) {
    recommendations.push('Positive results - consider gradual increase in rollout percentage');
  } else {
    recommendations.push('Mixed results - analyze individual metrics and consider optimizations');
  }
  
  return recommendations;
};

/**
 * Update A/B test configuration
 * @param {Object} newConfig - New configuration settings
 */
export const updateABTestConfig = (newConfig) => {
  try {
    abTestConfig = { ...abTestConfig, ...newConfig };
    console.log('A/B test configuration updated:', abTestConfig);
    return { success: true, config: abTestConfig };
  } catch (error) {
    console.error('Error updating A/B test config:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Reset A/B test results (for new test cycles)
 */
export const resetABTestResults = () => {
  try {
    abTestResults = {
      control: {
        totalRequests: 0,
        totalResponseTime: 0,
        totalTokensUsed: 0,
        successfulRecommendations: 0,
        userSatisfactionSum: 0,
        cacheHits: 0,
        cacheMisses: 0,
        fallbackUsage: 0,
        errors: 0
      },
      treatment: {
        totalRequests: 0,
        totalResponseTime: 0,
        totalTokensUsed: 0,
        successfulRecommendations: 0,
        userSatisfactionSum: 0,
        cacheHits: 0,
        cacheMisses: 0,
        fallbackUsage: 0,
        errors: 0
      },
      lastUpdated: new Date()
    };
    
    console.log('A/B test results reset');
    return { success: true };
  } catch (error) {
    console.error('Error resetting A/B test results:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get current A/B test configuration
 */
export const getABTestConfig = () => {
  return { ...abTestConfig };
};

/**
 * Export A/B test data for external analysis
 */
export const exportABTestData = () => {
  return {
    config: abTestConfig,
    results: abTestResults,
    analysis: getABTestAnalysis(),
    exportedAt: new Date()
  };
};
