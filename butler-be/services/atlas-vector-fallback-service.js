import { 
  getAtlasVectorRecommendations,
  isAtlasVectorSearchAvailable,
  hybridDishSearch 
} from "./atlas-vector-search-service.js";
import { searchDishes } from "./menu-search-service.js";
import { getFallbackRecommendations } from "./fallback-recommendation-service.js";
import { 
  getFallbackConfig, 
  isAtlasVectorFeatureEnabled,
  getSearchParameters 
} from "../config/atlas-vector-config.js";

/**
 * Atlas Vector Search Fallback Service
 * Provides intelligent fallback mechanisms for vector search failures
 */

// Performance tracking
let fallbackMetrics = {
  totalRequests: 0,
  atlasSuccessCount: 0,
  fallbackUsageCount: 0,
  averageResponseTime: 0,
  lastReset: new Date()
};

/**
 * Smart recommendation service with Atlas Vector Search and fallbacks
 * @param {string} query - User query
 * @param {Array} availableDishes - Available dishes
 * @param {Object} context - Request context
 * @returns {Promise<Object>} - Recommendations with metadata
 */
export const getSmartRecommendations = async (query, availableDishes, context = {}) => {
  const startTime = Date.now();
  fallbackMetrics.totalRequests++;

  try {
    const fallbackConfig = getFallbackConfig();
    const searchParams = getSearchParameters();

    console.log(`🎯 Starting smart recommendations for query: "${query}"`);
    console.log(`📊 Available dishes: ${availableDishes.length}`);

    // Try Atlas Vector Search first if enabled and available
    if (isAtlasVectorFeatureEnabled('enableAtlasVectorSearch') && context.foodChainId) {
      console.log("🔍 Attempting Atlas Vector Search...");
      
      const atlasResult = await tryAtlasVectorSearch(query, availableDishes, context, searchParams);
      
      if (atlasResult.success) {
        fallbackMetrics.atlasSuccessCount++;
        const responseTime = Date.now() - startTime;
        updateMetrics(responseTime);
        
        return {
          ...atlasResult,
          metadata: {
            method: 'atlas_vector_search',
            responseTime,
            fallbackUsed: false,
            dishesProcessed: availableDishes.length,
            resultsFound: atlasResult.recommendations.length
          }
        };
      }
    }

    // Fallback chain
    console.log("🔄 Atlas Vector Search unavailable, trying fallback methods...");
    fallbackMetrics.fallbackUsageCount++;

    const fallbackResult = await executeFallbackChain(
      query, 
      availableDishes, 
      context, 
      fallbackConfig.fallbackMethods
    );

    const responseTime = Date.now() - startTime;
    updateMetrics(responseTime);

    return {
      ...fallbackResult,
      metadata: {
        ...fallbackResult.metadata,
        responseTime,
        fallbackUsed: true,
        dishesProcessed: availableDishes.length
      }
    };

  } catch (error) {
    console.error("❌ Error in smart recommendations:", error);
    
    // Final fallback - return basic filtered results
    const basicResults = availableDishes.slice(0, 5);
    const responseTime = Date.now() - startTime;
    updateMetrics(responseTime);

    return {
      recommendations: basicResults,
      aiResponse: "I found some dishes for you. Please let me know if you'd like more specific recommendations.",
      metadata: {
        method: 'basic_fallback',
        responseTime,
        fallbackUsed: true,
        error: error.message,
        dishesProcessed: availableDishes.length,
        resultsFound: basicResults.length
      }
    };
  }
};

/**
 * Try Atlas Vector Search with timeout and error handling
 * @param {string} query - Search query
 * @param {Array} availableDishes - Available dishes
 * @param {Object} context - Request context
 * @param {Object} searchParams - Search parameters
 * @returns {Promise<Object>} - Search result with success flag
 */
const tryAtlasVectorSearch = async (query, availableDishes, context, searchParams) => {
  try {
    // Check if Atlas Vector Search is available
    const isAvailable = await Promise.race([
      isAtlasVectorSearchAvailable(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Availability check timeout')), 2000)
      )
    ]);

    if (!isAvailable) {
      console.log("📭 Atlas Vector Search not available");
      return { success: false, reason: 'not_available' };
    }

    // Perform Atlas Vector Search with timeout
    const recommendations = await Promise.race([
      getAtlasVectorRecommendations(query, availableDishes, {
        ...context,
        limit: searchParams.defaultLimit
      }),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Atlas Vector Search timeout')), searchParams.timeoutMs || 5000)
      )
    ]);

    if (recommendations && recommendations.length >= (searchParams.minResults || 3)) {
      console.log(`✅ Atlas Vector Search successful: ${recommendations.length} results`);
      
      return {
        success: true,
        recommendations,
        aiResponse: generateVectorSearchResponse(query, recommendations, context.language),
        performance: {
          method: 'atlas_vector_search',
          dishesProcessed: availableDishes.length,
          resultsFound: recommendations.length
        }
      };
    } else {
      console.log("📭 Atlas Vector Search returned insufficient results");
      return { success: false, reason: 'insufficient_results' };
    }

  } catch (error) {
    console.warn("⚠️ Atlas Vector Search failed:", error.message);
    return { success: false, reason: 'error', error: error.message };
  }
};

/**
 * Execute fallback chain in order of preference
 * @param {string} query - Search query
 * @param {Array} availableDishes - Available dishes
 * @param {Object} context - Request context
 * @param {Array} fallbackMethods - Ordered list of fallback methods
 * @returns {Promise<Object>} - Fallback result
 */
const executeFallbackChain = async (query, availableDishes, context, fallbackMethods) => {
  for (const method of fallbackMethods) {
    try {
      console.log(`🔄 Trying fallback method: ${method}`);
      
      let result = null;
      
      switch (method) {
        case 'enhanced_semantic_search':
          result = await tryEnhancedSemanticSearch(query, availableDishes, context);
          break;
          
        case 'text_search':
          result = await tryTextSearch(query, availableDishes, context);
          break;
          
        case 'basic_filter':
          result = await tryBasicFilter(query, availableDishes, context);
          break;
          
        default:
          console.warn(`⚠️ Unknown fallback method: ${method}`);
          continue;
      }
      
      if (result && result.recommendations && result.recommendations.length > 0) {
        console.log(`✅ Fallback method ${method} successful: ${result.recommendations.length} results`);
        return {
          ...result,
          metadata: {
            method,
            fallbackUsed: true,
            dishesProcessed: availableDishes.length,
            resultsFound: result.recommendations.length
          }
        };
      }
      
    } catch (error) {
      console.warn(`⚠️ Fallback method ${method} failed:`, error.message);
      continue;
    }
  }
  
  // If all fallback methods fail, return the original fallback service
  console.log("🔄 All fallback methods failed, using original fallback service");
  return await getFallbackRecommendations(query, availableDishes, context.userId, context);
};

/**
 * Try enhanced semantic search
 */
const tryEnhancedSemanticSearch = async (query, availableDishes, context) => {
  const results = await searchDishes(query, availableDishes, {
    limit: 8,
    threshold: 0.05,
    userHistory: context.userPreferences?.dishFrequency || {},
    filters: { availableOnly: true }
  });

  return {
    recommendations: results,
    aiResponse: generateSemanticSearchResponse(query, results, context.language)
  };
};

/**
 * Try text search
 */
const tryTextSearch = async (query, availableDishes, context) => {
  const searchFilter = {
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } },
      { cuisine: { $regex: query, $options: 'i' } }
    ]
  };

  const results = availableDishes.filter(dish => {
    return searchFilter.$or.some(condition => {
      const field = Object.keys(condition)[0];
      const regex = condition[field].$regex || condition[field].$in?.[0];
      
      if (field === 'tags' && dish.tags) {
        return dish.tags.some(tag => regex.test(tag));
      }
      
      return dish[field] && regex.test(dish[field]);
    });
  }).slice(0, 8);

  return {
    recommendations: results,
    aiResponse: generateTextSearchResponse(query, results, context.language)
  };
};

/**
 * Try basic filter (last resort)
 */
const tryBasicFilter = async (query, availableDishes, context) => {
  // Simple keyword matching
  const keywords = query.toLowerCase().split(' ').filter(word => word.length > 2);
  
  const results = availableDishes.filter(dish => {
    const dishText = `${dish.name} ${dish.description || ''} ${dish.cuisine || ''}`.toLowerCase();
    return keywords.some(keyword => dishText.includes(keyword));
  }).slice(0, 5);

  return {
    recommendations: results.length > 0 ? results : availableDishes.slice(0, 5),
    aiResponse: generateBasicFilterResponse(query, results, context.language)
  };
};

/**
 * Generate response for vector search results
 */
const generateVectorSearchResponse = (query, recommendations, language = 'en') => {
  if (recommendations.length === 0) {
    return language === 'hi' 
      ? "मुझे आपके लिए कोई डिश नहीं मिली।"
      : "I couldn't find any dishes matching your request.";
  }

  const dishNames = recommendations.slice(0, 3).map(dish => dish.name).join(', ');
  
  return language === 'hi'
    ? `आपकी पसंद के अनुसार मैंने ये डिश ढूंढी हैं: ${dishNames}। क्या आप इनमें से कोई ऑर्डर करना चाहेंगे?`
    : `Based on your preferences, I found these dishes: ${dishNames}. Would you like to order any of these?`;
};

/**
 * Generate response for semantic search results
 */
const generateSemanticSearchResponse = (query, recommendations, language = 'en') => {
  if (recommendations.length === 0) {
    return language === 'hi' 
      ? "मुझे आपके लिए कोई डिश नहीं मिली।"
      : "I couldn't find any dishes matching your request.";
  }

  return language === 'hi'
    ? `मैंने आपके लिए ${recommendations.length} डिश ढूंढी हैं। क्या आप देखना चाहेंगे?`
    : `I found ${recommendations.length} dishes for you. Would you like to see them?`;
};

/**
 * Generate response for text search results
 */
const generateTextSearchResponse = (query, recommendations, language = 'en') => {
  return generateSemanticSearchResponse(query, recommendations, language);
};

/**
 * Generate response for basic filter results
 */
const generateBasicFilterResponse = (query, recommendations, language = 'en') => {
  return language === 'hi'
    ? "यहाँ कुछ डिश हैं जो आपको पसंद आ सकती हैं।"
    : "Here are some dishes you might like.";
};

/**
 * Update performance metrics
 */
const updateMetrics = (responseTime) => {
  fallbackMetrics.averageResponseTime = 
    (fallbackMetrics.averageResponseTime * (fallbackMetrics.totalRequests - 1) + responseTime) / 
    fallbackMetrics.totalRequests;
};

/**
 * Get fallback performance metrics
 */
export const getFallbackMetrics = () => {
  return {
    ...fallbackMetrics,
    atlasSuccessRate: fallbackMetrics.totalRequests > 0 
      ? (fallbackMetrics.atlasSuccessCount / fallbackMetrics.totalRequests) * 100 
      : 0,
    fallbackUsageRate: fallbackMetrics.totalRequests > 0
      ? (fallbackMetrics.fallbackUsageCount / fallbackMetrics.totalRequests) * 100
      : 0
  };
};

/**
 * Reset fallback metrics
 */
export const resetFallbackMetrics = () => {
  fallbackMetrics = {
    totalRequests: 0,
    atlasSuccessCount: 0,
    fallbackUsageCount: 0,
    averageResponseTime: 0,
    lastReset: new Date()
  };
};
