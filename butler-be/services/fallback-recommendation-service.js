import { searchDishes } from './menu-search-service.js';
import { getPersonalizedRecommendations } from './personalization-service.js';
import { getEnhancedUserPreferences } from './enhanced-recommendation-service.js';

/**
 * Fallback Recommendation Service
 * Provides robust recommendation algorithms when AI is unavailable
 * Uses local algorithms for popularity, user history, and collaborative filtering
 */

/**
 * Main fallback recommendation function
 * @param {string} userQuery - User's query
 * @param {Array} availableDishes - Available dishes
 * @param {string} userId - User ID
 * @param {Object} context - Additional context
 * @returns {Promise<Object>} - Fallback recommendations
 */
export const getFallbackRecommendations = async (
  userQuery,
  availableDishes,
  userId,
  context = {}
) => {
  try {
    console.log('🔄 Using fallback recommendation system');
    
    // Strategy 1: Try enhanced search-based recommendations
    const searchResults = await getSearchBasedRecommendations(
      userQuery,
      availableDishes,
      userId,
      context
    );
    
    if (searchResults.recommendations.length >= 3) {
      return {
        ...searchResults,
        fallbackMethod: 'search_based',
        fallbackUsed: true
      };
    }

    // Strategy 2: Try personalized recommendations
    const personalizedResults = await getPersonalizedFallback(
      userQuery,
      availableDishes,
      userId,
      context
    );
    
    if (personalizedResults.recommendations.length >= 3) {
      return {
        ...personalizedResults,
        fallbackMethod: 'personalized',
        fallbackUsed: true
      };
    }

    // Strategy 3: Try collaborative filtering
    const collaborativeResults = await getCollaborativeFilteringRecommendations(
      userQuery,
      availableDishes,
      userId,
      context
    );
    
    if (collaborativeResults.recommendations.length >= 3) {
      return {
        ...collaborativeResults,
        fallbackMethod: 'collaborative',
        fallbackUsed: true
      };
    }

    // Strategy 4: Popularity-based recommendations (final fallback)
    const popularityResults = await getPopularityBasedRecommendations(
      userQuery,
      availableDishes,
      context
    );
    
    return {
      ...popularityResults,
      fallbackMethod: 'popularity',
      fallbackUsed: true
    };

  } catch (error) {
    console.error('Error in fallback recommendations:', error);
    
    // Ultimate fallback - basic available dishes
    return getBasicFallback(availableDishes, context);
  }
};

/**
 * Strategy 1: Search-based recommendations using enhanced search
 */
const getSearchBasedRecommendations = async (userQuery, availableDishes, userId, context) => {
  try {
    // Get user preferences for enhanced scoring
    const userPreferences = await getEnhancedUserPreferences(userId, context.outletId);
    
    // Use enhanced search with user history
    const searchResults = await searchDishes(userQuery, availableDishes, {
      limit: 8,
      threshold: 0.05,
      userHistory: userPreferences.dishFrequency || {},
      filters: { availableOnly: true }
    });

    // Generate simple AI-like response
    const aiResponse = generateSimpleResponse(userQuery, searchResults, context.language);

    return {
      recommendations: searchResults.slice(0, 5),
      aiResponse,
      performance: {
        method: 'search_based',
        dishesProcessed: availableDishes.length,
        resultsFound: searchResults.length
      }
    };
  } catch (error) {
    console.error('Error in search-based fallback:', error);
    return { recommendations: [] };
  }
};

/**
 * Strategy 2: Personalized recommendations fallback
 */
const getPersonalizedFallback = async (userQuery, availableDishes, userId, context) => {
  try {
    const personalizedRecs = await getPersonalizedRecommendations(
      userId,
      availableDishes,
      userQuery,
      context.outletId,
      5
    );

    if (personalizedRecs && personalizedRecs.length > 0) {
      const aiResponse = generateSimpleResponse(userQuery, personalizedRecs, context.language);
      
      return {
        recommendations: personalizedRecs,
        aiResponse,
        performance: {
          method: 'personalized',
          dishesProcessed: availableDishes.length,
          resultsFound: personalizedRecs.length
        }
      };
    }

    return { recommendations: [] };
  } catch (error) {
    console.error('Error in personalized fallback:', error);
    return { recommendations: [] };
  }
};

/**
 * Strategy 3: Collaborative filtering recommendations
 */
const getCollaborativeFilteringRecommendations = async (userQuery, availableDishes, userId, context) => {
  try {
    // Simple collaborative filtering based on similar user preferences
    const userPreferences = await getEnhancedUserPreferences(userId, context.outletId);
    
    if (!userPreferences.dishFrequency || Object.keys(userPreferences.dishFrequency).length === 0) {
      return { recommendations: [] };
    }

    // Find dishes similar to user's frequently ordered dishes
    const frequentDishes = Object.entries(userPreferences.dishFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([dishId]) => dishId);

    // Find dishes in similar categories or with similar tags
    const recommendations = availableDishes
      .filter(dish => dish.isAvailable && !frequentDishes.includes(dish._id.toString()))
      .map(dish => ({
        ...dish,
        collaborativeScore: calculateCollaborativeScore(dish, frequentDishes, availableDishes)
      }))
      .sort((a, b) => b.collaborativeScore - a.collaborativeScore)
      .slice(0, 5);

    const aiResponse = generateSimpleResponse(userQuery, recommendations, context.language);

    return {
      recommendations,
      aiResponse,
      performance: {
        method: 'collaborative',
        dishesProcessed: availableDishes.length,
        resultsFound: recommendations.length
      }
    };
  } catch (error) {
    console.error('Error in collaborative filtering fallback:', error);
    return { recommendations: [] };
  }
};

/**
 * Strategy 4: Popularity-based recommendations
 */
const getPopularityBasedRecommendations = async (userQuery, availableDishes, context) => {
  try {
    // Apply basic filtering based on query keywords
    const queryKeywords = extractBasicKeywords(userQuery);
    let filteredDishes = availableDishes.filter(dish => dish.isAvailable);

    // Apply dietary filters if detected
    if (queryKeywords.includes('veg') || queryKeywords.includes('vegetarian')) {
      filteredDishes = filteredDishes.filter(dish => dish.isVeg);
    } else if (queryKeywords.includes('non-veg') || queryKeywords.includes('meat')) {
      filteredDishes = filteredDishes.filter(dish => !dish.isVeg);
    }

    // Sort by popularity (ratings and count)
    const recommendations = filteredDishes
      .map(dish => ({
        ...dish,
        popularityScore: calculatePopularityScore(dish)
      }))
      .sort((a, b) => b.popularityScore - a.popularityScore)
      .slice(0, 5);

    const aiResponse = generateSimpleResponse(userQuery, recommendations, context.language);

    return {
      recommendations,
      aiResponse,
      performance: {
        method: 'popularity',
        dishesProcessed: availableDishes.length,
        resultsFound: recommendations.length
      }
    };
  } catch (error) {
    console.error('Error in popularity-based fallback:', error);
    return getBasicFallback(availableDishes, context);
  }
};

/**
 * Ultimate fallback - basic available dishes
 */
const getBasicFallback = (availableDishes, context) => {
  const basicDishes = availableDishes
    .filter(dish => dish.isAvailable)
    .slice(0, 5);

  return {
    recommendations: basicDishes,
    aiResponse: {
      keywords: [],
      aiMessage: context.language === 'hi' ? 
        "मैं आपकी मदद कर सकता हूं। यहां हमारे कुछ व्यंजन हैं।" :
        "I can help you explore our menu. Here are some dishes for you.",
      recommendedDishIds: basicDishes.map(dish => dish._id),
      faqSuggestions: context.language === 'hi' ? 
        ["आज क्या स्पेशल है?", "वेज ऑप्शन दिखाएं"] :
        ["What's special today?", "Show me vegetarian options"],
      detectedLanguage: context.language || 'en'
    },
    performance: {
      method: 'basic',
      dishesProcessed: availableDishes.length,
      resultsFound: basicDishes.length
    },
    fallbackMethod: 'basic',
    fallbackUsed: true
  };
};

/**
 * Generate simple AI-like response without actual AI
 */
const generateSimpleResponse = (userQuery, recommendations, language = 'en') => {
  const dishNames = recommendations.slice(0, 3).map(dish => dish.name);
  
  let aiMessage;
  if (language === 'hi' || language === 'hi-en') {
    aiMessage = `आपके लिए यहां कुछ बेहतरीन विकल्प हैं: ${dishNames.join(', ')}। क्या आप इनमें से कुछ try करना चाहेंगे?`;
  } else {
    aiMessage = `Here are some great options for you: ${dishNames.join(', ')}. Would you like to try any of these?`;
  }

  const faqSuggestions = language === 'hi' || language === 'hi-en' ? 
    ["और भी options दिखाएं", "Price range क्या है?", "आज का special क्या है?"] :
    ["Show me more options", "What's the price range?", "What's today's special?"];

  return {
    keywords: extractBasicKeywords(userQuery),
    aiMessage,
    recommendedDishIds: recommendations.map(dish => dish._id),
    faqSuggestions,
    detectedLanguage: language
  };
};

/**
 * Calculate collaborative filtering score
 */
const calculateCollaborativeScore = (dish, frequentDishIds, allDishes) => {
  let score = 0;
  
  // Find frequent dishes to compare with
  const frequentDishes = allDishes.filter(d => 
    frequentDishIds.includes(d._id.toString())
  );

  frequentDishes.forEach(freqDish => {
    // Same category bonus
    if (dish.category && freqDish.category && 
        dish.category.name === freqDish.category.name) {
      score += 0.3;
    }

    // Similar cuisine bonus
    if (dish.cuisine && freqDish.cuisine && 
        dish.cuisine === freqDish.cuisine) {
      score += 0.2;
    }

    // Similar tags bonus
    if (dish.tags && freqDish.tags) {
      const commonTags = dish.tags.filter(tag => freqDish.tags.includes(tag));
      score += commonTags.length * 0.1;
    }

    // Similar price range bonus
    if (dish.price && freqDish.price) {
      const priceDiff = Math.abs(dish.price - freqDish.price);
      if (priceDiff <= freqDish.price * 0.3) { // Within 30% price range
        score += 0.1;
      }
    }
  });

  // Add popularity component
  score += calculatePopularityScore(dish) * 0.3;

  return score;
};

/**
 * Calculate popularity score
 */
const calculatePopularityScore = (dish) => {
  const rating = dish.ratings?.average || 0;
  const count = dish.ratings?.count || 0;
  
  // Weighted score: rating importance increases with count
  const weightedRating = rating * Math.min(count / 10, 1);
  const countBonus = Math.min(count / 100, 0.5);
  
  return weightedRating + countBonus;
};

/**
 * Extract basic keywords without AI
 */
const extractBasicKeywords = (query) => {
  const foodKeywords = [
    'spicy', 'sweet', 'mild', 'hot', 'cold', 'veg', 'vegetarian', 'non-veg', 'meat',
    'chicken', 'fish', 'rice', 'bread', 'curry', 'dal', 'soup', 'salad', 'dessert',
    'breakfast', 'lunch', 'dinner', 'snack', 'appetizer', 'main', 'beverage'
  ];
  
  const queryLower = query.toLowerCase();
  return foodKeywords.filter(keyword => queryLower.includes(keyword));
};

/**
 * Health check for fallback system
 */
export const fallbackHealthCheck = () => {
  return {
    status: 'healthy',
    availableStrategies: [
      'search_based',
      'personalized', 
      'collaborative',
      'popularity',
      'basic'
    ],
    lastCheck: new Date()
  };
};
