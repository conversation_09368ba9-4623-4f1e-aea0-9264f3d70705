import { getEnhancedUserPreferences, getCurrentContext } from "./enhanced-recommendation-service.js";
import { getUserPreferences } from "./personalization-service.js";

/**
 * Enhanced menu personalization service with performance optimization for large datasets
 * Handles 200+ menu items efficiently with caching and batch processing
 */

// Cache for user preferences to avoid repeated database calls
const userPreferencesCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Enhanced menu personalization with performance optimization
 * @param {Array} dishes - Array of dishes (can be 200+ items)
 * @param {string} userId - User ID
 * @param {string} outletId - Outlet ID (optional)
 * @param {Object} options - Personalization options
 * @returns {Promise<Array>} - Personalized and optimized dish list
 */
export const enhancedPersonalizeMenuOrder = async (
  dishes,
  userId,
  outletId = null,
  options = {}
) => {
  try {
    const {
      enableCaching = true,
      batchSize = 50,
      includeTimeBasedScoring = true,
      includeCollaborativeFiltering = true,
      maxResults = null
    } = options;

    // Early return for small datasets or no user
    if (!userId || dishes.length <= 10) {
      return dishes.map(dish => ({
        ...dish,
        personalizationScore: 0,
        personalizedReason: "Standard ordering"
      }));
    }

    // Get cached or fresh user preferences
    const userPreferences = await getCachedUserPreferences(
      userId,
      outletId,
      enableCaching
    );

    // Get current context for time-based scoring
    const currentContext = getCurrentContext();

    // Process dishes in batches for better performance
    const personalizedDishes = [];
    const totalBatches = Math.ceil(dishes.length / batchSize);

    for (let i = 0; i < totalBatches; i++) {
      const startIndex = i * batchSize;
      const endIndex = Math.min(startIndex + batchSize, dishes.length);
      const batch = dishes.slice(startIndex, endIndex);

      const personalizedBatch = await processDishBatch(
        batch,
        userPreferences,
        currentContext,
        {
          includeTimeBasedScoring,
          includeCollaborativeFiltering,
          outletId
        }
      );

      personalizedDishes.push(...personalizedBatch);
    }

    // Sort by personalization score
    personalizedDishes.sort((a, b) => {
      // Primary sort: personalization score
      if (b.personalizationScore !== a.personalizationScore) {
        return b.personalizationScore - a.personalizationScore;
      }
      
      // Secondary sort: availability
      if (a.isAvailable !== b.isAvailable) {
        return b.isAvailable - a.isAvailable;
      }
      
      // Tertiary sort: ratings
      const aRating = a.ratings?.average || 0;
      const bRating = b.ratings?.average || 0;
      if (bRating !== aRating) {
        return bRating - aRating;
      }
      
      // Final sort: maintain original order for equal items
      return 0;
    });

    // Apply result limit if specified
    const finalResults = maxResults 
      ? personalizedDishes.slice(0, maxResults)
      : personalizedDishes;

    return finalResults;
  } catch (error) {
    console.error("Error in enhanced menu personalization:", error);
    // Fallback to basic ordering
    return dishes.map(dish => ({
      ...dish,
      personalizationScore: 0,
      personalizedReason: "Standard ordering (personalization failed)"
    }));
  }
};

/**
 * Process a batch of dishes for personalization
 * @param {Array} dishes - Batch of dishes
 * @param {Object} userPreferences - User preferences
 * @param {Object} currentContext - Current context
 * @param {Object} options - Processing options
 * @returns {Promise<Array>} - Processed dishes with scores
 */
const processDishBatch = async (dishes, userPreferences, currentContext, options) => {
  const {
    includeTimeBasedScoring,
    includeCollaborativeFiltering,
    outletId
  } = options;

  return dishes.map(dish => {
    let score = 0;
    let reasons = [];

    // Base personalization score (40% weight)
    const baseScore = calculateBasePersonalizationScore(dish, userPreferences);
    score += baseScore.score * 0.4;
    if (baseScore.reasons.length > 0) {
      reasons.push(...baseScore.reasons);
    }

    // Time-based scoring (25% weight)
    if (includeTimeBasedScoring && userPreferences.timeBasedPreferences) {
      const timeScore = calculateTimeBasedScore(
        dish,
        userPreferences.timeBasedPreferences,
        currentContext
      );
      score += timeScore.score * 0.25;
      if (timeScore.reasons.length > 0) {
        reasons.push(...timeScore.reasons);
      }
    }

    // Seasonal preferences (15% weight)
    if (userPreferences.seasonalPreferences) {
      const seasonalScore = calculateSeasonalScore(
        dish,
        userPreferences.seasonalPreferences,
        currentContext
      );
      score += seasonalScore.score * 0.15;
      if (seasonalScore.reasons.length > 0) {
        reasons.push(...seasonalScore.reasons);
      }
    }

    // Popularity and quality boost (10% weight)
    const popularityScore = calculatePopularityScore(dish);
    score += popularityScore * 0.1;

    // Availability boost (10% weight)
    if (dish.isAvailable) {
      score += 10;
    } else {
      score *= 0.5; // Heavily penalize unavailable items
      reasons.push("Currently unavailable");
    }

    // Special dish boost
    if (dish.isSpecial) {
      score += 5;
      reasons.push("Chef's special");
    }

    // Seasonal dish boost
    if (dish.isSeasonal) {
      score += 3;
      reasons.push("Seasonal favorite");
    }

    return {
      ...dish,
      personalizationScore: Math.round(score * 100) / 100,
      personalizedReason: reasons.length > 0 ? reasons[0] : "Recommended for you"
    };
  });
};

/**
 * Calculate base personalization score
 * @param {Object} dish - Dish object
 * @param {Object} userPreferences - User preferences
 * @returns {Object} - Score and reasons
 */
const calculateBasePersonalizationScore = (dish, userPreferences) => {
  let score = 0;
  const reasons = [];
  const { dishFrequency, preferences } = userPreferences;

  // Previously ordered dish (highest weight)
  const dishId = dish._id.toString();
  if (dishFrequency && dishFrequency[dishId]) {
    const frequency = dishFrequency[dishId];
    score += Math.min(30, frequency * 5); // Cap at 30 points
    if (frequency > 3) {
      reasons.push("One of your favorites");
    } else {
      reasons.push("You've enjoyed this before");
    }
  }

  // Category preference
  const dishCategory = dish.category?.name || dish.category;
  if (dishCategory && preferences.favoriteCategories?.length > 0) {
    const categoryMatch = preferences.favoriteCategories.find(
      cat => cat.category === dishCategory
    );
    if (categoryMatch) {
      score += (categoryMatch.percentage / 100) * 20;
      if (categoryMatch.percentage > 30) {
        reasons.push(`You love ${dishCategory.toLowerCase()}`);
      }
    }
  }

  // Cuisine preference
  if (dish.cuisine && preferences.favoriteCuisines?.length > 0) {
    const cuisineMatch = preferences.favoriteCuisines.find(
      cui => cui.cuisine === dish.cuisine
    );
    if (cuisineMatch) {
      score += (cuisineMatch.percentage / 100) * 15;
      if (cuisineMatch.percentage > 25) {
        reasons.push(`Great ${dish.cuisine} choice`);
      }
    }
  }

  // Price preference
  if (preferences.pricePreference) {
    const price = dish.price;
    let priceCategory = 'medium';
    if (price < 200) priceCategory = 'low';
    else if (price >= 500) priceCategory = 'high';
    
    const pricePreference = preferences.pricePreference[priceCategory] || 0;
    score += (pricePreference / 100) * 10;
  }

  // Veg preference
  if (preferences.vegPreference) {
    const vegPreference = dish.isVeg 
      ? preferences.vegPreference.vegetarian 
      : preferences.vegPreference.nonVegetarian;
    score += (vegPreference / 100) * 5;
  }

  return { score: Math.min(score, 50), reasons }; // Cap at 50 points
};

/**
 * Calculate time-based score
 * @param {Object} dish - Dish object
 * @param {Object} timeBasedPreferences - Time-based preferences
 * @param {Object} currentContext - Current context
 * @returns {Object} - Score and reasons
 */
const calculateTimeBasedScore = (dish, timeBasedPreferences, currentContext) => {
  let score = 0;
  const reasons = [];
  const { timeSlots, dayOfWeek } = timeBasedPreferences;
  const currentTimeSlot = currentContext.timeSlot;
  const currentDayType = currentContext.dayOfWeek;

  // Time slot preferences
  if (timeSlots && timeSlots[currentTimeSlot] && timeSlots[currentTimeSlot].count > 0) {
    const dishCategory = dish.category?.name || 'Other';
    const cuisine = dish.cuisine || 'Other';
    
    const categoryCount = timeSlots[currentTimeSlot].categories[dishCategory] || 0;
    const cuisineCount = timeSlots[currentTimeSlot].cuisines[cuisine] || 0;
    const totalTimeSlotItems = Object.values(timeSlots[currentTimeSlot].categories)
      .reduce((sum, count) => sum + count, 0);
    
    if (totalTimeSlotItems > 0) {
      const categoryScore = (categoryCount / totalTimeSlotItems) * 15;
      const cuisineScore = (cuisineCount / totalTimeSlotItems) * 10;
      score += categoryScore + cuisineScore;
      
      if (categoryScore > 3) {
        reasons.push(`Perfect for ${currentTimeSlot}`);
      }
    }
  }

  // Day of week preferences
  if (dayOfWeek && dayOfWeek[currentDayType] && dayOfWeek[currentDayType].count > 0) {
    const dishCategory = dish.category?.name || 'Other';
    const categoryCount = dayOfWeek[currentDayType].categories[dishCategory] || 0;
    const totalDayTypeItems = Object.values(dayOfWeek[currentDayType].categories)
      .reduce((sum, count) => sum + count, 0);
    
    if (totalDayTypeItems > 0) {
      const dayScore = (categoryCount / totalDayTypeItems) * 8;
      score += dayScore;
      
      if (dayScore > 2) {
        reasons.push(`Great for ${currentDayType}s`);
      }
    }
  }

  return { score: Math.min(score, 25), reasons }; // Cap at 25 points
};

/**
 * Calculate seasonal score
 * @param {Object} dish - Dish object
 * @param {Object} seasonalPreferences - Seasonal preferences
 * @param {Object} currentContext - Current context
 * @returns {Object} - Score and reasons
 */
const calculateSeasonalScore = (dish, seasonalPreferences, currentContext) => {
  let score = 0;
  const reasons = [];
  const currentSeason = currentContext.season;
  
  if (seasonalPreferences[currentSeason] && seasonalPreferences[currentSeason].count > 0) {
    const dishCategory = dish.category?.name || 'Other';
    const cuisine = dish.cuisine || 'Other';
    
    const categoryCount = seasonalPreferences[currentSeason].categories[dishCategory] || 0;
    const cuisineCount = seasonalPreferences[currentSeason].cuisines[cuisine] || 0;
    const totalSeasonItems = Object.values(seasonalPreferences[currentSeason].categories)
      .reduce((sum, count) => sum + count, 0);
    
    if (totalSeasonItems > 0) {
      score += (categoryCount / totalSeasonItems) * 12;
      score += (cuisineCount / totalSeasonItems) * 8;
      
      if (score > 3) {
        reasons.push(`Perfect for ${currentSeason}`);
      }
    }
  }

  // Boost for seasonal dishes during their season
  if (dish.isSeasonal) {
    score += 5;
    reasons.push("Seasonal special");
  }

  return { score: Math.min(score, 15), reasons }; // Cap at 15 points
};

/**
 * Calculate popularity score
 * @param {Object} dish - Dish object
 * @returns {number} - Popularity score
 */
const calculatePopularityScore = (dish) => {
  let score = 0;

  // Rating-based score
  if (dish.ratings && dish.ratings.average > 0) {
    score += (dish.ratings.average / 5) * 8; // Up to 8 points for 5-star rating
    
    // Bonus for highly rated dishes with many reviews
    if (dish.ratings.count > 10 && dish.ratings.average >= 4.5) {
      score += 2;
    }
  }

  // Special dish bonus
  if (dish.isSpecial) {
    score += 3;
  }

  return Math.min(score, 10); // Cap at 10 points
};

/**
 * Get cached user preferences or fetch fresh ones
 * @param {string} userId - User ID
 * @param {string} outletId - Outlet ID
 * @param {boolean} enableCaching - Whether to use caching
 * @returns {Promise<Object>} - User preferences
 */
const getCachedUserPreferences = async (userId, outletId, enableCaching) => {
  const cacheKey = `${userId}_${outletId || 'all'}`;
  
  if (enableCaching && userPreferencesCache.has(cacheKey)) {
    const cached = userPreferencesCache.get(cacheKey);
    if (Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }
  }

  // Fetch fresh preferences
  try {
    const preferences = await getEnhancedUserPreferences(userId, outletId, 30);
    
    if (enableCaching) {
      userPreferencesCache.set(cacheKey, {
        data: preferences,
        timestamp: Date.now()
      });
    }
    
    return preferences;
  } catch (error) {
    console.error("Error fetching enhanced preferences, falling back to basic:", error);
    return await getUserPreferences(userId, outletId, 20);
  }
};

/**
 * Clear user preferences cache (useful for testing or when user preferences change)
 * @param {string} userId - User ID (optional, clears all if not provided)
 */
export const clearUserPreferencesCache = (userId = null) => {
  if (userId) {
    // Clear specific user's cache entries
    for (const key of userPreferencesCache.keys()) {
      if (key.startsWith(userId)) {
        userPreferencesCache.delete(key);
      }
    }
  } else {
    // Clear entire cache
    userPreferencesCache.clear();
  }
};
