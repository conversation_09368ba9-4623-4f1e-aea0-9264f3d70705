import { generateText, processTextSemantics } from "./groq-service.js";

/**
 * Enhanced menu search service using Groq for semantic understanding
 * Replaces the vector embedding system with keyword-based and semantic search
 */

/**
 * Extract keywords from user query using Groq's enhanced semantic processing
 * @param {string} userQuery - User's search query
 * @returns {Promise<Array<string>>} - Extracted keywords
 */
export const extractKeywords = async (userQuery) => {
  try {
    // First try using Groq's semantic processing
    const semantics = await processTextSemantics(userQuery, {
      extractKeywords: true,
      extractEntities: true,
      analyzeSentiment: false,
    });

    if (semantics.keywords && semantics.keywords.length > 0) {
      // Combine keywords and entities for better search
      const allKeywords = [
        ...semantics.keywords,
        ...(semantics.entities || []),
      ];
      return [...new Set(allKeywords)]; // Remove duplicates
    }

    // Fallback to direct Groq prompt
    const prompt = `Extract relevant food-related keywords from this user query. Focus on:
    - Food items (dishes, ingredients, cuisines)
    - Cooking methods (fried, grilled, baked, etc.)
    - Taste preferences (spicy, sweet, mild, etc.)
    - Dietary preferences (vegetarian, vegan, etc.)
    - Meal types (breakfast, lunch, dinner, snacks)

    User query: "${userQuery}"

    Return only a JSON array of keywords, no other text:
    ["keyword1", "keyword2", "keyword3"]`;

    const response = await generateText(prompt, {
      model: "llama3-70b-8192",
      max_tokens: 100,
      temperature: 0.3,
    });

    const keywords = JSON.parse(response);
    return Array.isArray(keywords) ? keywords : [];
  } catch (error) {
    console.error("Error extracting keywords:", error);
    // Fallback to simple keyword extraction
    return extractSimpleKeywords(userQuery);
  }
};

/**
 * Simple keyword extraction fallback
 * @param {string} userQuery - User's search query
 * @returns {Array<string>} - Simple extracted keywords
 */
const extractSimpleKeywords = (userQuery) => {
  // Remove common words and extract meaningful terms
  const commonWords = [
    "i",
    "want",
    "need",
    "like",
    "get",
    "have",
    "can",
    "will",
    "the",
    "a",
    "an",
    "and",
    "or",
    "but",
    "in",
    "on",
    "at",
    "to",
    "for",
    "of",
    "with",
    "by",
  ];
  const words = userQuery
    .toLowerCase()
    .replace(/[^\w\s]/g, " ")
    .split(/\s+/)
    .filter((word) => word.length > 2 && !commonWords.includes(word));

  return [...new Set(words)]; // Remove duplicates
};

/**
 * Calculate relevance score for a dish based on keywords
 * @param {Object} dish - Dish object
 * @param {Array<string>} keywords - Search keywords
 * @returns {number} - Relevance score (0-1)
 */
/**
 * Enhanced relevance score calculation with multiple factors
 * @param {Object} dish - Dish object
 * @param {Array<string>} keywords - Search keywords
 * @param {Object} options - Additional scoring options
 * @returns {number} - Enhanced relevance score (0-1)
 */
const calculateRelevanceScore = (dish, keywords, options = {}) => {
  let score = 0;
  const dishText = `${dish.name} ${dish.description || ""} ${
    dish.cuisine || ""
  } ${(dish.tags || []).join(" ")}`.toLowerCase();

  // Base keyword matching
  keywords.forEach((keyword) => {
    const keywordLower = keyword.toLowerCase();
    const dishNameLower = dish.name.toLowerCase();

    // Exact match in name (highest weight)
    if (dishNameLower.includes(keywordLower)) {
      score += 0.4;
    }

    // Bonus for very close matches (handling special characters)
    const normalizedDishName = dishNameLower
      .replace(/[^\w\s]/g, " ")
      .replace(/\s+/g, " ")
      .trim();
    const normalizedKeyword = keywordLower
      .replace(/[^\w\s]/g, " ")
      .replace(/\s+/g, " ")
      .trim();

    if (normalizedDishName.includes(normalizedKeyword)) {
      score += 0.3;
    }

    // Extra bonus for word-by-word matching
    const dishWords = normalizedDishName.split(" ");
    const keywordWords = normalizedKeyword.split(" ");
    const matchingWords = keywordWords.filter((word) =>
      dishWords.some(
        (dishWord) => dishWord.includes(word) || word.includes(dishWord)
      )
    );

    if (matchingWords.length > 0) {
      score += (matchingWords.length / keywordWords.length) * 0.2;
    }

    // Match in description
    if (
      dish.description &&
      dish.description.toLowerCase().includes(keywordLower)
    ) {
      score += 0.25;
    }

    // Match in cuisine
    if (dish.cuisine && dish.cuisine.toLowerCase().includes(keywordLower)) {
      score += 0.2;
    }

    // Match in tags
    if (
      dish.tags &&
      dish.tags.some((tag) => tag.toLowerCase().includes(keywordLower))
    ) {
      score += 0.15;
    }

    // Match in category
    if (
      dish.category &&
      dish.category.name &&
      dish.category.name.toLowerCase().includes(keywordLower)
    ) {
      score += 0.1;
    }

    // Fuzzy match (partial word matching)
    if (dishText.includes(keywordLower)) {
      score += 0.05;
    }
  });

  // Enhanced scoring factors

  // Popularity boost (ratings and count)
  if (dish.ratings && dish.ratings.average > 0) {
    const popularityBoost = (dish.ratings.average / 5) * 0.1;
    const countBoost = Math.min((dish.ratings.count || 0) / 100, 0.05);
    score += popularityBoost + countBoost;
  }

  // Availability boost
  if (dish.isAvailable) {
    score += 0.05;
  }

  // Special/Featured dish boost
  if (dish.isSpecial || dish.isFeatured) {
    score += 0.03;
  }

  // Price competitiveness (if price range is provided)
  if (options.priceRange && dish.price) {
    const { min = 0, max = Infinity } = options.priceRange;
    if (dish.price >= min && dish.price <= max) {
      score += 0.02;
    }
  }

  // Dietary preference alignment
  if (options.dietaryPreference) {
    if (options.dietaryPreference === "vegetarian" && dish.isVeg) {
      score += 0.05;
    } else if (options.dietaryPreference === "non-vegetarian" && !dish.isVeg) {
      score += 0.05;
    }
  }

  // User history boost (if provided)
  if (options.userHistory && dish._id) {
    const dishId = dish._id.toString();
    const orderCount = options.userHistory[dishId] || 0;
    if (orderCount > 0) {
      score += Math.min(orderCount * 0.02, 0.1); // Cap at 0.1
    }
  }

  return Math.min(score, 1); // Cap at 1
};

/**
 * Filter dishes by category and availability
 * @param {Array<Object>} dishes - Array of dishes
 * @param {Object} filters - Filter options
 * @returns {Array<Object>} - Filtered dishes
 */
const applyFilters = (dishes, filters = {}) => {
  return dishes.filter((dish) => {
    // Check availability
    if (filters.availableOnly !== false && !dish.isAvailable) {
      return false;
    }

    // Check category
    if (
      filters.category &&
      dish.category &&
      dish.category.name !== filters.category
    ) {
      return false;
    }

    // Check price range
    if (filters.minPrice && dish.price < filters.minPrice) {
      return false;
    }
    if (filters.maxPrice && dish.price > filters.maxPrice) {
      return false;
    }

    // Check dietary preferences
    if (filters.vegetarian) {
      // Use the isVeg field directly instead of relying on tags
      if (!dish.isVeg) {
        return false;
      }
    }

    if (filters.nonVegetarian) {
      // Filter for non-vegetarian dishes only
      if (dish.isVeg) {
        return false;
      }
    }

    if (filters.nonVegetarian) {
      // Filter for non-vegetarian dishes only
      if (dish.isVeg) {
        return false;
      }
    }

    return true;
  });
};

/**
 * Search dishes using keyword-based semantic search
 * @param {string} userQuery - User's search query
 * @param {Array<Object>} availableDishes - Available dishes
 * @param {Object} options - Search options
 * @returns {Promise<Array<Object>>} - Ranked search results
 */
export const searchDishes = async (
  userQuery,
  availableDishes,
  options = {}
) => {
  try {
    const {
      limit = 5,
      threshold = 0.1,
      filters = {},
      userHistory = {},
      priceRange = null,
      dietaryPreference = null,
    } = options;

    // First, try exact name matching (case-insensitive)
    const queryLower = userQuery.toLowerCase();
    const exactMatches = availableDishes.filter((dish) => {
      const dishNameLower = dish.name.toLowerCase();

      // Direct match
      if (
        dishNameLower.includes(queryLower) ||
        queryLower.includes(dishNameLower)
      ) {
        return true;
      }

      // Normalized match (remove special characters)
      const normalizedDishName = dishNameLower
        .replace(/[^\w\s]/g, " ")
        .replace(/\s+/g, " ")
        .trim();
      const normalizedQuery = queryLower
        .replace(/[^\w\s]/g, " ")
        .replace(/\s+/g, " ")
        .trim();

      return (
        normalizedDishName.includes(normalizedQuery) ||
        normalizedQuery.includes(normalizedDishName)
      );
    });

    // If we found exact matches, prioritize them
    if (exactMatches.length > 0) {
      const filteredExactMatches = applyFilters(exactMatches, filters);
      if (filteredExactMatches.length > 0) {
        return filteredExactMatches.slice(0, limit);
      }
    }

    // Extract keywords from user query
    const keywords = await extractKeywords(userQuery);

    if (keywords.length === 0) {
      // If no keywords extracted, return popular or random dishes
      return availableDishes
        .filter((dish) => dish.isAvailable)
        .sort((a, b) => (b.ratings?.average || 0) - (a.ratings?.average || 0))
        .slice(0, limit);
    }

    // Apply filters first
    const filteredDishes = applyFilters(availableDishes, filters);

    // Enhanced scoring options
    const scoringOptions = {
      userHistory,
      priceRange,
      dietaryPreference,
    };

    // Calculate enhanced relevance scores
    const scoredDishes = filteredDishes.map((dish) => {
      // Convert Mongoose document to plain object if needed
      const dishObj = dish.toObject ? dish.toObject() : dish;
      return {
        ...dishObj,
        relevanceScore: calculateRelevanceScore(dish, keywords, scoringOptions),
      };
    });

    // Filter by threshold and sort by relevance
    const relevantDishes = scoredDishes
      .filter((dish) => dish.relevanceScore >= threshold)
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);

    return relevantDishes;
  } catch (error) {
    console.error("Error in dish search:", error);
    // Fallback to simple filtering
    return availableDishes
      .filter((dish) => dish.isAvailable)
      .slice(0, options.limit || 5);
  }
};

/**
 * Get dish recommendations based on user preferences and context
 * @param {string} userQuery - User's query
 * @param {Array<Object>} availableDishes - Available dishes
 * @param {Object} context - Additional context (cart, history, etc.)
 * @returns {Promise<Array<Object>>} - Recommended dishes
 */
export const getRecommendations = async (
  userQuery,
  availableDishes,
  context = {}
) => {
  try {
    const {
      cartHistory = [],
      orderHistory = [],
      userPreferences = {},
    } = context;

    // Search for relevant dishes
    const searchResults = await searchDishes(userQuery, availableDishes, {
      limit: 10,
      threshold: 0.05,
    });

    // If we have search results, return them
    if (searchResults.length > 0) {
      return searchResults.slice(0, 5);
    }

    // Fallback: recommend based on popularity or user history
    const fallbackRecommendations = availableDishes
      .filter((dish) => dish.isAvailable)
      .sort((a, b) => (b.ratings?.average || 0) - (a.ratings?.average || 0))
      .slice(0, 5);

    return fallbackRecommendations;
  } catch (error) {
    console.error("Error getting recommendations:", error);
    return availableDishes.filter((dish) => dish.isAvailable).slice(0, 5);
  }
};

/**
 * Enhanced search with category-based grouping
 * @param {string} userQuery - User's search query
 * @param {Array<Object>} availableDishes - Available dishes
 * @param {Object} options - Search options
 * @returns {Promise<Object>} - Grouped search results
 */
export const searchWithCategories = async (
  userQuery,
  availableDishes,
  options = {}
) => {
  try {
    const searchResults = await searchDishes(
      userQuery,
      availableDishes,
      options
    );

    // Group by category
    const groupedResults = searchResults.reduce((groups, dish) => {
      const categoryName = dish.category?.name || "Other";
      if (!groups[categoryName]) {
        groups[categoryName] = [];
      }
      groups[categoryName].push(dish);
      return groups;
    }, {});

    return {
      total: searchResults.length,
      results: searchResults,
      groupedResults,
      keywords: await extractKeywords(userQuery),
    };
  } catch (error) {
    console.error("Error in categorized search:", error);
    return {
      total: 0,
      results: [],
      groupedResults: {},
      keywords: [],
    };
  }
};
