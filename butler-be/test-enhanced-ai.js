import { AgenticResponseGenerator } from "./services/agentic-response-service.js";

console.log("🚀 Starting Enhanced AI Test...");

// Test data
const mockUserId = "test-user-123";
const mockOutletId = "test-outlet-456";
const mockFoodChainId = "test-chain-789";
const mockLanguage = "en";

const mockAvailableDishes = [
  {
    _id: "dish1",
    name: "Paneer Butter Masala",
    category: "Main Course",
    cuisine: "Indian",
    price: 250,
    isVeg: true,
    description: "Rich and creamy paneer curry",
    isAvailable: true,
  },
  {
    _id: "dish2",
    name: "Chicken Biryani",
    category: "Main Course",
    cuisine: "Indian",
    price: 350,
    isVeg: false,
    description: "Aromatic basmati rice with tender chicken",
    isAvailable: true,
  },
  {
    _id: "dish3",
    name: "Masala Dosa",
    category: "South Indian",
    cuisine: "South Indian",
    price: 120,
    isVeg: true,
    description: "Crispy dosa with spiced potato filling",
    isAvailable: true,
  },
  {
    _id: "dish4",
    name: "Mutton Curry",
    category: "Main Course",
    cuisine: "Indian",
    price: 400,
    isVeg: false,
    description: "Spicy mutton curry with traditional spices",
    isAvailable: true,
  },
];

const mockConversation = [
  {
    sender: "user",
    message: "Hi there!",
    time: "2024-01-01T10:00:00Z",
  },
  {
    sender: "butler",
    message: "Hello! Welcome to our restaurant. How can I help you today?",
    time: "2024-01-01T10:00:30Z",
  },
];

async function testEnhancedAI() {
  console.log("🧪 Testing Enhanced AI Response System");
  console.log("=====================================");

  try {
    const generator = new AgenticResponseGenerator(
      mockUserId,
      mockOutletId,
      mockFoodChainId,
      mockLanguage
    );

    console.log("✅ AgenticResponseGenerator created successfully");

    // Test scenarios
    const testCases = [
      {
        name: "General Greeting",
        message: "Hello, what do you recommend?",
        expectedContext: "Should provide personalized recommendations",
      },
      {
        name: "Vegetarian Request",
        message: "Show me some vegetarian options",
        expectedContext: "Should only show vegetarian dishes (isVeg: true)",
      },
      {
        name: "Non-Vegetarian Request",
        message: "I want some chicken dishes",
        expectedContext: "Should show non-vegetarian dishes (isVeg: false)",
      },
      {
        name: "Specific Dish Query",
        message: "Tell me about your biryani",
        expectedContext: "Should reference Chicken Biryani from menu",
      },
      {
        name: "Menu Inquiry",
        message: "What's on your menu today?",
        expectedContext: "Should show knowledge of actual menu items",
      },
      {
        name: "Non-existent Dish",
        message: "Do you have pizza?",
        expectedContext: "Should clarify what's actually available",
      },
    ];

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing: ${testCase.name}`);
      console.log(`Message: "${testCase.message}"`);
      console.log(`Expected: ${testCase.expectedContext}`);

      try {
        const response = await generator.processMessage(
          testCase.message,
          "test-conversation-123",
          mockConversation,
          mockAvailableDishes
        );

        console.log("✅ Response received:");
        console.log(
          `   AI Message: ${response.components.message || "No message"}`
        );
        console.log(
          `   Recommendations: ${
            response.components.recommendations?.length || 0
          } dishes`
        );
        console.log(`   Response Type: ${response.metadata.responseType}`);
        console.log(
          `   Processing Steps: ${response.metadata.processingSteps.join(", ")}`
        );

        if (response.components.recommendations?.length > 0) {
          console.log("   Recommended Dishes:");
          response.components.recommendations
            .slice(0, 3)
            .forEach((dish, index) => {
              console.log(
                `     ${index + 1}. ${dish.name} (${
                  dish.isVeg ? "Veg" : "Non-Veg"
                }) - ₹${dish.price}`
              );
            });
        }
      } catch (error) {
        console.error(`❌ Error testing ${testCase.name}:`, error.message);
      }

      console.log("---");
    }

    console.log("\n🎉 Enhanced AI Testing Complete!");
  } catch (error) {
    console.error("❌ Main test error:", error);
  }
}

// Run the test
testEnhancedAI().catch((error) => {
  console.error("❌ Test execution failed:", error);
  process.exit(1);
});
