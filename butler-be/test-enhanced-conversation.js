import { AgenticResponseGenerator } from './services/agentic-response-service.js';
import mongoose from 'mongoose';

console.log("🧪 Testing Enhanced Conversation Flow");
console.log("===================================");

// Mock data similar to real conversation
const mockUserId = new mongoose.Types.ObjectId();
const mockOutletId = new mongoose.Types.ObjectId();
const mockFoodChainId = new mongoose.Types.ObjectId();
const mockConversationId = new mongoose.Types.ObjectId().toString();

const mockAvailableDishes = [
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Chicken Burger Meal",
    category: "Main Course",
    cuisine: "American",
    price: 299,
    isVeg: false,
    description: "Juicy chicken burger with fries and drink",
    isAvailable: true
  },
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Paneer Butter Masala",
    category: "Main Course", 
    cuisine: "Indian",
    price: 250,
    isVeg: true,
    description: "Rich and creamy paneer curry",
    isAvailable: true
  },
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Masala Dosa",
    category: "South Indian",
    cuisine: "South Indian", 
    price: 120,
    isVeg: true,
    description: "Crispy dosa with spiced potato filling",
    isAvailable: true
  }
];

const mockConversation = [
  {
    sender: "user",
    message: "Hi there!",
    time: "2024-01-01T10:00:00Z"
  },
  {
    sender: "butler", 
    message: "Hello! Welcome to our restaurant. How can I help you today?",
    time: "2024-01-01T10:00:30Z"
  },
  {
    sender: "user",
    message: "Show me some non-veg options",
    time: "2024-01-01T10:01:00Z"
  },
  {
    sender: "butler",
    message: "Here are our delicious non-vegetarian options...",
    time: "2024-01-01T10:01:30Z"
  }
];

async function testConversationFlow() {
  try {
    const generator = new AgenticResponseGenerator(
      mockUserId.toString(),
      mockOutletId.toString(),
      mockFoodChainId.toString(),
      "en"
    );

    console.log("✅ AgenticResponseGenerator created");

    // Test scenarios from conversation.md
    const testScenarios = [
      {
        name: "Add specific item to cart",
        message: "add chicken burger meal to cart",
        expectedSuccess: true,
        expectedOperation: "add"
      },
      {
        name: "Add it to cart (pronoun resolution)",
        message: "add it to my cart", 
        expectedSuccess: true, // Should resolve to last mentioned item
        expectedOperation: "add"
      },
      {
        name: "Order it (place order)",
        message: "order it",
        expectedSuccess: true,
        expectedOperation: "order"
      },
      {
        name: "Place order",
        message: "place order",
        expectedSuccess: true,
        expectedOperation: "order"
      },
      {
        name: "Remove specific item",
        message: "remove Chicken Burger meal from cart",
        expectedSuccess: true,
        expectedOperation: "remove"
      }
    ];

    for (const scenario of testScenarios) {
      console.log(`\n🔍 Testing: ${scenario.name}`);
      console.log(`Message: "${scenario.message}"`);
      
      try {
        const response = await generator.processMessage(
          scenario.message,
          mockConversationId,
          mockConversation,
          mockAvailableDishes
        );

        console.log("Response received:");
        console.log(`  Success: ${response.success}`);
        console.log(`  Response Type: ${response.metadata.responseType}`);
        console.log(`  AI Message: ${response.components.message || 'No message'}`);
        
        if (response.components.cartOperation) {
          console.log(`  Cart Operation: ${JSON.stringify(response.components.cartOperation, null, 2)}`);
          
          if (response.components.cartOperation.success === scenario.expectedSuccess) {
            console.log("✅ Operation success matches expected");
          } else {
            console.log(`❌ Expected success: ${scenario.expectedSuccess}, Got: ${response.components.cartOperation.success}`);
          }
          
          if (response.components.cartOperation.operation === scenario.expectedOperation) {
            console.log("✅ Operation type matches expected");
          } else {
            console.log(`❌ Expected operation: ${scenario.expectedOperation}, Got: ${response.components.cartOperation.operation}`);
          }
        } else {
          console.log("❌ No cart operation in response");
        }
        
        if (response.components.recommendations?.length > 0) {
          console.log(`  Recommendations: ${response.components.recommendations.length} dishes`);
        }
        
      } catch (error) {
        console.error(`❌ Error testing ${scenario.name}:`, error.message);
      }
      
      console.log("---");
    }

    console.log("\n🎉 Enhanced Conversation Testing Complete!");
    
  } catch (error) {
    console.error("❌ Test setup failed:", error);
  }
}

// Test AI message quality
async function testAIMessageQuality() {
  console.log("\n🔍 Testing AI Message Quality");
  console.log("=============================");
  
  const generator = new AgenticResponseGenerator(
    mockUserId.toString(),
    mockOutletId.toString(), 
    mockFoodChainId.toString(),
    "en"
  );

  const messageTests = [
    {
      name: "Menu inquiry",
      message: "What's on your menu?",
      expectedContext: "Should mention actual dishes from menu"
    },
    {
      name: "Vegetarian request",
      message: "Show me vegetarian options",
      expectedContext: "Should only mention vegetarian dishes"
    },
    {
      name: "Non-existent dish",
      message: "Do you have pizza?",
      expectedContext: "Should clarify what's actually available"
    }
  ];

  for (const test of messageTests) {
    console.log(`\n🔍 Testing: ${test.name}`);
    console.log(`Message: "${test.message}"`);
    console.log(`Expected: ${test.expectedContext}`);
    
    try {
      const response = await generator.processMessage(
        test.message,
        mockConversationId,
        mockConversation,
        mockAvailableDishes
      );

      console.log(`AI Response: "${response.components.message}"`);
      
      // Check if response mentions actual menu items
      const mentionsActualDishes = mockAvailableDishes.some(dish => 
        response.components.message?.toLowerCase().includes(dish.name.toLowerCase())
      );
      
      if (mentionsActualDishes) {
        console.log("✅ Response mentions actual menu items");
      } else {
        console.log("⚠️ Response doesn't mention specific menu items");
      }
      
    } catch (error) {
      console.error(`❌ Error testing ${test.name}:`, error.message);
    }
    
    console.log("---");
  }
}

// Run all tests
async function runAllTests() {
  try {
    await testConversationFlow();
    await testAIMessageQuality();
    console.log("\n🎉 All Enhanced Tests Complete!");
  } catch (error) {
    console.error("❌ Test execution failed:", error);
  }
}

runAllTests();
