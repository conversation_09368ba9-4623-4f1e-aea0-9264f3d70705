import express from "express";
import { getOptimizedRecommendationsEndpoint } from "../controllers/user-controller.js";
import {
  getPerformanceDashboard,
  getDetailedMetrics,
  manageCacheOperations,
  resetMetrics,
  exportMetrics,
  getSystemHealth,
  getOptimizationConfig,
  updateOptimizationConfig,
} from "../controllers/admin-optimization-controller.js";
import { authenticateToken, authorizeRoles } from "../middlewares/auth.js";

const router = express.Router();

/**
 * User-facing optimized recommendation routes
 */

// Optimized recommendations endpoint (replaces/supplements existing recommendations)
router.get(
  "/recommendations/optimized",
  authenticateToken,
  getOptimizedRecommendationsEndpoint
);

/**
 * Admin monitoring and management routes
 * Requires admin, manager, or super admin role
 */

// Performance dashboard - main overview
router.get(
  "/admin/performance/dashboard",
  authenticateToken,
  authorizeRoles(["admin", "manager", "super_admin"]),
  getPerformanceDashboard
);

// Detailed performance metrics with filtering
router.get(
  "/admin/performance/metrics",
  authenticateToken,
  authorizeRoles(["admin", "manager", "super_admin"]),
  getDetailedMetrics
);

// Cache management operations
router.post(
  "/admin/cache/manage",
  authenticateToken,
  authorizeRoles(["admin", "super_admin"]),
  manageCacheOperations
);

// Reset performance metrics (admin only)
router.post(
  "/admin/performance/reset",
  authenticateToken,
  authorizeRoles(["super_admin"]),
  resetMetrics
);

// Export performance data
router.get(
  "/admin/performance/export",
  authenticateToken,
  authorizeRoles(["admin", "manager", "super_admin"]),
  exportMetrics
);

// System health check
router.get(
  "/admin/system/health",
  authenticateToken,
  authorizeRoles(["admin", "manager", "super_admin"]),
  getSystemHealth
);

// Get optimization configuration
router.get(
  "/admin/optimization/config",
  authenticateToken,
  authorizeRoles(["admin", "super_admin"]),
  getOptimizationConfig
);

// Update optimization configuration
router.put(
  "/admin/optimization/config",
  authenticateToken,
  authorizeRoles(["super_admin"]),
  updateOptimizationConfig
);

/**
 * Public health check endpoints (no auth required)
 */

// Basic system status for monitoring tools
router.get("/health/optimization", async (req, res) => {
  try {
    const { performanceHealthCheck } = await import(
      "../services/performance-monitoring-service.js"
    );
    const health = performanceHealthCheck();

    res.status(health.status === "healthy" ? 200 : 503).json({
      status: health.status,
      service: "recommendation-optimization",
      timestamp: new Date(),
      uptime: health.uptime,
      version: "1.0.0",
    });
  } catch (error) {
    res.status(503).json({
      status: "error",
      service: "recommendation-optimization",
      error: error.message,
      timestamp: new Date(),
    });
  }
});

// Metrics endpoint for Prometheus/monitoring tools
router.get("/metrics/optimization", async (req, res) => {
  try {
    const { getPerformanceReport } = await import(
      "../services/performance-monitoring-service.js"
    );
    const report = getPerformanceReport();

    // Convert to Prometheus format
    const metrics = [
      `# HELP recommendation_requests_total Total number of recommendation requests`,
      `# TYPE recommendation_requests_total counter`,
      `recommendation_requests_total ${report.requestPatterns.totalRequests}`,

      `# HELP recommendation_response_time_seconds Average response time in seconds`,
      `# TYPE recommendation_response_time_seconds gauge`,
      `recommendation_response_time_seconds ${
        report.responseTimes.averageResponseTime / 1000
      }`,

      `# HELP recommendation_token_savings_percentage Token savings percentage`,
      `# TYPE recommendation_token_savings_percentage gauge`,
      `recommendation_token_savings_percentage ${report.tokenUsage.tokenSavingsPercentage}`,

      `# HELP recommendation_cache_hit_rate Cache hit rate percentage`,
      `# TYPE recommendation_cache_hit_rate gauge`,
      `recommendation_cache_hit_rate ${report.cachePerformance.cacheHitRate}`,

      `# HELP recommendation_fallback_usage_rate Fallback usage rate`,
      `# TYPE recommendation_fallback_usage_rate gauge`,
      `recommendation_fallback_usage_rate ${
        report.recommendationAccuracy.fallbackUsageRate * 100
      }`,

      `# HELP recommendation_optimization_efficiency System optimization efficiency`,
      `# TYPE recommendation_optimization_efficiency gauge`,
      `recommendation_optimization_efficiency ${
        report.systemOptimization.optimizationEfficiency * 100
      }`,
    ].join("\n");

    res.set("Content-Type", "text/plain");
    res.send(metrics);
  } catch (error) {
    res.status(500).send("# Error generating metrics");
  }
});

export default router;
