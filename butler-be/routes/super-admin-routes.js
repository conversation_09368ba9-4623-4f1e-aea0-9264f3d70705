import express from "express";
import {
  createFood<PERSON>hai<PERSON>,
  registerFood<PERSON>hainAdmin,
  getAllFood<PERSON>hains,
  getFood<PERSON>hainById,
  updateFood<PERSON>hain,
  updateFoodChainStatus,
  deleteFood<PERSON>hain,
  getFoodChainAdmins,
  createSuperAdmin,
  getSuperAdminDashboard,
  getSystemAnalytics,
  getFinancialReports,
  getAllRegistrationRequests,
  updateRegistrationRequestStatus,
  createFoodChainFromRequest,
} from "../controllers/super-admin-controller.js";
import { authenticateSuperAdminToken } from "../middlewares/auth.js";

const router = express.Router();

// Food Chain Management

router.post(
  "/super-admin/create-super-admin",
  authenticateSuperAdminToken,
  createSuperAdmin
);

router.post(
  "/super-admin/create-food-chain",
  authenticateSuperAdminToken,
  createFoodChain
);
router.post(
  "/super-admin/register",
  authenticateSuperAdminToken,
  registerFood<PERSON>hainAdmin
);
router.get(
  "/super-admin/food-chains",
  authenticateSuperAdminToken,
  getAllFoodChains
);
router.get(
  "/super-admin/food-chain/:id",
  authenticateSuperAdminToken,
  getFoodChainById
);
router.put(
  "/super-admin/food-chain/:id",
  authenticateSuperAdminToken,
  updateFoodChain
);
router.delete(
  "/super-admin/food-chain/:id",
  authenticateSuperAdminToken,
  deleteFoodChain
);
router.get(
  "/super-admin/food-chain/:foodChainId/admins",
  authenticateSuperAdminToken,
  getFoodChainAdmins
);

// Food Chain Status Management
router.put(
  "/super-admin/food-chain/:id/status",
  authenticateSuperAdminToken,
  updateFoodChainStatus
);

// Super Admin Dashboard
router.get(
  "/super-admin/dashboard",
  authenticateSuperAdminToken,
  getSuperAdminDashboard
);

// Analytics and Reporting
router.get(
  "/super-admin/analytics",
  authenticateSuperAdminToken,
  getSystemAnalytics
);

router.get(
  "/super-admin/financial-reports",
  authenticateSuperAdminToken,
  getFinancialReports
);

// Registration Request Management
router.get(
  "/super-admin/registration-requests",
  authenticateSuperAdminToken,
  getAllRegistrationRequests
);

router.put(
  "/super-admin/registration-requests/:id/status",
  authenticateSuperAdminToken,
  updateRegistrationRequestStatus
);

router.post(
  "/super-admin/registration-requests/:id/create-food-chain",
  authenticateSuperAdminToken,
  createFoodChainFromRequest
);

export default router;
