import express from "express";
import {
  transcribeAudio,
  testTranscription,
  uploadAudioMiddleware,
  handleUploadError,
} from "../controllers/audio-transcription-controller.js";

const router = express.Router();

/**
 * Audio transcription routes
 */

// Test endpoint
router.get("/test", testTranscription);

// Main transcription endpoint
router.post(
  "/",
  uploadAudioMiddleware,
  handleUploadError,
  transcribeAudio
);

export default router;
