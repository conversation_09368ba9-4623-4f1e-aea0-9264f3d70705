import express from "express";
import {
  createPaymentRequest,
  getPaymentDetails,
  handlePaymentWebhook,
  handlePayoutWebhook,
  verifyPayment,
} from "../controllers/payment-controller.js";
import {
  createFoodChainRazorpayAccount,
  addFoodChainStakeholder,
  requestFoodChainRouteConfiguration,
  addBankAccountToFoodChainProduct,
  transferFundsToFoodChainAccount,
  handleTransferWebhook,
  getFoodChainRazorpayStatus,
} from "../controllers/razorpay-route-controller.js";
import { authenticateToken } from "../middlewares/auth.js";

const router = express.Router();

// Admin routes for creating payment requests
router.post(
  "/admin/orders/:orderId/payment-request",
  authenticateToken,
  createPaymentRequest
);

// Routes for getting payment details
router.get(
  "/admin/orders/:orderId/payment",
  authenticateToken,
  getPaymentDetails
);

router.get(
  "/user/orders/:orderId/payment",
  authenticateToken,
  getPaymentDetails
);

// Webhooks for Razorpay updates
router.post("/webhook/razorpay/payment", handlePaymentWebhook);
router.post("/webhook/razorpay/payout", handlePayoutWebhook);
router.post("/webhook/razorpay/transfer", handleTransferWebhook);

// Verify payment after completion
router.post("/payments/verify", authenticateToken, verifyPayment);

// Razorpay Route setup endpoints (integrated with payments)
router.post(
  "/admin/payments/food-chain/:foodChainId/razorpay-setup",
  authenticateToken,
  createFoodChainRazorpayAccount
);

router.post(
  "/admin/payments/food-chain/:foodChainId/stakeholder",
  authenticateToken,
  addFoodChainStakeholder
);

router.post(
  "/admin/payments/food-chain/:foodChainId/route-activation",
  authenticateToken,
  requestFoodChainRouteConfiguration
);

router.post(
  "/admin/payments/food-chain/:foodChainId/bank-account",
  authenticateToken,
  addBankAccountToFoodChainProduct
);

router.post(
  "/admin/payments/food-chain/:foodChainId/transfer",
  authenticateToken,
  transferFundsToFoodChainAccount
);

// Get Razorpay setup status for a food chain
router.get(
  "/admin/payments/food-chain/:foodChainId/razorpay-status",
  authenticateToken,
  getFoodChainRazorpayStatus
);

export default router;
