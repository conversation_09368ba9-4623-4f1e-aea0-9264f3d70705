import express from "express";
import {
  createSubscriptionPlan,
  getAllSubscriptionPlans,
  updateSubscriptionPlan,
  createSubscription,
  getAllSubscriptions,
  getFoodChainSubscription,
  getInvoiceDetails,
  createInvoice,
  handleSubscriptionPaymentWebhook,
  verifySubscriptionPayment,
  exemptFromNextPayment,
  triggerMonthlyBilling,
  upgradeSubscriptionPlan,
  checkTrialPeriods,
} from "../controllers/subscription-controller.js";
import {
  authenticateToken,
  authenticateSuperAdminToken,
} from "../middlewares/auth.js";

const router = express.Router();

// Subscription Plan Routes (Super Admin only)
router.post(
  "/super-admin/subscription/plans",
  authenticateSuperAdminToken,
  createSubscriptionPlan
);

router.get("/subscription/plans", getAllSubscriptionPlans);

router.put(
  "/super-admin/subscription/plans/:planId",
  authenticateSuperAdminToken,
  updateSubscriptionPlan
);

// Subscription Management Routes
router.post(
  "/super-admin/subscription",
  authenticateSuperAdminToken,
  createSubscription
);

router.get(
  "/super-admin/subscriptions",
  authenticateSuperAdminToken,
  getAllSubscriptions
);

router.get(
  "/admin/subscription/food-chain/:foodChainId",
  authenticateToken,
  getFoodChainSubscription
);

// Invoice Routes
router.get(
  "/admin/subscription/invoices/:invoiceId",
  authenticateToken,
  getInvoiceDetails
);

router.post(
  "/super-admin/subscription/:subscriptionId/invoice",
  authenticateSuperAdminToken,
  createInvoice
);

// Exempt subscription from next payment
router.post(
  "/super-admin/subscription/:subscriptionId/exempt",
  authenticateSuperAdminToken,
  exemptFromNextPayment
);

// Manually trigger monthly billing
router.post(
  "/super-admin/subscription/trigger-billing",
  authenticateSuperAdminToken,
  triggerMonthlyBilling
);

// Manually check trial periods
router.post(
  "/super-admin/subscription/check-trials",
  authenticateSuperAdminToken,
  checkTrialPeriods
);

// Upgrade subscription plan
router.post(
  "/admin/subscription/:subscriptionId/upgrade",
  authenticateToken,
  upgradeSubscriptionPlan
);

// Payment Routes
router.post(
  "/webhook/razorpay/subscription-payment",
  handleSubscriptionPaymentWebhook
);

router.post(
  "/subscription/payments/verify",
  authenticateToken,
  verifySubscriptionPayment
);

export default router;
