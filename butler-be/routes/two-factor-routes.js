import express from "express";
import {
  generate2FASecret,
  verify2FAToken,
  disable2FA,
  validate2FAToken,
  generateBackupCodes,
} from "../controllers/two-factor-controller.js";
import { authenticateToken } from "../middlewares/auth.js";

const router = express.Router();

// Protected routes (require authentication)
router.post("/2fa/generate", authenticateToken, generate2FASecret);
router.post("/2fa/verify", authenticateToken, verify2FAToken);
router.post("/2fa/disable", authenticateToken, disable2FA);
router.post("/2fa/backup-codes", authenticateToken, generateBackupCodes);

// Public route (used during login)
router.post("/2fa/validate", validate2FAToken);

export default router;
