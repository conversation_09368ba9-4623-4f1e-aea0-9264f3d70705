import express from "express";
import {
  getCart,
  addToCart,
  removeFromCart,
  updateCartItemQuantity,
  clearCart,
  syncCart,
} from "../controllers/cart-controller.js";
import { authenticateToken } from "../middlewares/auth.js";

const router = express.Router();

// Get user's cart
router.get("/user/cart", authenticateToken, getCart);

// Add item to cart
router.post("/user/cart/add", authenticateToken, addToCart);

// Remove item from cart
router.delete("/user/cart/remove/:dishId", authenticateToken, removeFromCart);

// Update item quantity in cart
router.put(
  "/user/cart/update/:dishId",
  authenticateToken,
  updateCartItemQuantity
);

// Clear cart
router.delete("/user/cart/clear", authenticateToken, clearCart);

// Sync cart from localStorage to backend
router.post("/user/cart/sync", authenticateToken, syncCart);

export default router;
