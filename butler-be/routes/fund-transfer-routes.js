import express from "express";
import {
  createFoodChainFundAccount,
  updateFoodChainBankDetails,
  createFundTransfer,
  getFoodChainFundTransfers,
  getFundTransferDetails,
  getFoodChainTransferHistory,
} from "../controllers/fund-transfer-controller.js";
import {
  authenticateToken,
  authenticateSuperAdminToken,
} from "../middlewares/auth.js";

const router = express.Router();

// Food chain bank details routes
router.put(
  "/admin/food-chain/:foodChainId/bank-details",
  authenticateToken,
  updateFoodChainBankDetails
);

// Fund account routes
router.post(
  "/admin/food-chain/:foodChainId/fund-account",
  authenticateToken,
  createFoodChainFundAccount
);

// Fund transfer routes (super admin only)
router.post(
  "/super-admin/food-chain/:foodChainId/transfer",
  authenticateSuperAdminToken,
  createFundTransfer
);

// Get food chain transfers
router.get(
  "/admin/food-chain/:foodChainId/transfers",
  authenticateToken,
  getFoodChainFundTransfers
);

// Get transfer details
router.get(
  "/admin/transfers/:transferId",
  authenticateToken,
  getFundTransferDetails
);

// Get food chain transfer history
router.get(
  "/admin/food-chain/:foodChainId/transfers/history",
  authenticateToken,
  getFoodChainTransferHistory
);

export default router;
