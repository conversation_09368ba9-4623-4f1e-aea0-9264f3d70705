import mongoose from 'mongoose';
import Offer from './models/Offer.js';
import Dish from './models/Dish.js';
import Order from './models/Order.js';
import FoodChain from './models/FoodChain.js';
import Outlet from './models/Outlet.js';
import User from './models/User.js';
import { validateAndApplyOffers } from './services/offer-validation-service.js';
import dotenv from 'dotenv';

dotenv.config();

const testOffers = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB Atlas');

    console.log('\n=== COMPREHENSIVE OFFERS TESTING ===\n');

    // 1. Test existing offers and identify issues
    console.log('1. ANALYZING EXISTING OFFERS...');
    const offers = await Offer.find({});
    console.log(`Found ${offers.length} offers`);

    const issues = [];
    
    offers.forEach((offer, index) => {
      console.log(`\n${index + 1}. ${offer.name} (${offer.offerType})`);
      console.log(`   Active: ${offer.isActive}`);
      
      // Check for incomplete discount details
      if (offer.discountDetails) {
        const { discountType, discountValue, maxDiscount } = offer.discountDetails;
        
        if (offer.offerType === 'dayOfWeek') {
          if (!discountValue || discountValue === 0) {
            issues.push({
              offerId: offer._id,
              name: offer.name,
              type: 'dayOfWeek',
              issue: 'discountValue is 0 or missing',
              current: discountValue
            });
            console.log(`   ❌ ISSUE: discountValue is ${discountValue}`);
          }
          
          if (!offer.discountDetails.timeRestrictions?.daysOfWeek?.length) {
            issues.push({
              offerId: offer._id,
              name: offer.name,
              type: 'dayOfWeek',
              issue: 'daysOfWeek not properly configured',
              current: offer.discountDetails.timeRestrictions?.daysOfWeek
            });
            console.log(`   ❌ ISSUE: daysOfWeek not configured`);
          } else {
            console.log(`   ✅ Days configured: ${offer.discountDetails.timeRestrictions.daysOfWeek}`);
          }
        }
        
        if (offer.offerType === 'BOGO') {
          if (!offer.discountDetails.buyQuantity || !offer.discountDetails.getQuantity) {
            issues.push({
              offerId: offer._id,
              name: offer.name,
              type: 'BOGO',
              issue: 'buyQuantity or getQuantity missing',
              current: { buy: offer.discountDetails.buyQuantity, get: offer.discountDetails.getQuantity }
            });
            console.log(`   ❌ ISSUE: BOGO quantities not configured`);
          }
        }
        
        if (offer.offerType === 'combo') {
          if (!offer.discountDetails.comboItems?.length || !offer.discountDetails.comboPrice) {
            issues.push({
              offerId: offer._id,
              name: offer.name,
              type: 'combo',
              issue: 'comboItems or comboPrice missing',
              current: { items: offer.discountDetails.comboItems?.length, price: offer.discountDetails.comboPrice }
            });
            console.log(`   ❌ ISSUE: Combo details not configured`);
          }
        }
        
        console.log(`   Discount: ${discountType} - ${discountValue}${discountType === 'percentage' ? '%' : '₹'}`);
        if (maxDiscount) console.log(`   Max Discount: ₹${maxDiscount}`);
      } else {
        issues.push({
          offerId: offer._id,
          name: offer.name,
          type: offer.offerType,
          issue: 'discountDetails missing entirely',
          current: null
        });
        console.log(`   ❌ ISSUE: No discount details found`);
      }
    });

    console.log(`\n=== ISSUES FOUND: ${issues.length} ===`);
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.name} (${issue.type}): ${issue.issue}`);
      console.log(`   Current value: ${JSON.stringify(issue.current)}`);
    });

    // 2. Fix the issues
    console.log('\n2. FIXING IDENTIFIED ISSUES...');
    
    for (const issue of issues) {
      const offer = await Offer.findById(issue.offerId);
      if (!offer) continue;
      
      console.log(`\nFixing: ${issue.name}`);
      
      if (issue.type === 'dayOfWeek' && issue.issue === 'discountValue is 0 or missing') {
        offer.discountDetails.discountValue = 15; // Set 15% discount
        offer.discountDetails.discountType = 'percentage';
        offer.discountDetails.maxDiscount = 200; // Max ₹200 discount
        console.log(`   ✅ Set discount to 15% (max ₹200)`);
      }
      
      if (issue.type === 'BOGO' && issue.issue === 'buyQuantity or getQuantity missing') {
        offer.discountDetails.buyQuantity = 2;
        offer.discountDetails.getQuantity = 1;
        console.log(`   ✅ Set BOGO to Buy 2 Get 1`);
      }
      
      if (issue.type === 'combo' && issue.issue === 'comboItems or comboPrice missing') {
        // Get some dishes to create a combo
        const dishes = await Dish.find({}).limit(3);
        if (dishes.length >= 2) {
          offer.discountDetails.comboItems = [
            { dishId: dishes[0]._id, quantity: 1 },
            { dishId: dishes[1]._id, quantity: 1 }
          ];
          offer.discountDetails.comboPrice = Math.round((dishes[0].price + dishes[1].price) * 0.8); // 20% off combo
          console.log(`   ✅ Set combo with 2 dishes at 20% off`);
        }
      }
      
      await offer.save();
      console.log(`   💾 Saved changes for ${offer.name}`);
    }

    // 3. Test offer application with sample order
    console.log('\n3. TESTING OFFER APPLICATION...');
    
    // Get sample data
    const sampleDishes = await Dish.find({}).limit(5);
    const sampleOutlet = await Outlet.findOne({});
    const sampleFoodChain = await FoodChain.findOne({});
    
    if (!sampleDishes.length || !sampleOutlet || !sampleFoodChain) {
      console.log('❌ Missing sample data (dishes, outlet, or food chain)');
      return;
    }
    
    // Create test order data
    const testOrderData = {
      outletId: sampleOutlet._id,
      foodChainId: sampleFoodChain._id,
      items: [
        {
          dishId: sampleDishes[0]._id,
          quantity: 2,
          price: sampleDishes[0].price,
          name: sampleDishes[0].name
        },
        {
          dishId: sampleDishes[1]._id,
          quantity: 1,
          price: sampleDishes[1].price,
          name: sampleDishes[1].name
        }
      ],
      totalAmount: (sampleDishes[0].price * 2) + sampleDishes[1].price
    };
    
    console.log(`\nTest order: ${testOrderData.items.length} items, Total: ₹${testOrderData.totalAmount}`);
    testOrderData.items.forEach(item => {
      console.log(`  - ${item.name} x${item.quantity} = ₹${item.price * item.quantity}`);
    });
    
    // Test offer validation and application
    const result = await validateAndApplyOffers(testOrderData);
    
    console.log('\n=== OFFER APPLICATION RESULTS ===');
    console.log(`Success: ${result.success}`);
    console.log(`Applied Offers: ${result.appliedOffers?.length || 0}`);
    console.log(`Total Discount: ₹${result.totalDiscount || 0}`);
    console.log(`Final Amount: ₹${result.finalAmount || testOrderData.totalAmount}`);
    
    if (result.appliedOffers?.length) {
      result.appliedOffers.forEach((appliedOffer, index) => {
        console.log(`\n${index + 1}. ${appliedOffer.name}`);
        console.log(`   Type: ${appliedOffer.type}`);
        console.log(`   Discount: ₹${appliedOffer.discount}`);
        console.log(`   Description: ${appliedOffer.description || 'N/A'}`);
      });
    }

    console.log('\n=== TEST COMPLETED ===');
    process.exit(0);
    
  } catch (error) {
    console.error('Error during testing:', error);
    process.exit(1);
  }
};

testOffers();
