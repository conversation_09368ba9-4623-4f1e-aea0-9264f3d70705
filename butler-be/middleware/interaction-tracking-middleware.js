import { trackUserInteraction, trackSearchPattern } from "../services/real-time-preference-learning-service.js";

/**
 * Middleware to automatically track user interactions for preference learning
 */

/**
 * Track dish views when users fetch dishes
 */
export const trackDishViews = (req, res, next) => {
  // Store original json method
  const originalJson = res.json;
  
  // Override json method to track interactions
  res.json = function(data) {
    // Call original json method first
    const result = originalJson.call(this, data);
    
    // Track interactions asynchronously (don't block response)
    setImmediate(async () => {
      try {
        const userId = req.user?._id || req.query?.userId;
        const sessionId = req.sessionID || req.headers['x-session-id'] || 'default';
        
        if (userId && data.success && data.data && Array.isArray(data.data)) {
          // Track views for dishes returned in the response
          const dishes = data.data.slice(0, 10); // Track only first 10 dishes to avoid spam
          
          for (const dish of dishes) {
            if (dish._id) {
              await trackUserInteraction(
                userId.toString(),
                dish._id.toString(),
                'view',
                {
                  sessionId,
                  endpoint: req.originalUrl,
                  timestamp: new Date(),
                  userAgent: req.headers['user-agent']
                }
              );
            }
          }
        }
      } catch (error) {
        console.error("Error tracking dish views:", error);
      }
    });
    
    return result;
  };
  
  next();
};

/**
 * Track search interactions when users search for dishes
 */
export const trackSearchInteractions = (req, res, next) => {
  // Store original json method
  const originalJson = res.json;
  
  // Override json method to track search patterns
  res.json = function(data) {
    // Call original json method first
    const result = originalJson.call(this, data);
    
    // Track search patterns asynchronously
    setImmediate(async () => {
      try {
        const userId = req.user?._id || req.query?.userId;
        const searchQuery = req.query?.message || req.query?.query || req.body?.query;
        const sessionId = req.sessionID || req.headers['x-session-id'] || 'default';
        
        if (userId && searchQuery && data.success) {
          // Extract search results from response
          let searchResults = [];
          
          if (data.recommendations && Array.isArray(data.recommendations)) {
            searchResults = data.recommendations.map(rec => rec.dish || rec).filter(Boolean);
          } else if (data.data && Array.isArray(data.data)) {
            searchResults = data.data;
          }
          
          await trackSearchPattern(
            userId.toString(),
            searchQuery,
            searchResults,
            {
              sessionId,
              endpoint: req.originalUrl,
              timestamp: new Date(),
              resultCount: searchResults.length,
              userAgent: req.headers['user-agent']
            }
          );
        }
      } catch (error) {
        console.error("Error tracking search interactions:", error);
      }
    });
    
    return result;
  };
  
  next();
};

/**
 * Track cart operations
 */
export const trackCartOperations = (req, res, next) => {
  // Store original json method
  const originalJson = res.json;
  
  // Override json method to track cart operations
  res.json = function(data) {
    // Call original json method first
    const result = originalJson.call(this, data);
    
    // Track cart operations asynchronously
    setImmediate(async () => {
      try {
        const userId = req.user?._id || req.query?.userId || req.body?.userId;
        const sessionId = req.sessionID || req.headers['x-session-id'] || 'default';
        
        if (userId && data.success) {
          // Determine interaction type based on endpoint and method
          let interactionType = 'add_to_cart';
          
          if (req.originalUrl.includes('remove') || req.method === 'DELETE') {
            interactionType = 'remove_from_cart';
          } else if (req.originalUrl.includes('update')) {
            interactionType = 'update_cart';
          }
          
          // Extract dish information from request
          const dishId = req.body?.dishId || req.params?.dishId || req.query?.dishId;
          
          if (dishId) {
            await trackUserInteraction(
              userId.toString(),
              dishId.toString(),
              interactionType,
              {
                sessionId,
                endpoint: req.originalUrl,
                timestamp: new Date(),
                quantity: req.body?.quantity || 1,
                userAgent: req.headers['user-agent']
              }
            );
          }
        }
      } catch (error) {
        console.error("Error tracking cart operations:", error);
      }
    });
    
    return result;
  };
  
  next();
};

/**
 * Track order placements
 */
export const trackOrderPlacements = (req, res, next) => {
  // Store original json method
  const originalJson = res.json;
  
  // Override json method to track order placements
  res.json = function(data) {
    // Call original json method first
    const result = originalJson.call(this, data);
    
    // Track order placements asynchronously
    setImmediate(async () => {
      try {
        const userId = req.user?._id || req.body?.userId;
        const sessionId = req.sessionID || req.headers['x-session-id'] || 'default';
        
        if (userId && data.success && data.order) {
          const order = data.order;
          
          // Track each dish in the order
          if (order.items && Array.isArray(order.items)) {
            for (const item of order.items) {
              if (item.dishId) {
                await trackUserInteraction(
                  userId.toString(),
                  item.dishId.toString(),
                  'order',
                  {
                    sessionId,
                    endpoint: req.originalUrl,
                    timestamp: new Date(),
                    quantity: item.quantity,
                    price: item.price,
                    orderId: order._id,
                    userAgent: req.headers['user-agent']
                  }
                );
              }
            }
          }
        }
      } catch (error) {
        console.error("Error tracking order placements:", error);
      }
    });
    
    return result;
  };
  
  next();
};

/**
 * Track recommendation interactions
 */
export const trackRecommendationInteractions = (req, res, next) => {
  // Store original json method
  const originalJson = res.json;
  
  // Override json method to track recommendation views
  res.json = function(data) {
    // Call original json method first
    const result = originalJson.call(this, data);
    
    // Track recommendation interactions asynchronously
    setImmediate(async () => {
      try {
        const userId = req.user?._id || req.query?.userId;
        const sessionId = req.sessionID || req.headers['x-session-id'] || 'default';
        
        if (userId && data.success && data.recommendations) {
          // Track views for recommended dishes
          const recommendations = Array.isArray(data.recommendations) 
            ? data.recommendations 
            : [data.recommendations];
          
          for (const recommendation of recommendations.slice(0, 5)) {
            const dish = recommendation.dish || recommendation;
            if (dish && dish._id) {
              await trackUserInteraction(
                userId.toString(),
                dish._id.toString(),
                'view',
                {
                  sessionId,
                  endpoint: req.originalUrl,
                  timestamp: new Date(),
                  isRecommendation: true,
                  recommendationReason: recommendation.reason || recommendation.recommendationReason,
                  userAgent: req.headers['user-agent']
                }
              );
            }
          }
        }
      } catch (error) {
        console.error("Error tracking recommendation interactions:", error);
      }
    });
    
    return result;
  };
  
  next();
};

/**
 * General interaction tracking middleware
 * Can be applied to any endpoint to track basic user activity
 */
export const trackGeneralInteractions = (interactionType = 'view') => {
  return (req, res, next) => {
    // Store original json method
    const originalJson = res.json;
    
    // Override json method to track interactions
    res.json = function(data) {
      // Call original json method first
      const result = originalJson.call(this, data);
      
      // Track interactions asynchronously
      setImmediate(async () => {
        try {
          const userId = req.user?._id || req.query?.userId;
          const sessionId = req.sessionID || req.headers['x-session-id'] || 'default';
          
          if (userId) {
            // Extract relevant IDs from request or response
            const dishId = req.params?.dishId || req.query?.dishId || req.body?.dishId;
            const outletId = req.params?.outletId || req.query?.outletId || req.body?.outletId;
            
            if (dishId) {
              await trackUserInteraction(
                userId.toString(),
                dishId.toString(),
                interactionType,
                {
                  sessionId,
                  endpoint: req.originalUrl,
                  timestamp: new Date(),
                  outletId: outletId?.toString(),
                  userAgent: req.headers['user-agent']
                }
              );
            }
          }
        } catch (error) {
          console.error(`Error tracking ${interactionType} interactions:`, error);
        }
      });
      
      return result;
    };
    
    next();
  };
};

/**
 * Middleware to add real-time preferences to request object
 */
export const addRealTimePreferences = (req, res, next) => {
  try {
    const userId = req.user?._id || req.query?.userId;
    const sessionId = req.sessionID || req.headers['x-session-id'] || 'default';
    
    if (userId) {
      // Import here to avoid circular dependencies
      const { getRealTimePreferences } = require("../services/real-time-preference-learning-service.js");
      req.realTimePreferences = getRealTimePreferences(userId.toString(), sessionId);
    }
  } catch (error) {
    console.error("Error adding real-time preferences:", error);
  }
  
  next();
};
