import cron from "node-cron";
import SubscriptionPlan from "../models/SubscriptionPlan.js";
import Subscription from "../models/Subscription.js";
import SubscriptionInvoice from "../models/SubscriptionInvoice.js";
import Outlet from "../models/Outlet.js";
import FoodChain from "../models/FoodChain.js";
import { createSubscriptionInvoice } from "./razorpayInvoice.js";

/**
 * Create default subscription plan if none exists
 */
export const createDefaultSubscriptionPlan = async () => {
  try {
    // Check if any plan exists
    const existingPlans = await SubscriptionPlan.countDocuments();

    if (existingPlans === 0) {
      console.log("Creating default subscription plan...");

      // Create default plan
      const defaultPlan = new SubscriptionPlan({
        name: "Standard Plan",
        description: "Standard subscription plan for Butler",
        price: 2000, // ₹2000 per outlet
        interval: "monthly",
        features: [
          "Full access to Butler platform",
          "Unlimited orders",
          "Customer management",
          "Analytics dashboard",
          "Payment processing",
        ],
        isDefault: true,
        isActive: true,
      });

      await defaultPlan.save();
      console.log("Default subscription plan created successfully");
    }
  } catch (error) {
    console.error("Error creating default subscription plan:", error);
  }
};

/**
 * Calculate the first day of the current month
 * @returns {Date} First day of the current month
 */
const getFirstDayOfMonth = () => {
  const date = new Date();
  date.setDate(1);
  date.setHours(0, 0, 0, 0);
  return date;
};

/**
 * Calculate the last day of the current month
 * @returns {Date} Last day of the current month
 */
const getLastDayOfMonth = () => {
  const date = new Date();
  date.setMonth(date.getMonth() + 1);
  date.setDate(0);
  date.setHours(23, 59, 59, 999);
  return date;
};

/**
 * Calculate prorated amount based on days used
 * @param {Date} startDate - The start date of the period
 * @param {Date} endDate - The end date of the period
 * @param {Number} fullPrice - The full monthly price
 * @returns {Number} Prorated amount
 */
const calculateProratedAmount = (startDate, endDate, fullPrice) => {
  // Ensure dates are valid
  if (!startDate || !endDate) {
    return 0;
  }

  // Convert to date objects if they're strings
  const start = new Date(startDate);
  const end = new Date(endDate);

  // If end date is before start date, return 0
  if (end < start) {
    return 0;
  }

  // Calculate total days in the month
  const daysInMonth = new Date(
    end.getFullYear(),
    end.getMonth() + 1,
    0
  ).getDate();

  // Calculate days used
  const msPerDay = 1000 * 60 * 60 * 24;
  const daysUsed = Math.ceil((end.getTime() - start.getTime()) / msPerDay) + 1; // +1 to include both start and end days

  // Calculate prorated amount
  return Math.round((daysUsed / daysInMonth) * fullPrice);
};

/**
 * Calculate prorated amount for a subscription with trial period
 * @param {Date} trialEndDate - The trial end date
 * @param {Date} periodStartDate - The start date of the billing period
 * @param {Date} periodEndDate - The end date of the billing period
 * @param {Number} fullPrice - The full monthly price
 * @returns {Number} Prorated amount
 */
const calculateProratedAmountWithTrial = (
  trialEndDate,
  periodStartDate,
  periodEndDate,
  fullPrice
) => {
  // Ensure dates are valid
  if (!trialEndDate || !periodStartDate || !periodEndDate) {
    return 0;
  }

  // Convert to date objects if they're strings
  const trial = new Date(trialEndDate);
  const start = new Date(periodStartDate);
  const end = new Date(periodEndDate);

  // If the entire period is before trial ends, return 0
  if (end <= trial) {
    return 0;
  }

  // If trial has already ended before the period starts, charge for the full period
  if (trial < start) {
    return calculateProratedAmount(start, end, fullPrice);
  }

  // Otherwise, charge only for days after trial ends
  return calculateProratedAmount(trial, end, fullPrice);
};

/**
 * Process monthly subscription billing
 */
export const processMonthlySubscriptions = async () => {
  try {
    console.log("Processing monthly subscriptions...");

    // Get the first day of the current month (for billing period start)
    const firstDayOfMonth = getFirstDayOfMonth();

    // Get the last day of the previous month (for billing period end)
    const lastDayOfPreviousMonth = new Date(firstDayOfMonth);
    lastDayOfPreviousMonth.setDate(0);
    lastDayOfPreviousMonth.setHours(23, 59, 59, 999);

    // Get active and pending subscriptions with autoRenew enabled
    // Note: We'll handle exempted subscriptions in the loop for better visibility
    const subscriptions = await Subscription.find({
      status: { $in: ["active", "pending"] },
      autoRenew: true,
    })
      .populate("foodChainId")
      .populate("planId")
      .populate("planHistory.planId");

    console.log(`Found ${subscriptions.length} subscriptions to process`);

    // Track results for reporting
    const results = {
      processed: 0,
      skipped: 0,
      failed: 0,
      exempted: 0,
      noOutlets: 0,
    };

    for (const subscription of subscriptions) {
      try {
        // Skip if exempted from payment
        if (subscription.isExemptedFromNextPayment) {
          console.log(
            `Subscription ${subscription._id} for ${
              subscription.foodChainId?.name || "Unknown"
            } is exempted from payment`
          );
          results.exempted++;
          continue;
        }

        // Get all outlets for this food chain
        const outlets = await Outlet.find({
          foodChain: subscription.foodChainId._id,
        });

        if (outlets.length === 0) {
          console.log(
            `No outlets found for food chain ${subscription.foodChainId.name}`
          );
          continue;
        }

        // Calculate total amount based on subscription trial period and plan changes
        let totalAmount = 0;
        const invoiceItems = [];

        // Get the subscription trial end date
        const trialEndDate =
          subscription.trialEndDate ||
          (() => {
            const date = new Date(subscription.createdAt);
            date.setDate(date.getDate() + 15); // 15-day trial
            return date;
          })();

        // Check if the subscription has plan history
        if (subscription.planHistory && subscription.planHistory.length > 0) {
          // Handle multiple plans used in the billing period
          for (const planRecord of subscription.planHistory) {
            // Skip if plan record doesn't have start date
            if (!planRecord.startDate) continue;

            // Get plan start and end dates within the billing period
            const planStartDate = new Date(planRecord.startDate);
            const planEndDate = planRecord.endDate
              ? new Date(planRecord.endDate)
              : lastDayOfPreviousMonth;

            // Skip if plan was not used in the previous month
            if (
              planStartDate > lastDayOfPreviousMonth ||
              planEndDate < firstDayOfMonth
            ) {
              continue;
            }

            // Calculate prorated amount for this plan
            const proratedAmount = calculateProratedAmountWithTrial(
              trialEndDate,
              planStartDate > firstDayOfMonth ? planStartDate : firstDayOfMonth,
              planEndDate < lastDayOfPreviousMonth
                ? planEndDate
                : lastDayOfPreviousMonth,
              planRecord.price
            );

            if (proratedAmount > 0) {
              // Add to total amount
              totalAmount += proratedAmount * outlets.length;

              // Add as invoice item
              invoiceItems.push({
                description: `${planRecord.name} - Prorated (${
                  planStartDate.toISOString().split("T")[0]
                } to ${planEndDate.toISOString().split("T")[0]})`,
                quantity: outlets.length,
                unitPrice: proratedAmount,
                amount: proratedAmount * outlets.length,
              });
            }
          }
        } else {
          // Handle single plan (current plan)
          const proratedAmount = calculateProratedAmountWithTrial(
            trialEndDate,
            firstDayOfMonth,
            lastDayOfPreviousMonth,
            subscription.planId.price
          );

          if (proratedAmount > 0) {
            // Add to total amount
            totalAmount += proratedAmount * outlets.length;

            // Add as invoice item
            invoiceItems.push({
              description: `${subscription.planId.name} - ${subscription.planId.interval} subscription`,
              quantity: outlets.length,
              unitPrice: proratedAmount,
              amount: proratedAmount * outlets.length,
            });
          }
        }

        // Skip if no billable amount
        if (totalAmount === 0 || invoiceItems.length === 0) {
          console.log(
            `No billable amount for food chain ${subscription.foodChainId.name}`
          );
          continue;
        }

        // Generate invoice number
        const invoiceNumber = `INV-${Date.now()}-${subscription.foodChainId._id
          .toString()
          .substring(0, 6)}`;

        // Create invoice
        const invoice = new SubscriptionInvoice({
          subscriptionId: subscription._id,
          foodChainId: subscription.foodChainId._id,
          invoiceNumber,
          amount: totalAmount,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Due in 7 days
          items: invoiceItems,
        });

        await invoice.save();

        // Update subscription with latest invoice ID
        subscription.lastInvoiceId = invoice._id;
        subscription.outletCount = outlets.length;
        subscription.totalAmount = totalAmount;
        await subscription.save();

        // Create Razorpay invoice with error handling
        try {
          const razorpayInvoice = await createSubscriptionInvoice(
            invoice,
            subscription.foodChainId
          );

          // Update invoice with Razorpay invoice details
          invoice.razorpayInvoiceId = razorpayInvoice.id;
          invoice.paymentLink = razorpayInvoice.short_url;
          invoice.status = "created"; // Mark as successfully created
          await invoice.save();

          results.processed++;
        } catch (razorpayError) {
          console.error(
            `Error creating Razorpay invoice for subscription ${subscription._id}:`,
            razorpayError
          );

          // Update invoice status to reflect the error
          invoice.status = "error";
          invoice.notes = `Failed to create Razorpay invoice: ${
            razorpayError.message || "Unknown error"
          }`;
          await invoice.save();

          results.failed++;
        }

        console.log(
          `Created invoice ${invoiceNumber} for ${subscription.foodChainId.name} - Amount: ${totalAmount}`
        );

        // Reset exemption if it was set for one-time exemption
        if (subscription.isExemptedFromNextPayment) {
          subscription.isExemptedFromNextPayment = false;
          subscription.exemptionReason = null;
          await subscription.save();
        }

        // Update subscription status to active if it was pending
        if (subscription.status === "pending") {
          subscription.status = "active";
          await subscription.save();
        }
      } catch (error) {
        console.error(
          `Error processing subscription for ${
            subscription.foodChainId?.name || "Unknown"
          }:`,
          error
        );
        results.failed++;
      }
    }

    console.log("Monthly subscription processing completed with results:", {
      processed: results.processed,
      skipped: results.skipped,
      failed: results.failed,
      exempted: results.exempted,
      noOutlets: results.noOutlets,
    });

    return results;
  } catch (error) {
    console.error("Error processing monthly subscriptions:", error);
    throw error; // Re-throw to allow proper handling in the caller
  }
};

/**
 * Check for subscriptions with trial periods ending soon and send notifications
 */
export const checkTrialPeriods = async () => {
  try {
    console.log("Checking for trial periods ending soon...");

    // Get current date
    const now = new Date();

    // Get date 3 days from now
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

    // Find subscriptions with trial end dates approaching in the next 3 days
    const subscriptions = await Subscription.find({
      trialEndDate: {
        $gte: now,
        $lte: threeDaysFromNow,
      },
      status: { $in: ["active", "pending"] },
    })
      .populate("foodChainId")
      .populate("planId");

    console.log(
      `Found ${subscriptions.length} subscriptions with trial periods ending soon`
    );

    // Import notification service
    const { createTrialEndingNotification } = await import(
      "../services/notification-service.js"
    );

    // Send notifications for each subscription
    for (const subscription of subscriptions) {
      try {
        await createTrialEndingNotification(
          subscription,
          subscription.trialEndDate
        );
        console.log(
          `Sent trial ending notification for ${subscription.foodChainId.name}`
        );
      } catch (error) {
        console.error(
          `Error sending trial ending notification for ${subscription.foodChainId.name}:`,
          error
        );
      }
    }

    return {
      success: true,
      count: subscriptions.length,
    };
  } catch (error) {
    console.error("Error checking trial periods:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Setup cron jobs for subscription processing
 */
export const setupSubscriptionCronJobs = () => {
  // Create default plan on startup
  createDefaultSubscriptionPlan();

  // Schedule monthly billing - Run on the 1st day of each month at 1:00 AM
  cron.schedule("0 1 1 * *", () => {
    console.log(
      `[${new Date().toISOString()}] Running monthly subscription billing cron job`
    );
    processMonthlySubscriptions()
      .then((results) => {
        console.log(
          `[${new Date().toISOString()}] Monthly subscription billing cron job completed successfully`,
          results
        );
      })
      .catch((error) => {
        console.error(
          `[${new Date().toISOString()}] Error in monthly subscription billing cron job:`,
          error
        );
      });
  });

  // Schedule trial period check - Run daily at 9:00 AM
  cron.schedule("0 9 * * *", () => {
    console.log(
      `[${new Date().toISOString()}] Running trial period check cron job`
    );
    checkTrialPeriods()
      .then((results) => {
        console.log(
          `[${new Date().toISOString()}] Trial period check cron job completed successfully`,
          results
        );
      })
      .catch((error) => {
        console.error(
          `[${new Date().toISOString()}] Error in trial period check cron job:`,
          error
        );
      });
  });

  // Log when the cron jobs are scheduled
  console.log(
    `[${new Date().toISOString()}] Subscription cron jobs scheduled:
    - Monthly billing: 1st day of each month at 1:00 AM
    - Trial period check: Daily at 9:00 AM`
  );
};

/**
 * Manually trigger monthly subscription processing
 * @returns {Object} Results of the billing process
 */
export const triggerMonthlyBilling = async () => {
  try {
    const results = await processMonthlySubscriptions();
    return {
      success: true,
      message: "Monthly billing process completed successfully",
      data: results,
    };
  } catch (error) {
    console.error("Error triggering monthly billing:", error);
    return {
      success: false,
      message: "Error processing monthly billing",
      error: error.message || "Unknown error",
    };
  }
};

export default {
  setupSubscriptionCronJobs,
  createDefaultSubscriptionPlan,
  processMonthlySubscriptions,
  triggerMonthlyBilling,
  checkTrialPeriods,
};
