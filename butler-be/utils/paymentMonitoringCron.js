import cron from "node-cron";
import { checkPendingPayments } from "../services/payment-monitoring-service.js";

/**
 * Setup cron job for monitoring payment status
 */
export const setupPaymentMonitoringCronJob = () => {
  // Check pending payments every 10 minutes
  cron.schedule("*/10 * * * *", async () => {
    console.log(`[${new Date().toISOString()}] Running payment monitoring cron job`);
    try {
      const results = await checkPendingPayments();
      console.log(
        `[${new Date().toISOString()}] Payment monitoring cron job completed: ${results.length} payments checked, ${results.filter(r => r.updated).length} updated`
      );
    } catch (error) {
      console.error(
        `[${new Date().toISOString()}] Error in payment monitoring cron job:`,
        error
      );
    }
  });

  console.log(
    `[${new Date().toISOString()}] Payment monitoring cron job scheduled - Will run every 10 minutes`
  );
};

export default {
  setupPaymentMonitoringCronJob,
};
