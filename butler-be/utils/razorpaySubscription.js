import Razorpay from "razorpay";
import dotenv from "dotenv";
import crypto from "crypto";

dotenv.config();

// Initialize Razorpay with API keys from environment variables
const razorpay = new Razorpay({
  key_id: process.env.KEY_ID,
  key_secret: process.env.KEY_SECRET,
});

/**
 * Create a Razorpay payment link for a subscription invoice
 * @param {Object} invoice - The invoice object
 * @param {Object} foodChain - The food chain object
 * @returns {Object} - The created payment link object
 */
export const createSubscriptionPaymentLink = async (invoice, foodChain) => {
  try {
    const options = {
      amount: Math.round(invoice.amount * 100), // Amount in smallest currency unit (paise)
      currency: invoice.currency || "INR",
      accept_partial: false,
      description: `Subscription Invoice #${invoice.invoiceNumber}`,
      customer: {
        name: foodChain.name,
        contact: foodChain.phone || foodChain.contact,
        email: foodChain.email,
      },
      notify: {
        sms: true,
        email: true,
      },
      reminder_enable: true,
      notes: {
        invoiceId: invoice._id.toString(),
        foodChainId: foodChain._id.toString(),
        invoiceNumber: invoice.invoiceNumber,
      },
      callback_url: `${process.env.CORS_ORIGIN}/admin/subscription/payment-success`,
      callback_method: "get",
    };

    const paymentLink = await razorpay.paymentLink.create(options);
    return paymentLink;
  } catch (error) {
    console.error("Error creating subscription payment link:", error);
    throw error;
  }
};

/**
 * Get details of a payment link
 * @param {String} paymentLinkId - The Razorpay payment link ID
 * @returns {Object} - The payment link details
 */
export const getSubscriptionPaymentLinkDetails = async (paymentLinkId) => {
  try {
    const paymentLink = await razorpay.paymentLink.fetch(paymentLinkId);
    return paymentLink;
  } catch (error) {
    console.error("Error fetching payment link details:", error);
    throw error;
  }
};

/**
 * Verify payment signature
 * @param {String} razorpayPaymentId - The Razorpay payment ID
 * @param {String} razorpayOrderId - The Razorpay order ID
 * @param {String} signature - The signature received from Razorpay
 * @returns {Boolean} - Whether the signature is valid
 */
export const verifySubscriptionPaymentSignature = (
  razorpayPaymentId,
  razorpayOrderId,
  signature
) => {
  const generatedSignature = crypto
    .createHmac("sha256", process.env.KEY_SECRET)
    .update(`${razorpayOrderId}|${razorpayPaymentId}`)
    .digest("hex");

  return generatedSignature === signature;
};

export default {
  createSubscriptionPaymentLink,
  getSubscriptionPaymentLinkDetails,
  verifySubscriptionPaymentSignature,
};
