import Razorpay from "razorpay";
import dotenv from "dotenv";

dotenv.config();

// Initialize Razorpay with API keys from environment variables
const razorpay = new Razorpay({
  key_id: process.env.KEY_ID,
  key_secret: process.env.KEY_SECRET,
});

/**
 * Create a Razorpay account for a food chain (onboarding)
 * @param {Object} foodChain - The food chain object
 * @returns {Object} - The created Razorpay account
 */
export const createRazorpayAccount = async (foodChain) => {
  try {
    if (!foodChain.name || !foodChain.email || !foodChain.phone) {
      throw new Error("Food chain details are incomplete");
    }

    // Prepare account data
    const accountData = {
      email: foodChain.email,
      phone: foodChain.phone,
      type: "route",
      reference_id: foodChain._id.toString().substring(0, 20), // Limit to 20 chars as per Razorpay requirements
      legal_business_name: foodChain.name,
      business_type: foodChain.businessType || "individual", // Default to individual if not specified
      contact_name: foodChain.contactPerson || foodChain.name,
      profile: {
        category: "food",
        subcategory: foodChain.subcategory || "restaurant", // Use subcategory from foodChain or default to restaurant
        addresses: {
          registered: {
            street1: foodChain.address?.street || "",
            street2: foodChain.address?.street2 || "",
            city: foodChain.address?.city || "",
            state: foodChain.address?.state || "",
            postal_code: foodChain.address?.postalCode || "",
            country: "IN", // Default to India
          },
        },
      },
      // Add legal info if available
      // ...(foodChain.legalInfo && {
      //   legal_info: {
      //     pan: foodChain.legalInfo.pan || "",
      //     gst: foodChain.legalInfo.gst || "",
      //   },
      // }),
      // Bank account details will be added after product configuration
    };
    console.log(
      accountData.reference_id.length,
      "the account data =>",
      accountData,
      "\n",
      accountData.profile.addresses
    );
    // Create Razorpay account
    const account = await razorpay.accounts.create(accountData);
    console.log(
      `Created Razorpay account for food chain ${foodChain.name}:`,
      account.id
    );

    return account;
  } catch (error) {
    console.error("Error creating Razorpay account:", error);
    throw error;
  }
};

/**
 * Add a stakeholder to a Razorpay account
 * @param {String} accountId - The Razorpay account ID
 * @param {Object} stakeholderData - The stakeholder data
 * @returns {Object} - The created stakeholder
 */
export const addStakeholder = async (accountId, stakeholderData) => {
  try {
    if (!accountId) {
      throw new Error("Razorpay account ID is required");
    }

    // Create stakeholder
    const stakeholder = await razorpay.stakeholders.create(
      accountId,
      stakeholderData
    );
    console.log(
      `Added stakeholder to Razorpay account ${accountId}:`,
      stakeholder.id
    );

    return stakeholder;
  } catch (error) {
    console.error("Error adding stakeholder to Razorpay account:", error);
    throw error;
  }
};

/**
 * Request Route product configuration for a Razorpay account
 * @param {String} accountId - The Razorpay account ID
 * @returns {Object} - The product configuration response
 */
export const requestRouteConfiguration = async (accountId) => {
  try {
    if (!accountId) {
      throw new Error("Razorpay account ID is required");
    }

    // Request Route product configuration
    const productConfig = await razorpay.products.requestProductConfiguration(
      accountId,
      {
        product_name: "route",
        tnc_accepted: true,
      }
    );

    console.log(
      `Requested Route configuration for account ${accountId}:`,
      productConfig
    );
    return productConfig;
  } catch (error) {
    console.error("Error requesting Route configuration:", error);
    throw error;
  }
};

/**
 * Add bank account details to a Razorpay product
 * @param {String} accountId - The Razorpay account ID
 * @param {String} productId - The Razorpay product ID
 * @param {Object} bankDetails - The bank account details
 * @returns {Object} - The updated product configuration
 */
export const addBankAccountToProduct = async (
  accountId,
  productId,
  bankDetails
) => {
  try {
    if (!accountId || !productId) {
      throw new Error("Razorpay account ID and product ID are required");
    }

    if (
      !bankDetails ||
      !bankDetails.accountNumber ||
      !bankDetails.ifscCode ||
      !bankDetails.beneficiaryName
    ) {
      throw new Error("Bank account details are incomplete");
    }

    // Edit the product to add bank account details
    const updatedProduct = await razorpay.products.edit(accountId, productId, {
      settlements: {
        account_number: bankDetails.accountNumber,
        ifsc_code: bankDetails.ifscCode,
        beneficiary_name: bankDetails.beneficiaryName,
      },
      tnc_accepted: true,
    });

    console.log(
      `Added bank account details to product ${productId} for account ${accountId}:`,
      updatedProduct
    );
    return updatedProduct;
  } catch (error) {
    console.error("Error adding bank account details to product:", error);
    throw error;
  }
};

/**
 * Transfer funds from a payment to a food chain's Razorpay account
 * @param {String} paymentId - The Razorpay payment ID
 * @param {String} accountId - The destination Razorpay account ID
 * @param {Number} amount - The amount to transfer
 * @param {String} description - Description of the transfer
 * @returns {Object} - The transfer response
 */
export const transferFundsToFoodChain = async (
  paymentId,
  accountId,
  amount,
  description
) => {
  try {
    if (!paymentId || !accountId || !amount) {
      throw new Error("Payment ID, account ID, and amount are required");
    }

    // Create transfer data
    const transferData = {
      transfers: [
        {
          account: accountId,
          amount: Math.round(amount * 100), // Convert to paise
          currency: "INR",
          notes: {
            description: description || "Transfer from Butler",
            payment_id: paymentId,
          },
          linked_account_notes: ["description", "payment_id"],
          on_hold: false,
        },
      ],
    };

    // Create transfer
    const transfer = await razorpay.payments.transfer(paymentId, transferData);
    console.log(
      `Created transfer from payment ${paymentId} to account ${accountId}:`,
      transfer
    );

    return transfer;
  } catch (error) {
    console.error("Error transferring funds:", error);
    throw error;
  }
};

/**
 * Get transfer details
 * @param {String} transferId - The transfer ID
 * @returns {Object} - The transfer details
 */
export const getTransferDetails = async (transferId) => {
  try {
    if (!transferId) {
      throw new Error("Transfer ID is required");
    }

    // Get transfer details
    const transfer = await razorpay.transfers.fetch(transferId);
    return transfer;
  } catch (error) {
    console.error("Error fetching transfer details:", error);
    throw error;
  }
};

export default {
  createRazorpayAccount,
  addStakeholder,
  requestRouteConfiguration,
  addBankAccountToProduct,
  transferFundsToFoodChain,
  getTransferDetails,
};
