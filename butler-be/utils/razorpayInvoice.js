import Razorpay from "razorpay";
import dotenv from "dotenv";
import crypto from "crypto";

dotenv.config();

// Initialize Razorpay with API keys from environment variables
const razorpay = new Razorpay({
  key_id: process.env.KEY_ID,
  key_secret: process.env.KEY_SECRET,
});

/**
 * Create a Razorpay invoice for a subscription
 * @param {Object} invoice - The invoice object from our database
 * @param {Object} foodChain - The food chain object
 * @returns {Object} - The created Razorpay invoice object
 */
export const createSubscriptionInvoice = async (invoice, foodChain) => {
  try {
    // Format line items for Razorpay
    const lineItems = invoice.items.map((item) => ({
      name: item.description,
      amount: Math.round(item.amount * 100), // Amount in smallest currency unit (paise)
      currency: invoice.currency || "INR",
      quantity: item.quantity,
    }));

    // Create invoice options
    const options = {
      type: "invoice",
      description: `Subscription Invoice #${invoice.invoiceNumber}`,
      customer: {
        name: foodChain.name,
        contact: foodChain.phone || foodChain.contact,
        email: foodChain.email,
      },
      line_items: lineItems,
      sms_notify: 1,
      email_notify: 1,
      currency: invoice.currency || "INR",
      expire_by: Math.floor(new Date(invoice.dueDate).getTime() / 1000), // Unix timestamp
      notes: {
        invoiceId: invoice._id.toString(),
        foodChainId: foodChain._id.toString(),
        invoiceNumber: invoice.invoiceNumber,
      },
    };

    // Create the invoice in Razorpay
    const razorpayInvoice = await razorpay.invoices.create(options);
    return razorpayInvoice;
  } catch (error) {
    console.error("Error creating Razorpay invoice:", error);
    throw error;
  }
};

/**
 * Get details of a Razorpay invoice
 * @param {String} invoiceId - The Razorpay invoice ID
 * @returns {Object} - The invoice details
 */
export const getInvoiceDetails = async (invoiceId) => {
  try {
    const invoice = await razorpay.invoices.fetch(invoiceId);
    return invoice;
  } catch (error) {
    console.error("Error fetching Razorpay invoice:", error);
    throw error;
  }
};

/**
 * Verify payment signature for invoice payment
 * @param {String} razorpayPaymentId - The Razorpay payment ID
 * @param {String} razorpayOrderId - The Razorpay order ID
 * @param {String} signature - The signature received from Razorpay
 * @returns {Boolean} - Whether the signature is valid
 */
export const verifyInvoicePaymentSignature = (
  razorpayPaymentId,
  razorpayOrderId,
  signature
) => {
  const generatedSignature = crypto
    .createHmac("sha256", process.env.KEY_SECRET)
    .update(`${razorpayOrderId}|${razorpayPaymentId}`)
    .digest("hex");

  return generatedSignature === signature;
};

export default {
  createSubscriptionInvoice,
  getInvoiceDetails,
  verifyInvoicePaymentSignature,
};
