/**
 * Generate HTML template for order invoice email
 * @param {Object} order - The order object with populated items
 * @param {Object} customer - The customer object
 * @param {Object} outlet - The outlet object
 * @param {Object} foodChain - The food chain object
 * @returns {String} - HTML template for the invoice email
 */
export const generateOrderInvoiceTemplate = (order, customer, outlet, foodChain) => {
  // Format date
  const orderDate = new Date(order.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  // Generate items HTML
  const itemsHtml = order.items.map(item => `
    <tr>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">${item.dishId.name}</td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">₹${item.price.toFixed(2)}</td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">₹${(item.price * item.quantity).toFixed(2)}</td>
    </tr>
  `).join('');

  // Generate coupon HTML if applicable
  const couponHtml = order.couponCode ? `
    <tr>
      <td colspan="3" style="padding: 10px; text-align: right; font-weight: bold;">Coupon (${order.couponCode}):</td>
      <td style="padding: 10px; text-align: right; color: #e53e3e;">-₹${order.couponDiscount.toFixed(2)}</td>
    </tr>
  ` : '';

  return `
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333; margin-bottom: 5px;">${foodChain.name}</h1>
        <p style="color: #666; margin-top: 0;">${outlet.name} - ${outlet.address}</p>
      </div>
      
      <div style="margin-bottom: 30px;">
        <h2 style="color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">Order Invoice</h2>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 5px;"><strong>Order Number:</strong></td>
            <td style="padding: 5px;">${order.orderNumber}</td>
            <td style="padding: 5px; text-align: right;"><strong>Date:</strong></td>
            <td style="padding: 5px; text-align: right;">${orderDate}</td>
          </tr>
          <tr>
            <td style="padding: 5px;"><strong>Customer:</strong></td>
            <td style="padding: 5px;">${customer.name}</td>
            <td style="padding: 5px; text-align: right;"><strong>Payment Method:</strong></td>
            <td style="padding: 5px; text-align: right;">${order.paymentMethod === 'cash' ? 'Cash' : 'Online'}</td>
          </tr>
          <tr>
            <td style="padding: 5px;"><strong>Email:</strong></td>
            <td style="padding: 5px;">${customer.email}</td>
            <td style="padding: 5px; text-align: right;"><strong>Status:</strong></td>
            <td style="padding: 5px; text-align: right;">${order.status.charAt(0).toUpperCase() + order.status.slice(1)}</td>
          </tr>
          ${customer.phone ? `
          <tr>
            <td style="padding: 5px;"><strong>Phone:</strong></td>
            <td style="padding: 5px;">${customer.phone}</td>
            <td style="padding: 5px;"></td>
            <td style="padding: 5px;"></td>
          </tr>
          ` : ''}
        </table>
      </div>
      
      <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">Order Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background-color: #f8f9fa;">
              <th style="padding: 10px; text-align: left; border-bottom: 2px solid #eee;">Item</th>
              <th style="padding: 10px; text-align: center; border-bottom: 2px solid #eee;">Quantity</th>
              <th style="padding: 10px; text-align: right; border-bottom: 2px solid #eee;">Price</th>
              <th style="padding: 10px; text-align: right; border-bottom: 2px solid #eee;">Total</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHtml}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="3" style="padding: 10px; text-align: right; font-weight: bold;">Subtotal:</td>
              <td style="padding: 10px; text-align: right;">₹${order.totalAmount.toFixed(2)}</td>
            </tr>
            ${couponHtml}
            <tr>
              <td colspan="3" style="padding: 10px; text-align: right; font-weight: bold; font-size: 1.1em;">Total:</td>
              <td style="padding: 10px; text-align: right; font-weight: bold; font-size: 1.1em;">₹${order.finalAmount ? order.finalAmount.toFixed(2) : order.totalAmount.toFixed(2)}</td>
            </tr>
          </tfoot>
        </table>
      </div>
      
      ${order.specialInstructions ? `
      <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">Special Instructions</h3>
        <p style="padding: 10px; background-color: #f8f9fa; border-radius: 5px;">${order.specialInstructions}</p>
      </div>
      ` : ''}
      
      <div style="margin-top: 40px; text-align: center; color: #666; font-size: 0.9em;">
        <p>Thank you for your order!</p>
        <p>For any questions or concerns, please contact us at ${foodChain.email || outlet.email || ''}</p>
        <p>&copy; ${new Date().getFullYear()} ${foodChain.name}. All rights reserved.</p>
      </div>
    </div>
  `;
};

export default generateOrderInvoiceTemplate;
