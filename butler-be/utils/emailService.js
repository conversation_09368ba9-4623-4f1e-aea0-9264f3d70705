import nodemailer from "nodemailer";
import dotenv from "dotenv";

dotenv.config();

// Email configuration is loaded from environment variables

// Create a transporter using environment variables
console.log(
  "process.env.EMAIL_USER",
  process.env.EMAIL_USER,
  process.env.EMAIL_PASSWORD
);
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

// Verify the transporter connection
transporter.verify((error) => {
  if (error) {
    console.error("Error connecting to email server:", error);
  } else {
    console.log("Email server connection established");
  }
});

/**
 * Send a welcome email to a new customer
 * @param {Object} customer - Customer details
 * @param {string} customer.email - Customer email
 * @param {string} customer.name - Customer name
 * @param {string} password - Temporary password
 * @param {string} foodChainName - Name of the food chain
 */
export const sendCustomerWelcomeEmail = async (
  customer,
  password,
  foodChainName
) => {
  try {
    const mailOptions = {
      from: `"${foodChainName}" <${process.env.EMAIL_USER}>`,
      to: customer.email,
      subject: `Welcome to ${foodChainName}!`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Welcome to ${foodChainName}!</h2>
          <p>Dear ${customer.name},</p>
          <p>Thank you for choosing ${foodChainName}. An account has been created for you by our staff.</p>
          <p>You can log in to your account using the following credentials:</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>Email:</strong> ${customer.email}</p>
            <p><strong>Temporary Password:</strong> ${password}</p>
          </div>
          <p>For security reasons, we recommend changing your password after your first login.</p>
          <p>Thank you,<br>${foodChainName} Team</p>
        </div>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Customer welcome email sent:", info.messageId);
    return true;
  } catch (error) {
    console.error("Error sending customer welcome email:", error);
    return false;
  }
};

/**
 * Send a welcome email to a new employee
 * @param {Object} employee - Employee details
 * @param {string} employee.email - Employee email
 * @param {string} employee.name - Employee name
 * @param {string} employee.role - Employee role
 * @param {string} password - Temporary password
 * @param {string} foodChainName - Name of the food chain
 */
export const sendEmployeeWelcomeEmail = async (
  employee,
  password,
  foodChainName
) => {
  try {
    const mailOptions = {
      from: `"${foodChainName}" <${process.env.EMAIL_USER}>`,
      to: employee.email,
      subject: `Welcome to ${foodChainName} Team!`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Welcome to ${foodChainName} Team!</h2>
          <p>Dear ${employee.name},</p>
          <p>You have been added to the ${foodChainName} team as a <strong>${employee.role}</strong>.</p>
          <p>You can log in to the admin dashboard using the following credentials:</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>Email:</strong> ${employee.email}</p>
            <p><strong>Temporary Password:</strong> ${password}</p>
          </div>
          <p>For security reasons, we recommend changing your password after your first login.</p>
          <p>Thank you,<br>${foodChainName} Management</p>
        </div>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Employee welcome email sent:", info.messageId);
    return true;
  } catch (error) {
    console.error("Error sending employee welcome email:", error);
    return false;
  }
};

/**
 * Send a password reset email
 * @param {Object} user - User details
 * @param {string} user.email - User email
 * @param {string} user.name - User name
 * @param {string} resetUrl - Password reset URL
 * @param {string} role - User role (user, admin, etc.)
 */
export const sendPasswordResetEmail = async (user, resetUrl, role) => {
  try {
    const roleDisplay = role === "user" ? "Customer" : "Staff";

    const mailOptions = {
      from: `"Butler App" <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: "Password Reset Instructions",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Password Reset Request</h2>
          <p>Dear ${user.name},</p>
          <p>We received a request to reset your password for your ${roleDisplay} account.</p>
          <p>To reset your password, please click the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
          </div>
          <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
          <p>This link will expire in 1 hour for security reasons.</p>
          <p>Thank you,<br>Butler App Support Team</p>
        </div>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Password reset email sent:", info.messageId);
    return true;
  } catch (error) {
    console.error("Error sending password reset email:", error);
    return false;
  }
};

/**
 * Send a password reset confirmation email
 * @param {Object} user - User details
 * @param {string} user.email - User email
 * @param {string} user.name - User name
 */
export const sendPasswordResetConfirmationEmail = async (user) => {
  try {
    const mailOptions = {
      from: `"Butler App" <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: "Your Password Has Been Reset",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Password Reset Successful</h2>
          <p>Dear ${user.name},</p>
          <p>Your password has been successfully reset.</p>
          <p>If you did not perform this action, please contact our support team immediately.</p>
          <p>Thank you,<br>Butler App Support Team</p>
        </div>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Password reset confirmation email sent:", info.messageId);
    return true;
  } catch (error) {
    console.error("Error sending password reset confirmation email:", error);
    return false;
  }
};

/**
 * Send a test email to a specific email address
 * @param {string} testEmail - Email address to send the test to
 */
export const sendTestEmail = async (
  testEmail = "<EMAIL>"
) => {
  try {
    const mailOptions = {
      from: `"Butler App" <${process.env.EMAIL_USER}>`,
      to: testEmail,
      subject: "Butler App Email Test",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Email Test Successful</h2>
          <p>This is a test email from the Butler App.</p>
          <p>If you're receiving this email, it means the email service is configured correctly.</p>
          <p>Email configuration:</p>
          <ul>
            <li>Host: ${process.env.EMAIL_HOST}</li>
            <li>Port: ${process.env.EMAIL_PORT}</li>
            <li>Secure: ${process.env.EMAIL_SECURE}</li>
            <li>User: ${process.env.EMAIL_USER}</li>
          </ul>
          <p>Thank you,<br>Butler App Support Team</p>
        </div>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Test email sent:", info.messageId);
    return {
      success: true,
      messageId: info.messageId,
    };
  } catch (error) {
    console.error("Error sending test email:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Send a generic email
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} options.html - Email HTML content
 * @param {string} options.campaignId - Optional campaign ID for tracking
 * @param {string} options.recipientId - Optional recipient ID for tracking
 */
export const sendEmail = async (options) => {
  try {
    const { to, subject, html, campaignId, recipientId } = options;

    // Add tracking pixel for campaigns if IDs are provided
    let trackingHtml = html;
    if (campaignId && recipientId) {
      const trackingPixel = `<img src="${process.env.FRONTEND_URL}/api/v1/track/open/${campaignId}/${recipientId}" width="1" height="1" alt="" style="display:none;" />`;
      trackingHtml = html + trackingPixel;
    }

    const mailOptions = {
      from: `"Butler App" <${process.env.EMAIL_USER}>`,
      to,
      subject,
      html: trackingHtml,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Email sent:", info.messageId);
    return true;
  } catch (error) {
    console.error("Error sending email:", error);
    return false;
  }
};

/**
 * Send an order invoice email to a customer
 * @param {Object} order - The order object with populated items
 * @param {Object} customer - The customer object
 * @param {Object} outlet - The outlet object
 * @param {Object} foodChain - The food chain object
 * @returns {Boolean} - Whether the email was sent successfully
 */
export const sendOrderInvoiceEmail = async (
  order,
  customer,
  outlet,
  foodChain
) => {
  try {
    // Import the invoice template generator
    const { generateOrderInvoiceTemplate } = await import(
      "./emailTemplates/orderInvoice.js"
    );

    // Generate the HTML content
    const html = generateOrderInvoiceTemplate(
      order,
      customer,
      outlet,
      foodChain
    );

    const mailOptions = {
      from: `"${foodChain.name}" <${process.env.EMAIL_USER}>`,
      to: customer.email,
      subject: `Your Order Invoice #${order.orderNumber} from ${foodChain.name}`,
      html,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Order invoice email sent:", info.messageId);
    return true;
  } catch (error) {
    console.error("Error sending order invoice email:", error);
    return false;
  }
};

export default {
  sendCustomerWelcomeEmail,
  sendEmployeeWelcomeEmail,
  sendPasswordResetEmail,
  sendPasswordResetConfirmationEmail,
  sendTestEmail,
  sendEmail,
  sendOrderInvoiceEmail,
};
