import cron from "node-cron";
import { processWebhookRetries } from "../services/webhook-retry-service.js";

/**
 * Setup cron job for processing webhook retries
 */
export const setupWebhookRetryCronJob = () => {
  // Process webhook retries every 5 minutes
  cron.schedule("*/5 * * * *", async () => {
    console.log(`[${new Date().toISOString()}] Running webhook retry cron job`);
    try {
      const results = await processWebhookRetries();
      console.log(
        `[${new Date().toISOString()}] Webhook retry cron job completed: ${results.length} retries processed`
      );
    } catch (error) {
      console.error(
        `[${new Date().toISOString()}] Error in webhook retry cron job:`,
        error
      );
    }
  });

  console.log(
    `[${new Date().toISOString()}] Webhook retry cron job scheduled - Will run every 5 minutes`
  );
};

export default {
  setupWebhookRetryCronJob,
};
