# Groq Migration Guide

This guide explains how to migrate the Butler application from Hugging Face to Groq for user conversations.

## Overview

The Butler application has been updated to use Groq's LLM API instead of Hugging Face for user conversations. This change provides better performance and more advanced language capabilities.

## Changes Made

1. Added Groq SDK to the project
2. Created a new service module for Groq integration (`services/groq-service.js`)
3. Updated the user controller to use Groq instead of Hugging Face
4. Kept Hugging Face for embeddings since Groq doesn't have an embeddings API yet

## Environment Variables

You need to add a new environment variable for Groq:

```
GROQ_API_KEY=your_groq_api_key
```

You can get a Groq API key by signing up at [https://console.groq.com/](https://console.groq.com/).

## How to Update

1. Install the Groq SDK:
   ```
   npm install groq-sdk
   ```

2. Add the `GROQ_API_KEY` to your `.env` file.

3. Restart the server:
   ```
   npm run dev
   ```

## Testing

After updating, test the user conversation feature to ensure it's working correctly. The user interface should work the same as before, but the responses will now come from Groq's LLM.

## Troubleshooting

If you encounter any issues:

1. Check that the `GROQ_API_KEY` is correctly set in your `.env` file
2. Ensure the Groq SDK is installed
3. Check the server logs for any error messages
4. If needed, you can temporarily revert to Hugging Face by uncommenting the Hugging Face code in `user-controller.js`

## Future Improvements

In the future, when Groq adds an embeddings API, we can update the `vector-service.js` file to use Groq for embeddings as well.
