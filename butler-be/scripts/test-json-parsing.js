import { config } from "dotenv";

// Load environment variables
config();

console.log("🧪 Testing JSON parsing fixes...");

function testJSONParsing() {
  console.log("\n📝 Testing enhanced JSON parsing logic...");
  
  // Test cases that simulate the actual Groq responses
  const testCases = [
    {
      name: "Complete JSON with markdown",
      response: `Here is the response:

\`\`\`json
{
  "keywords": ["White Sause Pasta", "Pasta", "Italian"],
  "aiMessage": "मैं आपके लिए मेन्यू से बेहतरीन विकल्प सुझा सकता हूँ.",
  "faqSuggestions": ["क्या आपने अभी तक कुछ ऑर्डर किया है?"],
  "cartIntent": {
    "detected": true,
    "operation": "add",
    "item": "White Sause Pasta",
    "quantity": 1
  },
  "detectedLanguage": "hi-en"
}
\`\`\``,
      shouldParse: true
    },
    {
      name: "Incomplete JSON (cut off)",
      response: `Here is the response:

\`\`\`
{
  "keywords": ["White Sause Pasta", "Pasta", "Italian"],
  "aiMessage": "मैं आपके लिए मेन्यू से बेहतरीन विकल्प सुझा सकता हूँ. आपने व्हाइट सॉस पास्ता जोड़ा है. अब आप कुछ और ट्राय करना चाहते हैं? मैं आपके लिए रोस्टेड एगप्लेंट कैपोनाटा सुझा सकता हूँ.",
  "faqSuggestions": ["क्या आपने अभी तक कुछ ऑर्डर किया है?", "मेरे पास क्या ऑप्शंस हैं?"],
  "cartIntent": {
    "detected": true`,
      shouldParse: false // Will be fixed by our logic
    },
    {
      name: "JSON without markdown",
      response: `{
  "keywords": ["pasta", "italian"],
  "aiMessage": "I recommend the pasta",
  "faqSuggestions": ["What else?"],
  "cartIntent": {"detected": false},
  "detectedLanguage": "en"
}`,
      shouldParse: true
    },
    {
      name: "JSON with prefix text",
      response: `Based on your request, here's my response:

{
  "keywords": ["spicy", "food"],
  "aiMessage": "Try our spicy dishes!",
  "faqSuggestions": ["What's spicy?"],
  "cartIntent": {"detected": false},
  "detectedLanguage": "en"
}

Hope this helps!`,
      shouldParse: true
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n  Test ${index + 1}: ${testCase.name}`);
    console.log(`  Expected to parse: ${testCase.shouldParse}`);
    
    try {
      // Simulate our enhanced JSON parsing logic
      let responseText = testCase.response
        .replace(/^.*?Here is the response:\s*/i, "") // Remove "Here is the response:" prefix
        .replace(/^.*?```json\s*/i, "") // Remove everything before ```json
        .replace(/^.*?```\s*/i, "") // Remove everything before ```
        .replace(/```.*$/s, "") // Remove everything after ```
        .trim();

      // Look for the pattern that starts with { and ends with }
      const jsonRegex = /(\{[\s\S]*\})/;
      const jsonMatch = responseText.match(jsonRegex);

      if (!jsonMatch) {
        console.log(`    ❌ No JSON structure found`);
        return;
      }

      // Clean the JSON string
      let cleanJson = jsonMatch[0];

      // Remove any text before the first { and after the last }
      const firstBrace = cleanJson.indexOf("{");
      const lastBrace = cleanJson.lastIndexOf("}");
      if (firstBrace !== -1 && lastBrace !== -1) {
        cleanJson = cleanJson.substring(firstBrace, lastBrace + 1);
      }

      // Further clean the JSON
      cleanJson = cleanJson
        .replace(/[\r\n]/g, " ") // Remove newlines
        .replace(/\s+/g, " ") // Normalize spaces
        .replace(/,\s*}/g, "}") // Remove trailing commas
        .replace(/,\s*]/g, "]") // Remove trailing commas in arrays
        .replace(/"\s*$/, '"') // Fix incomplete quotes at end
        .trim(); // Remove leading/trailing spaces

      // Check if JSON is incomplete and try to fix it
      if (!cleanJson.endsWith("}")) {
        console.log(`    🔧 Fixing incomplete JSON...`);
        
        // Check for unterminated strings
        const quotes = (cleanJson.match(/"/g) || []).length;
        if (quotes % 2 !== 0) {
          // Odd number of quotes means unterminated string
          cleanJson += '"';
        }

        // Try to find the last complete property and close the JSON
        const lastCommaIndex = cleanJson.lastIndexOf(",");
        const lastColonIndex = cleanJson.lastIndexOf(":");
        const lastQuoteIndex = cleanJson.lastIndexOf('"');

        if (lastColonIndex > lastCommaIndex && lastQuoteIndex < lastColonIndex) {
          // Incomplete property value after colon, remove the incomplete property
          const propertyStart = cleanJson.lastIndexOf('"', lastColonIndex - 1);
          if (propertyStart > 0) {
            const beforeProperty = cleanJson.substring(0, propertyStart - 1);
            cleanJson = beforeProperty.endsWith(",")
              ? beforeProperty.slice(0, -1) + "}"
              : beforeProperty + "}";
          }
        } else if (lastQuoteIndex > lastCommaIndex && lastQuoteIndex > lastColonIndex) {
          // Incomplete property value, remove it and close JSON
          const propertyStart = cleanJson.lastIndexOf('"', lastQuoteIndex - 1);
          if (propertyStart > 0) {
            const beforeProperty = cleanJson.substring(0, propertyStart - 1);
            cleanJson = beforeProperty.endsWith(",")
              ? beforeProperty.slice(0, -1) + "}"
              : beforeProperty + "}";
          }
        } else {
          // Just close the JSON
          cleanJson += "}";
        }
      }

      const parsed = JSON.parse(cleanJson);
      console.log(`    ✅ Successfully parsed!`);
      console.log(`    📋 Keywords: [${parsed.keywords?.join(', ') || 'none'}]`);
      console.log(`    💬 AI Message: "${parsed.aiMessage?.substring(0, 50) || 'none'}..."`);
      console.log(`    🛒 Cart Intent: ${parsed.cartIntent?.detected || false}`);
      
    } catch (error) {
      console.log(`    ❌ Failed to parse: ${error.message}`);
    }
  });
}

function testKeywordTranslation() {
  console.log("\n🌍 Testing keyword translation...");
  
  const hindiToEnglish = {
    'व्हाइट सॉस पास्ता': 'white sauce pasta',
    'कार्ट': 'cart',
    'पास्ता': 'pasta',
    'पिज्जा': 'pizza',
    'बर्गर': 'burger',
    'चिकन': 'chicken',
    'वेज': 'vegetarian',
    'नॉन-वेज': 'non-vegetarian',
    'मसालेदार': 'spicy',
    'मिठाई': 'sweet',
    'खाना': 'food',
    'भोजन': 'meal',
    'नाश्ता': 'breakfast',
    'दोपहर का खाना': 'lunch',
    'रात का खाना': 'dinner',
    'स्नैक्स': 'snacks'
  };
  
  const translateKeywordsToEnglish = (keywords) => {
    return keywords.map(keyword => {
      const lowerKeyword = keyword.toLowerCase();
      // Check if it's Hindi and translate
      for (const [hindi, english] of Object.entries(hindiToEnglish)) {
        if (lowerKeyword.includes(hindi.toLowerCase())) {
          return english;
        }
      }
      // If not Hindi or no translation found, return as is (assuming it's English)
      return keyword.toLowerCase();
    });
  };
  
  const testKeywords = [
    ["White Sause Pasta", "Pasta", "Italian"],
    ["व्हाइट सॉस पास्ता", "कार्ट"],
    ["pizza", "burger", "पिज्जा"],
    ["मसालेदार", "खाना", "spicy"]
  ];
  
  testKeywords.forEach((keywords, index) => {
    const translated = translateKeywordsToEnglish(keywords);
    console.log(`  Test ${index + 1}:`);
    console.log(`    Input: [${keywords.join(', ')}]`);
    console.log(`    Output: [${translated.join(', ')}]`);
    
    // Verify all outputs are in English
    const allEnglish = translated.every(keyword => 
      /^[a-zA-Z\s]+$/.test(keyword) // Only English letters and spaces
    );
    console.log(`    ✅ All English: ${allEnglish}`);
  });
}

async function runTests() {
  try {
    testJSONParsing();
    testKeywordTranslation();
    
    console.log("\n" + "=".repeat(60));
    console.log("🎉 JSON PARSING TESTS COMPLETED!");
    console.log("=".repeat(60));
    
    console.log("\n✅ Enhanced Features:");
    console.log("• Better markdown removal");
    console.log("• Improved incomplete JSON handling");
    console.log("• Robust keyword translation");
    console.log("• Increased token limits (300 tokens)");
    console.log("• Enhanced error recovery");
    
    console.log("\n🚀 Expected Results:");
    console.log("• Complete JSON responses from Groq");
    console.log("• Proper handling of cut-off responses");
    console.log("• Keywords always in English");
    console.log("• AI messages in selected language");
    
  } catch (error) {
    console.error("\n❌ Test suite failed:", error);
  } finally {
    console.log("\n👋 Testing completed.");
    process.exit(0);
  }
}

// Run all tests
runTests();
