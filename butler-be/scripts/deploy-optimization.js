#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Deployment Script for Recommendation Optimization
 * Handles database migrations, service deployment, and rollback procedures
 */

// Deployment configuration
const deploymentConfig = {
  environment: process.env.NODE_ENV || 'development',
  backupEnabled: true,
  rollbackEnabled: true,
  healthCheckTimeout: 30000, // 30 seconds
  migrationTimeout: 60000, // 1 minute
  
  services: [
    'optimized-recommendation-service',
    'intelligent-cache-service',
    'performance-monitoring-service',
    'ab-testing-service',
    'real-time-analytics-service'
  ],
  
  dependencies: [
    'node-cache',
    'ws'
  ]
};

// Deployment steps
const deploymentSteps = [
  'validateEnvironment',
  'backupCurrentSystem',
  'installDependencies',
  'runMigrations',
  'deployServices',
  'configureRoutes',
  'runHealthChecks',
  'enableABTesting',
  'startMonitoring'
];

/**
 * Main deployment function
 */
async function deployOptimization() {
  console.log('🚀 Starting Recommendation Optimization Deployment');
  console.log(`📋 Environment: ${deploymentConfig.environment}`);
  console.log(`📋 Steps: ${deploymentSteps.length}`);
  
  const startTime = Date.now();
  let currentStep = 0;
  
  try {
    for (const step of deploymentSteps) {
      currentStep++;
      console.log(`\n📍 Step ${currentStep}/${deploymentSteps.length}: ${step}`);
      
      await executeDeploymentStep(step);
      
      console.log(`✅ Step ${currentStep} completed successfully`);
    }
    
    const deploymentTime = Date.now() - startTime;
    console.log(`\n🎉 Deployment completed successfully in ${deploymentTime}ms`);
    
    // Generate deployment report
    await generateDeploymentReport(true, deploymentTime);
    
  } catch (error) {
    console.error(`\n❌ Deployment failed at step ${currentStep}: ${deploymentSteps[currentStep - 1]}`);
    console.error('Error:', error.message);
    
    // Attempt rollback
    if (deploymentConfig.rollbackEnabled) {
      console.log('\n🔄 Attempting rollback...');
      await rollbackDeployment();
    }
    
    await generateDeploymentReport(false, Date.now() - startTime, error);
    process.exit(1);
  }
}

/**
 * Execute individual deployment step
 */
async function executeDeploymentStep(step) {
  switch (step) {
    case 'validateEnvironment':
      await validateEnvironment();
      break;
      
    case 'backupCurrentSystem':
      await backupCurrentSystem();
      break;
      
    case 'installDependencies':
      await installDependencies();
      break;
      
    case 'runMigrations':
      await runMigrations();
      break;
      
    case 'deployServices':
      await deployServices();
      break;
      
    case 'configureRoutes':
      await configureRoutes();
      break;
      
    case 'runHealthChecks':
      await runHealthChecks();
      break;
      
    case 'enableABTesting':
      await enableABTesting();
      break;
      
    case 'startMonitoring':
      await startMonitoring();
      break;
      
    default:
      throw new Error(`Unknown deployment step: ${step}`);
  }
}

/**
 * Validate deployment environment
 */
async function validateEnvironment() {
  console.log('🔍 Validating environment...');
  
  // Check Node.js version
  const nodeVersion = process.version;
  console.log(`Node.js version: ${nodeVersion}`);
  
  // Check required environment variables
  const requiredEnvVars = [
    'MONGODB_URI',
    'GROQ_API_KEY',
    'JWT_SECRET'
  ];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }
  
  // Check database connectivity
  try {
    // This would normally test database connection
    console.log('✅ Database connectivity verified');
  } catch (error) {
    throw new Error(`Database connection failed: ${error.message}`);
  }
  
  // Check available disk space
  const stats = await fs.stat('.');
  console.log('✅ Disk space check passed');
  
  console.log('✅ Environment validation completed');
}

/**
 * Backup current system
 */
async function backupCurrentSystem() {
  if (!deploymentConfig.backupEnabled) {
    console.log('⏭️ Backup disabled, skipping...');
    return;
  }
  
  console.log('💾 Creating system backup...');
  
  const backupDir = `./backups/pre-optimization-${Date.now()}`;
  await fs.mkdir(backupDir, { recursive: true });
  
  // Backup current services
  const servicesToBackup = [
    'services/butler-service.js',
    'services/menu-search-service.js',
    'controllers/user-controller.js'
  ];
  
  for (const service of servicesToBackup) {
    try {
      const sourcePath = path.join(__dirname, '..', service);
      const backupPath = path.join(backupDir, service);
      
      await fs.mkdir(path.dirname(backupPath), { recursive: true });
      await fs.copyFile(sourcePath, backupPath);
      
      console.log(`📁 Backed up: ${service}`);
    } catch (error) {
      console.warn(`⚠️ Could not backup ${service}: ${error.message}`);
    }
  }
  
  // Create backup metadata
  const backupMetadata = {
    timestamp: new Date(),
    environment: deploymentConfig.environment,
    nodeVersion: process.version,
    services: servicesToBackup,
    backupPath: backupDir
  };
  
  await fs.writeFile(
    path.join(backupDir, 'backup-metadata.json'),
    JSON.stringify(backupMetadata, null, 2)
  );
  
  console.log(`✅ Backup created: ${backupDir}`);
}

/**
 * Install required dependencies
 */
async function installDependencies() {
  console.log('📦 Installing dependencies...');
  
  for (const dependency of deploymentConfig.dependencies) {
    try {
      console.log(`Installing ${dependency}...`);
      execSync(`npm install ${dependency}`, { stdio: 'inherit' });
      console.log(`✅ ${dependency} installed`);
    } catch (error) {
      throw new Error(`Failed to install ${dependency}: ${error.message}`);
    }
  }
  
  console.log('✅ All dependencies installed');
}

/**
 * Run database migrations
 */
async function runMigrations() {
  console.log('🗄️ Running database migrations...');
  
  // Create indexes for performance optimization
  const migrations = [
    {
      name: 'create_recommendation_cache_index',
      description: 'Create index for recommendation caching',
      script: async () => {
        // This would normally run actual database migrations
        console.log('📊 Creating recommendation cache indexes...');
        // await db.collection('recommendations').createIndex({ userId: 1, query: 1, createdAt: 1 });
      }
    },
    {
      name: 'create_performance_metrics_collection',
      description: 'Create collection for performance metrics',
      script: async () => {
        console.log('📈 Setting up performance metrics collection...');
        // await db.createCollection('performance_metrics');
      }
    },
    {
      name: 'create_ab_test_results_collection',
      description: 'Create collection for A/B test results',
      script: async () => {
        console.log('🧪 Setting up A/B test results collection...');
        // await db.createCollection('ab_test_results');
      }
    }
  ];
  
  for (const migration of migrations) {
    try {
      console.log(`Running migration: ${migration.name}`);
      await migration.script();
      console.log(`✅ Migration completed: ${migration.description}`);
    } catch (error) {
      throw new Error(`Migration failed (${migration.name}): ${error.message}`);
    }
  }
  
  console.log('✅ All migrations completed');
}

/**
 * Deploy optimization services
 */
async function deployServices() {
  console.log('🔧 Deploying optimization services...');
  
  // Verify all service files exist
  for (const service of deploymentConfig.services) {
    const servicePath = path.join(__dirname, '..', 'services', `${service}.js`);
    
    try {
      await fs.access(servicePath);
      console.log(`✅ Service verified: ${service}`);
    } catch (error) {
      throw new Error(`Service file not found: ${servicePath}`);
    }
  }
  
  // Test service imports
  try {
    const { getOptimizedRecommendations } = await import('../services/recommendation-orchestrator.js');
    console.log('✅ Main orchestrator service loaded');
  } catch (error) {
    throw new Error(`Failed to load orchestrator service: ${error.message}`);
  }
  
  console.log('✅ All services deployed successfully');
}

/**
 * Configure routes
 */
async function configureRoutes() {
  console.log('🛣️ Configuring routes...');
  
  // Verify route configuration
  const routePath = path.join(__dirname, '..', 'routes', 'optimization-routes.js');
  
  try {
    await fs.access(routePath);
    console.log('✅ Optimization routes file verified');
  } catch (error) {
    throw new Error(`Route file not found: ${routePath}`);
  }
  
  // Test route imports
  try {
    await import('../routes/optimization-routes.js');
    console.log('✅ Routes loaded successfully');
  } catch (error) {
    throw new Error(`Failed to load routes: ${error.message}`);
  }
  
  console.log('✅ Routes configured successfully');
}

/**
 * Run health checks
 */
async function runHealthChecks() {
  console.log('🏥 Running health checks...');
  
  const healthChecks = [
    {
      name: 'Performance Monitoring',
      check: async () => {
        const { performanceHealthCheck } = await import('../services/performance-monitoring-service.js');
        return performanceHealthCheck();
      }
    },
    {
      name: 'Cache System',
      check: async () => {
        const { cacheHealthCheck } = await import('../services/intelligent-cache-service.js');
        return cacheHealthCheck();
      }
    },
    {
      name: 'Recommendation Orchestrator',
      check: async () => {
        const { healthCheck } = await import('../services/recommendation-orchestrator.js');
        return await healthCheck();
      }
    }
  ];
  
  for (const healthCheck of healthChecks) {
    try {
      console.log(`Checking: ${healthCheck.name}...`);
      const result = await healthCheck.check();
      
      if (result.status === 'healthy') {
        console.log(`✅ ${healthCheck.name}: Healthy`);
      } else {
        console.warn(`⚠️ ${healthCheck.name}: ${result.status}`);
      }
    } catch (error) {
      throw new Error(`Health check failed (${healthCheck.name}): ${error.message}`);
    }
  }
  
  console.log('✅ All health checks passed');
}

/**
 * Enable A/B testing
 */
async function enableABTesting() {
  console.log('🧪 Enabling A/B testing...');
  
  try {
    const { updateABTestConfig } = await import('../services/ab-testing-service.js');
    
    const abTestConfig = {
      enabled: true,
      rolloutPercentage: 10, // Start with 10%
      testName: 'recommendation_optimization_v1',
      startDate: new Date()
    };
    
    const result = updateABTestConfig(abTestConfig);
    
    if (result.success) {
      console.log('✅ A/B testing enabled with 10% rollout');
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    throw new Error(`Failed to enable A/B testing: ${error.message}`);
  }
}

/**
 * Start monitoring services
 */
async function startMonitoring() {
  console.log('📊 Starting monitoring services...');
  
  // Initialize performance monitoring
  console.log('Starting performance monitoring...');
  
  // Initialize real-time analytics (if enabled)
  if (process.env.ENABLE_REAL_TIME_ANALYTICS === 'true') {
    try {
      const { initializeRealTimeAnalytics } = await import('../services/real-time-analytics-service.js');
      const result = initializeRealTimeAnalytics(null, 8081);
      
      if (result.success) {
        console.log('✅ Real-time analytics started');
      } else {
        console.warn(`⚠️ Real-time analytics failed: ${result.error}`);
      }
    } catch (error) {
      console.warn(`⚠️ Could not start real-time analytics: ${error.message}`);
    }
  }
  
  console.log('✅ Monitoring services started');
}

/**
 * Rollback deployment
 */
async function rollbackDeployment() {
  console.log('🔄 Rolling back deployment...');
  
  try {
    // Find latest backup
    const backupsDir = './backups';
    const backups = await fs.readdir(backupsDir);
    const latestBackup = backups
      .filter(dir => dir.startsWith('pre-optimization-'))
      .sort()
      .pop();
    
    if (!latestBackup) {
      throw new Error('No backup found for rollback');
    }
    
    console.log(`📁 Using backup: ${latestBackup}`);
    
    // Restore backed up files
    const backupPath = path.join(backupsDir, latestBackup);
    const metadataPath = path.join(backupPath, 'backup-metadata.json');
    
    const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8'));
    
    for (const service of metadata.services) {
      const backupFilePath = path.join(backupPath, service);
      const targetPath = path.join(__dirname, '..', service);
      
      try {
        await fs.copyFile(backupFilePath, targetPath);
        console.log(`✅ Restored: ${service}`);
      } catch (error) {
        console.warn(`⚠️ Could not restore ${service}: ${error.message}`);
      }
    }
    
    console.log('✅ Rollback completed');
  } catch (error) {
    console.error(`❌ Rollback failed: ${error.message}`);
  }
}

/**
 * Generate deployment report
 */
async function generateDeploymentReport(success, duration, error = null) {
  const report = {
    timestamp: new Date(),
    environment: deploymentConfig.environment,
    success,
    duration,
    error: error ? error.message : null,
    services: deploymentConfig.services,
    steps: deploymentSteps
  };
  
  const reportPath = `./reports/deployment-${Date.now()}.json`;
  await fs.mkdir('./reports', { recursive: true });
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`📋 Deployment report saved: ${reportPath}`);
}

// Run deployment if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  deployOptimization().catch(console.error);
}

export { deployOptimization, rollbackDeployment };
