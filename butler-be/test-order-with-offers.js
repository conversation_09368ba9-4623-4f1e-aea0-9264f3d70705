import mongoose from "mongoose";
import Offer from "./models/Offer.js";
import Dish from "./models/Dish.js";
import Order from "./models/Order.js";
import FoodChain from "./models/FoodChain.js";
import Outlet from "./models/Outlet.js";
import User from "./models/User.js";
import Category from "./models/Category.js";
import { validateAndApplyOffers } from "./services/offer-validation-service.js";
import dotenv from "dotenv";

dotenv.config();

const testOrderWithOffers = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB Atlas");

    console.log("\n=== TESTING ORDER CREATION WITH OFFERS ===\n");

    // Get sample data
    const sampleDishes = await Dish.find({}).limit(3);
    const sampleOutlet = await Outlet.findOne({});
    const sampleFoodChain = await FoodChain.findOne({});
    const sampleUser = await User.findOne({});

    if (!sampleDishes.length || !sampleOutlet || !sampleFoodChain) {
      console.log("❌ Missing sample data");
      return;
    }

    console.log(`Using outlet: ${sampleOutlet.name}`);
    console.log(`Using food chain: ${sampleFoodChain.name}`);
    console.log(`Using user: ${sampleUser?.name || "Guest"}`);

    // 1. Create test order data
    const orderData = {
      outletId: sampleOutlet._id,
      foodChainId: sampleFoodChain._id,
      items: [
        {
          dishId: sampleDishes[0]._id,
          quantity: 2,
          price: sampleDishes[0].price,
          name: sampleDishes[0].name,
        },
        {
          dishId: sampleDishes[1]._id,
          quantity: 1,
          price: sampleDishes[1].price,
          name: sampleDishes[1].name,
        },
      ],
      totalAmount: sampleDishes[0].price * 2 + sampleDishes[1].price,
    };

    console.log("\n=== ORIGINAL ORDER ===");
    console.log(`Total Items: ${orderData.items.length}`);
    orderData.items.forEach((item) => {
      console.log(
        `  - ${item.name} x${item.quantity} = ₹${item.price * item.quantity}`
      );
    });
    console.log(`Subtotal: ₹${orderData.totalAmount}`);

    // 2. Apply offers
    console.log("\n=== APPLYING OFFERS ===");
    const offerResult = await validateAndApplyOffers(
      orderData,
      sampleUser?._id
    );

    console.log(`Success: ${offerResult.success}`);
    console.log(`Applied Offers: ${offerResult.appliedOffers?.length || 0}`);
    console.log(`Total Discount: ₹${offerResult.totalDiscount || 0}`);
    console.log(
      `Final Amount: ₹${offerResult.finalAmount || orderData.totalAmount}`
    );

    if (offerResult.appliedOffers?.length) {
      console.log("\nApplied Offers Details:");
      offerResult.appliedOffers.forEach((appliedOffer, index) => {
        console.log(
          `${index + 1}. ${appliedOffer.offerName} (${appliedOffer.offerType})`
        );
        console.log(`   Discount: ₹${appliedOffer.discount}`);
        if (appliedOffer.freeItems?.length) {
          console.log(`   Free Items: ${appliedOffer.freeItems.length}`);
        }
      });
    }

    // 3. Create actual order in database
    console.log("\n=== CREATING ORDER IN DATABASE ===");

    const newOrder = new Order({
      userId: sampleUser?._id,
      outletId: sampleOutlet._id,
      foodChainId: sampleFoodChain._id,
      items: orderData.items.map((item) => ({
        dishId: item.dishId,
        dishName: item.name,
        price: item.price,
        quantity: item.quantity,
      })),
      appliedOffers: offerResult.appliedOffers || [],
      offerDiscount: offerResult.totalDiscount || 0,
      totalAmount: orderData.totalAmount,
      finalAmount: offerResult.finalAmount || orderData.totalAmount,
      status: "pending",
      paymentStatus: "pending",
      orderType: "dine-in",
      createdAt: new Date(),
    });

    const savedOrder = await newOrder.save();
    console.log(`✅ Order created with ID: ${savedOrder._id}`);
    console.log(`Order Status: ${savedOrder.status}`);
    console.log(`Payment Status: ${savedOrder.paymentStatus}`);
    console.log(
      `Applied Offers Count: ${savedOrder.appliedOffers?.length || 0}`
    );

    // 4. Verify order can be retrieved with offers
    console.log("\n=== VERIFYING ORDER RETRIEVAL ===");

    const retrievedOrder = await Order.findById(savedOrder._id)
      .populate("userId", "name email phone")
      .populate("outletId", "name")
      .populate("items.dishId", "name category");

    console.log(`Retrieved Order ID: ${retrievedOrder._id}`);
    console.log(`Customer: ${retrievedOrder.userId?.name || "Guest"}`);
    console.log(`Outlet: ${retrievedOrder.outletId?.name}`);
    console.log(`Items: ${retrievedOrder.items.length}`);
    console.log(`Original Amount: ₹${retrievedOrder.totalAmount}`);
    console.log(`Offer Discount: ₹${retrievedOrder.offerDiscount}`);
    console.log(`Final Amount: ₹${retrievedOrder.finalAmount}`);

    if (retrievedOrder.appliedOffers?.length) {
      console.log("\nOffers Applied to Order:");
      retrievedOrder.appliedOffers.forEach((offer, index) => {
        console.log(
          `${index + 1}. ${offer.offerName} (${offer.offerType}): ₹${
            offer.discount
          }`
        );
      });
    }

    // 5. Test admin view - get orders with offers
    console.log("\n=== TESTING ADMIN VIEW ===");

    const adminOrders = await Order.find({
      foodChainId: sampleFoodChain._id,
      "appliedOffers.0": { $exists: true }, // Orders with at least one applied offer
    })
      .populate("userId", "name email")
      .populate("outletId", "name")
      .sort({ createdAt: -1 })
      .limit(5);

    console.log(`Found ${adminOrders.length} orders with applied offers`);

    adminOrders.forEach((order, index) => {
      console.log(`\n${index + 1}. Order ${order._id}`);
      console.log(`   Customer: ${order.userId?.name || "Guest"}`);
      console.log(`   Outlet: ${order.outletId?.name}`);
      console.log(
        `   Amount: ₹${order.totalAmount} - ₹${order.offerDiscount} = ₹${order.finalAmount}`
      );
      console.log(`   Offers: ${order.appliedOffers?.length || 0}`);

      if (order.appliedOffers?.length) {
        order.appliedOffers.forEach((offer) => {
          console.log(`     - ${offer.offerName}: ₹${offer.discount}`);
        });
      }
    });

    // 6. Test customer view - get user's orders with offers
    console.log("\n=== TESTING CUSTOMER VIEW ===");

    if (sampleUser) {
      const customerOrders = await Order.find({
        userId: sampleUser._id,
        "appliedOffers.0": { $exists: true },
      })
        .populate("outletId", "name")
        .sort({ createdAt: -1 })
        .limit(3);

      console.log(
        `Found ${customerOrders.length} orders for customer with offers`
      );

      customerOrders.forEach((order, index) => {
        console.log(`\n${index + 1}. Order ${order._id}`);
        console.log(`   Outlet: ${order.outletId?.name}`);
        console.log(`   Date: ${order.createdAt.toLocaleDateString()}`);
        console.log(`   Original: ₹${order.totalAmount}`);
        console.log(`   Discount: ₹${order.offerDiscount}`);
        console.log(`   Final: ₹${order.finalAmount}`);
        console.log(
          `   Savings: ₹${order.offerDiscount} (${Math.round(
            (order.offerDiscount / order.totalAmount) * 100
          )}%)`
        );

        if (order.appliedOffers?.length) {
          console.log(`   Offers Used:`);
          order.appliedOffers.forEach((offer) => {
            console.log(`     - ${offer.offerName}: ₹${offer.discount}`);
          });
        }
      });
    }

    console.log("\n=== ORDER WITH OFFERS TEST COMPLETED ===");
    console.log("\n✅ SUMMARY:");
    console.log(`- Offers are working and being applied correctly`);
    console.log(`- Order creation includes offer details`);
    console.log(`- Admin can view orders with applied offers`);
    console.log(`- Customer can see their savings and offer details`);
    console.log(`- Offer names and types are properly stored and retrieved`);

    process.exit(0);
  } catch (error) {
    console.error("Error during testing:", error);
    process.exit(1);
  }
};

testOrderWithOffers();
