# 🚀 Migration from Hugging Face to Groq + Open Source

This guide documents the complete migration from Hugging Face to Groq and open-source alternatives for the Butler AI system.

## 📋 Migration Summary

### ❌ Removed Dependencies
- `@huggingface/inference` - Replaced with Groq and open-source alternatives
- `HUGGINGFACE_API_KEY` environment variable

### ✅ Added Dependencies
- `@xenova/transformers` - Open-source embeddings (Sentence Transformers)
- Enhanced Groq integration with semantic processing

## 🔧 Environment Variables

### Required
```env
GROQ_API_KEY=your_groq_api_key
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
```

### Optional (for advanced features)
```env
OPENAI_API_KEY=your_openai_api_key  # Future use if needed
```

## 🆕 New Features

### 1. Multi-Language Support
- **Dynamic Language Detection**: Automatically detects Hindi, English, or Hinglish
- **Context-Aware Responses**: Maintains language consistency within conversations
- **Script-Based Detection**: Uses Devanagari script for Hindi identification

### 2. Enhanced Search System
- **Groq-Powered Semantic Search**: Uses Groq for keyword extraction and understanding
- **Open-Source Embeddings**: Local Sentence Transformers for vector search
- **Hybrid Search**: Combines keyword matching with semantic understanding

### 3. Personalized Menu Ordering
- **Order History Analysis**: Learns from user's past orders
- **Preference Tracking**: Identifies favorite categories, cuisines, and price ranges
- **Dynamic Menu Reordering**: Prioritizes items based on user preferences

### 4. Context-Aware Recommendations
- **Cart Context**: Understands current cart contents for complementary suggestions
- **Session Tracking**: Analyzes recent conversations and orders
- **Cross-Session Intelligence**: Learns from different outlet visits

## 🚀 API Enhancements

### Personalized Menu
```bash
GET /api/v1/user/dishes?foodChainId=CHAIN_ID&outletId=OUTLET_ID&userId=USER_ID&personalize=true
```

### Multi-Language Recommendations
```bash
# Hindi
GET /api/v1/user/recommendations?message=मुझे कुछ अच्छा खाना चाहिए

# English  
GET /api/v1/user/recommendations?message=I want something spicy

# Hinglish
GET /api/v1/user/recommendations?message=Kuch accha khana hai kya?
```

### Context-Aware Search
```bash
GET /api/v1/user/recommendations?message=what goes well with pizza?
```

## 🔄 Migration Steps

### 1. Install Dependencies
```bash
cd butler-be
npm install @xenova/transformers
npm uninstall @huggingface/inference
```

### 2. Update Environment Variables
Remove `HUGGINGFACE_API_KEY` and ensure `GROQ_API_KEY` is set.

### 3. Run Migration Script
```bash
npm run migrate-to-groq
```

### 4. Test the System
```bash
npm run test-ai
```

## 📊 Performance Benefits

### Cost Reduction
- **Zero External API Costs**: No more Hugging Face API charges for embeddings
- **Local Processing**: Embeddings generated locally using Xenova Transformers
- **Efficient Caching**: User preferences calculated from existing data

### Speed Improvements
- **Faster Embeddings**: Local processing eliminates API latency
- **Optimized Queries**: Efficient database queries with proper indexing
- **Smart Fallbacks**: Graceful degradation ensures system reliability

### Accuracy Improvements
- **Food-Specific Models**: Better understanding of culinary terms
- **Multi-Language Support**: Native support for Hindi, English, and Hinglish
- **Context Awareness**: Recommendations based on comprehensive user context

## 🧪 Testing

### Test Language Detection
```javascript
// Test different languages
const responses = await Promise.all([
  getRecommendations("मुझे कुछ अच्छा खाना चाहिए"), // Hindi
  getRecommendations("I want something spicy"),      // English
  getRecommendations("Kuch accha khana hai kya?")    // Hinglish
]);
```

### Test Personalization
```javascript
// Get personalized menu
const personalizedMenu = await getDishes({
  foodChainId: "CHAIN_ID",
  outletId: "OUTLET_ID", 
  userId: "USER_ID",
  personalize: true
});
```

### Test Context Awareness
```javascript
// Test with cart context
const recommendations = await getRecommendations(
  "what goes well with pizza?",
  { cartHistory: [{ dish: "Margherita Pizza", operation: "add" }] }
);
```

## 🔧 Troubleshooting

### Common Issues

1. **Groq API Key Error**
   - Ensure `GROQ_API_KEY` is set in `.env`
   - Verify API key is valid and has sufficient credits

2. **Embedding Generation Slow**
   - First-time model download may take time
   - Subsequent requests will be faster

3. **Language Detection Issues**
   - Check if text contains mixed scripts
   - Verify conversation history is being passed

4. **Personalization Not Working**
   - Ensure `userId` is provided in requests
   - Check if user has order history

### Debug Mode
Enable detailed logging by setting:
```env
NODE_ENV=development
DEBUG=butler:*
```

## 📈 Monitoring

### Key Metrics to Monitor
- Response times for AI recommendations
- Embedding generation performance
- Language detection accuracy
- Personalization effectiveness

### Health Checks
```bash
# Test Groq integration
curl -X GET "localhost:3000/api/v1/health/groq"

# Test embedding service
curl -X GET "localhost:3000/api/v1/health/embeddings"
```

## 🔮 Future Enhancements

### Planned Features
- **Voice Input Support**: Multi-language voice recognition
- **Image Recognition**: Dish image analysis for recommendations
- **Advanced Analytics**: User behavior insights and trends
- **Real-time Learning**: Continuous improvement from user interactions

### Potential Integrations
- **Local LLM Support**: Ollama integration for offline capabilities
- **Advanced Vector Search**: MongoDB Atlas Vector Search
- **Caching Layer**: Redis for improved performance

## 📞 Support

For issues or questions regarding the migration:
1. Check the troubleshooting section above
2. Review server logs for detailed error messages
3. Test individual components using the provided scripts
4. Ensure all environment variables are correctly set

---

**Migration completed successfully! 🎉**

The Butler AI system now runs entirely on Groq and open-source alternatives, providing better performance, lower costs, and enhanced features.
