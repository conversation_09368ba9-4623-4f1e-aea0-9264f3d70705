/**
 * MongoDB Atlas Vector Search Configuration
 * Centralized configuration for Atlas Vector Search features
 */

export const atlasVectorConfig = {
  // Atlas Vector Search Index Configuration
  index: {
    name: "butler-dishes",
    embeddingField: "embedding",
    embeddingDimensions: 384, // MiniLM-L6-v2 dimensions
  },

  // Search Parameters
  search: {
    defaultLimit: 10,
    maxLimit: 50,
    minScore: 0.05,
    numCandidates: 100,
    
    // Weights for hybrid search
    vectorWeight: 0.7,
    textWeight: 0.3,
    
    // Pre-filtering settings
    preFilterPercentage: 0.3, // Use 30% of available dishes
    maxPreFilterDishes: 50,
  },

  // Feature Flags
  features: {
    enableAtlasVectorSearch: process.env.ENABLE_ATLAS_VECTOR_SEARCH !== 'false',
    enableHybridSearch: process.env.ENABLE_HYBRID_SEARCH !== 'false',
    enableVectorPreFiltering: process.env.ENABLE_VECTOR_PREFILTERING !== 'false',
    fallbackToTextSearch: process.env.FALLBACK_TO_TEXT_SEARCH !== 'false',
  },

  // Performance Settings
  performance: {
    cacheResults: true,
    cacheTTL: 300, // 5 minutes
    timeoutMs: 5000, // 5 second timeout
    retryAttempts: 2,
    retryDelayMs: 1000,
  },

  // Monitoring and Logging
  monitoring: {
    logSearchQueries: process.env.LOG_VECTOR_SEARCHES === 'true',
    logPerformanceMetrics: process.env.LOG_VECTOR_PERFORMANCE === 'true',
    trackSearchAnalytics: process.env.TRACK_VECTOR_ANALYTICS === 'true',
  },

  // Fallback Configuration
  fallback: {
    enableFallbackChain: true,
    fallbackMethods: [
      'atlas_vector_search',
      'enhanced_semantic_search',
      'text_search',
      'basic_filter'
    ],
    fallbackThresholds: {
      minResults: 3,
      maxResponseTime: 3000,
    }
  }
};

/**
 * Get Atlas Vector Search configuration with environment overrides
 * @returns {Object} - Complete configuration object
 */
export const getAtlasVectorConfig = () => {
  return {
    ...atlasVectorConfig,
    
    // Environment variable overrides
    search: {
      ...atlasVectorConfig.search,
      defaultLimit: parseInt(process.env.ATLAS_VECTOR_DEFAULT_LIMIT) || atlasVectorConfig.search.defaultLimit,
      minScore: parseFloat(process.env.ATLAS_VECTOR_MIN_SCORE) || atlasVectorConfig.search.minScore,
      vectorWeight: parseFloat(process.env.ATLAS_VECTOR_WEIGHT) || atlasVectorConfig.search.vectorWeight,
    },
    
    performance: {
      ...atlasVectorConfig.performance,
      timeoutMs: parseInt(process.env.ATLAS_VECTOR_TIMEOUT_MS) || atlasVectorConfig.performance.timeoutMs,
      cacheTTL: parseInt(process.env.ATLAS_VECTOR_CACHE_TTL) || atlasVectorConfig.performance.cacheTTL,
    }
  };
};

/**
 * Validate Atlas Vector Search configuration
 * @returns {Object} - Validation result with any issues
 */
export const validateAtlasVectorConfig = () => {
  const config = getAtlasVectorConfig();
  const issues = [];

  // Validate required fields
  if (!config.index.name) {
    issues.push("Atlas Vector Search index name is required");
  }

  if (!config.index.embeddingField) {
    issues.push("Embedding field name is required");
  }

  if (config.index.embeddingDimensions <= 0) {
    issues.push("Embedding dimensions must be positive");
  }

  // Validate search parameters
  if (config.search.vectorWeight + config.search.textWeight !== 1.0) {
    issues.push("Vector and text weights must sum to 1.0");
  }

  if (config.search.minScore < 0 || config.search.minScore > 1) {
    issues.push("Minimum score must be between 0 and 1");
  }

  // Validate performance settings
  if (config.performance.timeoutMs <= 0) {
    issues.push("Timeout must be positive");
  }

  if (config.performance.cacheTTL <= 0) {
    issues.push("Cache TTL must be positive");
  }

  return {
    isValid: issues.length === 0,
    issues,
    config
  };
};

/**
 * Get feature flags for Atlas Vector Search
 * @returns {Object} - Feature flags object
 */
export const getAtlasVectorFeatureFlags = () => {
  const config = getAtlasVectorConfig();
  return config.features;
};

/**
 * Check if a specific Atlas Vector Search feature is enabled
 * @param {string} featureName - Name of the feature to check
 * @returns {boolean} - True if feature is enabled
 */
export const isAtlasVectorFeatureEnabled = (featureName) => {
  const features = getAtlasVectorFeatureFlags();
  return features[featureName] === true;
};

/**
 * Get search parameters with defaults
 * @param {Object} overrides - Parameter overrides
 * @returns {Object} - Search parameters
 */
export const getSearchParameters = (overrides = {}) => {
  const config = getAtlasVectorConfig();
  return {
    ...config.search,
    ...overrides
  };
};

/**
 * Get fallback configuration
 * @returns {Object} - Fallback configuration
 */
export const getFallbackConfig = () => {
  const config = getAtlasVectorConfig();
  return config.fallback;
};

/**
 * Log Atlas Vector Search configuration on startup
 */
export const logAtlasVectorConfig = () => {
  const validation = validateAtlasVectorConfig();
  
  if (validation.isValid) {
    console.log("✅ Atlas Vector Search configuration is valid");
    console.log(`📊 Index: ${validation.config.index.name}`);
    console.log(`🎯 Features enabled: ${Object.entries(validation.config.features)
      .filter(([, enabled]) => enabled)
      .map(([feature]) => feature)
      .join(', ')}`);
  } else {
    console.warn("⚠️ Atlas Vector Search configuration issues:");
    validation.issues.forEach(issue => console.warn(`  - ${issue}`));
  }
};
