import { verifyToken } from "../middlewares/auth.js";
import Order from "../models/Order.js";
import Payment from "../models/Payment.js";

export const setupOrderSocket = (io) => {
  const adminNamespace = io.of("/admin-orders");
  const customerNamespace = io.of("/customer-orders");

  // Customer namespace setup
  customerNamespace.use(async (socket, next) => {
    try {
      console.log(
        "Customer socket connection attempt",
        socket.handshake.auth ? "with auth" : "without auth"
      );

      // Check if token is provided for authentication
      const token = socket.handshake.auth?.token;
      if (token) {
        try {
          const decoded = verifyToken(token);
          if (decoded && decoded.role === "user") {
            socket.user = decoded;
            console.log(
              "Customer socket authenticated for user:",
              decoded.userId
            );
          } else {
            console.warn("Invalid customer token or role:", decoded?.role);
          }
        } catch (tokenError) {
          console.warn(
            "Customer token verification failed:",
            tokenError.message
          );
          // Continue without authentication for backward compatibility
        }
      }

      next();
    } catch (error) {
      console.error("Customer socket error:", error.message);
      next(new Error(`Connection error: ${error.message}`));
    }
  });

  customerNamespace.on("connection", (socket) => {
    console.log("Customer connected to socket");

    socket.on("join-order", async (orderId) => {
      console.log("Customer joining order room:", orderId);
      if (!orderId) {
        console.error("No order ID provided for join-order event");
        socket.emit("error", { message: "No order ID provided" });
        return;
      }

      // Validate orderId format
      if (!orderId.match(/^[0-9a-fA-F]{24}$/)) {
        console.error("Invalid order ID format:", orderId);
        socket.emit("error", { message: "Invalid order ID format" });
        return;
      }

      socket.join(`order-${orderId}`);
      console.log(`Customer joined order room: order-${orderId}`);

      // Send initial order status
      try {
        const order = await Order.findById(orderId)
          .populate("items.dishId", "name price")
          .populate("userId", "name phone email")
          .populate("outletId", "name address contact");

        if (order) {
          console.log(`Sending order data for order ID: ${orderId}`);
          socket.emit("order-status-update", {
            type: "order-status-update",
            data: order,
          });
        } else {
          console.error("Order not found for ID:", orderId);
          socket.emit("error", { message: "Order not found" });
        }
      } catch (error) {
        console.error("Error fetching initial order:", error);
        socket.emit("error", {
          message: "Error fetching order data",
          details: error.message,
        });
      }
    });

    socket.on("leave-order", (orderId) => {
      if (orderId) {
        socket.leave(`order-${orderId}`);
        console.log("Customer left order room:", orderId);
      }
    });

    socket.on("disconnect", () => {
      console.log("Customer disconnected from socket");
    });

    socket.on("error", (error) => {
      console.error("Customer socket error:", error);
    });
  });

  // Admin namespace setup
  adminNamespace.use(async (socket, next) => {
    try {
      console.log("Admin socket auth attempt", socket.handshake.auth);
      const token = socket.handshake.auth.token;
      if (!token) {
        console.error("No token provided in socket handshake");
        return next(new Error("Authentication error: No token provided"));
      }

      const decoded = verifyToken(token);
      console.log("Token decoded:", decoded ? "success" : "failed");

      if (!decoded) {
        console.error("Invalid token in socket handshake");
        return next(new Error("Authentication error: Invalid token"));
      }

      if (decoded.role !== "admin") {
        console.error("Unauthorized role in socket handshake:", decoded.role);
        return next(new Error("Unauthorized: Admin role required"));
      }

      socket.user = decoded;
      next();
    } catch (error) {
      console.error("Socket authentication error:", error.message);
      next(new Error(`Authentication error: ${error.message}`));
    }
  });

  adminNamespace.on("connection", async (socket) => {
    try {
      console.log("Admin connected:", socket.user.foodChain);

      // Subscribe admin to their food chain's room
      socket.join(`foodchain-${socket.user.foodChain}`);
      console.log(
        `🔗 Admin joined foodchain room: foodchain-${socket.user.foodChain}`
      );

      // Debug: Check current room membership
      const currentRooms = Array.from(socket.rooms);
      console.log(`🏠 Admin socket rooms:`, currentRooms);

      // Send initial orders for the admin's food chain
      try {
        const orders = await Order.find({ foodChainId: socket.user.foodChain })
          .sort({ createdAt: -1 })
          .limit(50)
          .populate("items.dishId", "name price")
          .populate("userId", "name phone email")
          .populate("outletId", "name address contact");

        console.log(`Sending ${orders.length} initial orders to admin`);
        socket.emit("initial-orders", { orders });
      } catch (error) {
        console.error("Error fetching initial orders:", error);
        socket.emit("error", { message: "Error fetching initial orders" });
      }

      socket.on("subscribe-outlet", (outletId) => {
        if (outletId) {
          socket.join(`outlet-${outletId}`);
          console.log(`Admin subscribed to outlet: outlet-${outletId}`);
        } else {
          console.error("No outlet ID provided for subscribe-outlet event");
        }
      });

      // Handle refresh-orders event
      socket.on("refresh-orders", async () => {
        try {
          console.log(
            `Admin requested order refresh for foodchain: ${socket.user.foodChain}`
          );

          // Fetch latest orders for the admin's food chain
          const orders = await Order.find({
            foodChainId: socket.user.foodChain,
          })
            .sort({ createdAt: -1 })
            .limit(50)
            .populate("items.dishId", "name price")
            .populate("userId", "name phone email")
            .populate("outletId", "name address contact");

          console.log(`Sending ${orders.length} refreshed orders to admin`);
          socket.emit("initial-orders", { orders });
        } catch (error) {
          console.error("Error refreshing orders:", error);
          socket.emit("error", { message: "Error refreshing orders" });
        }
      });

      socket.on("disconnect", () => {
        console.log("Admin disconnected from socket");
      });

      socket.on("error", (error) => {
        console.error("Admin socket error:", error);
      });
    } catch (error) {
      console.error("Error in admin socket connection handler:", error);
      socket.emit("error", { message: "Internal server error" });
    }
  });
};

export const emitNewOrder = async (orderId) => {
  try {
    console.log(`Emitting new order event for order ID: ${orderId}`);
    const order = await Order.findById(orderId)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    if (!order) {
      console.error(`Order not found for ID: ${orderId}`);
      return;
    }

    const io = global.io;
    if (!io) {
      console.error("Global io object not available");
      return;
    }

    const adminNamespace = io.of("/admin-orders");
    console.log(`Emitting to foodchain-${order.foodChainId} room`);

    // Get connected clients in the food chain room
    const foodChainRoom = adminNamespace.adapter.rooms.get(
      `foodchain-${order.foodChainId}`
    );
    console.log(
      `Connected clients in foodchain-${order.foodChainId} room:`,
      foodChainRoom ? foodChainRoom.size : 0
    );

    // REMOVED: Broadcasting to all admin clients regardless of food chain
    // This was causing orders to appear in the wrong food chains
    // console.log("Broadcasting new order to all admin clients");
    // adminNamespace.emit("new-order", {
    //   type: "new-order",
    //   data: order,
    // });

    // Emit to food chain room
    adminNamespace.to(`foodchain-${order.foodChainId}`).emit("new-order", {
      type: "new-order",
      data: order,
    });

    // Also emit to specific outlet room
    adminNamespace.to(`outlet-${order.outletId._id}`).emit("new-order", {
      type: "new-order",
      data: order,
    });

    console.log(
      `Successfully emitted new order event for order ID: ${orderId}`
    );
  } catch (error) {
    console.error("Error emitting new order:", error);
  }
};

export const emitOrderStatusUpdate = async (orderId) => {
  try {
    const order = await Order.findById(orderId)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    if (!order) return;

    const io = global.io;
    if (!io) return;

    // Emit to customer
    const customerNamespace = io.of("/customer-orders");
    customerNamespace.to(`order-${orderId}`).emit("order-status-update", {
      type: "order-status-update",
      data: order,
    });

    // Emit to admin (both food chain and outlet rooms)
    const adminNamespace = io.of("/admin-orders");
    adminNamespace
      .to(`foodchain-${order.foodChainId}`)
      .emit("order-status-update", {
        type: "order-status-update",
        data: order,
      });
    adminNamespace
      .to(`outlet-${order.outletId._id}`)
      .emit("order-status-update", {
        type: "order-status-update",
        data: order,
      });
  } catch (error) {
    console.error("Error emitting order status update:", error);
  }
};

export const emitPaymentUpdate = async (orderId) => {
  try {
    console.log(`Emitting payment update for order ID: ${orderId}`);
    const order = await Order.findById(orderId)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    if (!order) {
      console.error(`Order not found for payment update: ${orderId}`);
      return;
    }

    // Get payment details if available
    const payment = await Payment.findOne({ orderId });
    console.log(
      `Payment found for order ${orderId}:`,
      payment ? payment._id : "none"
    );

    const io = global.io;
    if (!io) {
      console.error("Global io object not available for payment update");
      return;
    }

    // Prepare data to emit
    const dataToEmit = {
      order,
      payment: payment || null,
    };

    // Emit to customer
    const customerNamespace = io.of("/customer-orders");
    customerNamespace.to(`order-${orderId}`).emit("payment-update", {
      type: "payment-update",
      data: dataToEmit,
    });
    console.log(`Emitted payment update to customer for order: ${orderId}`);

    // Emit to admin (both food chain and outlet rooms)
    const adminNamespace = io.of("/admin-orders");

    // Get connected clients in the food chain room for debugging
    const foodChainRoom = adminNamespace.adapter.rooms.get(
      `foodchain-${order.foodChainId}`
    );
    console.log(
      `Emitting payment update to foodchain-${order.foodChainId} room with ${
        foodChainRoom ? foodChainRoom.size : 0
      } connected clients`
    );

    adminNamespace.to(`foodchain-${order.foodChainId}`).emit("payment-update", {
      type: "payment-update",
      data: dataToEmit,
    });

    adminNamespace.to(`outlet-${order.outletId._id}`).emit("payment-update", {
      type: "payment-update",
      data: dataToEmit,
    });
    console.log(`Payment update emitted successfully for order: ${orderId}`);
  } catch (error) {
    console.error("Error emitting payment update:", error);
  }
};

export const emitOrderItemsUpdate = async (orderId, updatedBy = "user") => {
  try {
    console.log(
      `🔄 Emitting order items update for order ID: ${orderId}, updated by: ${updatedBy}`
    );
    const order = await Order.findById(orderId)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    if (!order) {
      console.error(`❌ Order not found for items update: ${orderId}`);
      return;
    }

    const io = global.io;
    if (!io) {
      console.error("❌ Global io object not available for order items update");
      return;
    }

    // Prepare data to emit
    const dataToEmit = {
      order,
      updatedBy,
      timestamp: new Date().toISOString(),
    };

    // Emit to customer
    const customerNamespace = io.of("/customer-orders");
    console.log(`Emitting to customer room: order-${orderId}`, dataToEmit);
    customerNamespace.to(`order-${orderId}`).emit("order-items-update", {
      type: "order-items-update",
      data: dataToEmit,
    });
    console.log(
      `✅ Emitted order items update to customer for order: ${orderId}`
    );

    // Emit to admin (both food chain and outlet rooms)
    const adminNamespace = io.of("/admin-orders");

    // Get connected clients in the food chain room for debugging
    const foodChainRoom = adminNamespace.adapter.rooms.get(
      `foodchain-${order.foodChainId}`
    );
    const outletRoom = adminNamespace.adapter.rooms.get(
      `outlet-${order.outletId._id}`
    );

    console.log(
      `🏢 Food chain room (foodchain-${order.foodChainId}): ${
        foodChainRoom ? foodChainRoom.size : 0
      } connected clients`
    );
    console.log(
      `🏪 Outlet room (outlet-${order.outletId._id}): ${
        outletRoom ? outletRoom.size : 0
      } connected clients`
    );

    // Emit order items update to all admins in the food chain
    const emitResult1 = adminNamespace
      .to(`foodchain-${order.foodChainId}`)
      .emit("order-items-update", {
        type: "order-items-update",
        data: dataToEmit,
      });
    console.log(
      `✅ Emitted order items update to food chain room: foodchain-${order.foodChainId}`,
      { emitResult: emitResult1 }
    );

    // Also emit to outlet room
    const emitResult2 = adminNamespace
      .to(`outlet-${order.outletId._id}`)
      .emit("order-items-update", {
        type: "order-items-update",
        data: dataToEmit,
      });
    console.log(
      `✅ Emitted order items update to outlet room: outlet-${order.outletId._id}`,
      { emitResult: emitResult2 }
    );

    // Debug: List all connected sockets in the namespace
    const allSockets = await adminNamespace.fetchSockets();
    console.log(`🔍 Total connected admin sockets: ${allSockets.length}`);
    allSockets.forEach((socket, index) => {
      console.log(`🔍 Socket ${index + 1}:`, {
        id: socket.id,
        rooms: Array.from(socket.rooms),
        userId: socket.user?.userId,
        foodChain: socket.user?.foodChain,
      });
    });

    // Emit a special notification event for admins when user updates order
    if (updatedBy === "user") {
      adminNamespace
        .to(`foodchain-${order.foodChainId}`)
        .emit("order-update-notification", {
          type: "order-update-notification",
          data: {
            orderId: order._id,
            orderNumber: order.orderNumber,
            customerName: order.userId?.name || "Unknown Customer",
            outletName: order.outletId?.name || "Unknown Outlet",
            totalAmount: order.finalAmount || order.totalAmount,
            timestamp: new Date().toISOString(),
            message: `Order #${order.orderNumber} has been updated by customer`,
          },
        });
      console.log(
        `🔔 Emitted order update notification to food chain room: foodchain-${order.foodChainId}`
      );
    }

    // For admin updates, emit a different notification to other admins
    if (updatedBy === "admin") {
      adminNamespace
        .to(`foodchain-${order.foodChainId}`)
        .emit("admin-order-update-notification", {
          type: "admin-order-update-notification",
          data: {
            orderId: order._id,
            orderNumber: order.orderNumber,
            customerName: order.userId?.name || "Unknown Customer",
            outletName: order.outletId?.name || "Unknown Outlet",
            totalAmount: order.finalAmount || order.totalAmount,
            timestamp: new Date().toISOString(),
            message: `Order #${order.orderNumber} has been updated by admin`,
          },
        });
      console.log(
        `🔔 Emitted admin order update notification to food chain room: foodchain-${order.foodChainId}`
      );
    }

    console.log(
      `✅ Order items update emitted successfully for order: ${orderId}`
    );
  } catch (error) {
    console.error("❌ Error emitting order items update:", error);
  }
};
