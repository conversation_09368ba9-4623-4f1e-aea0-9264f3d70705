import Cart from "../models/Cart.js";

/**
 * Cart Socket Handler
 * Handles real-time cart updates and notifications
 */

export const setupCartSocket = (io) => {
  const cartNamespace = io.of("/cart-updates");

  // Track user connections to prevent excessive connections
  const userConnections = new Map();

  cartNamespace.on("connection", (socket) => {
    console.log(`Cart socket connected: ${socket.id}`);

    // Join user-specific cart room
    socket.on("join-cart", ({ userId, foodChainId, outletId }) => {
      if (!userId || !foodChainId || !outletId) {
        socket.emit("error", { message: "Missing required parameters" });
        return;
      }

      const cartRoom = `cart-${userId}-${foodChainId}-${outletId}`;
      const userKey = `${userId}-${foodChainId}-${outletId}`;

      // Check if user already has an active connection
      if (userConnections.has(userKey)) {
        const existingSocket = userConnections.get(userKey);
        if (existingSocket && existingSocket.connected) {
          console.log(
            `User ${userId} already connected to cart room: ${cartRoom}`
          );
          // Disconnect the old socket
          existingSocket.disconnect();
        }
      }

      // Store the new connection
      userConnections.set(userKey, socket);
      socket.userKey = userKey;

      socket.join(cartRoom);
      console.log(`User ${userId} joined cart room: ${cartRoom}`);

      // Send current cart state (debounced)
      setTimeout(() => {
        sendCurrentCartState(socket, userId, foodChainId, outletId);
      }, 100);
    });

    // Leave cart room
    socket.on("leave-cart", ({ userId, foodChainId, outletId }) => {
      if (!userId || !foodChainId || !outletId) return;

      const cartRoom = `cart-${userId}-${foodChainId}-${outletId}`;
      const userKey = `${userId}-${foodChainId}-${outletId}`;

      socket.leave(cartRoom);
      console.log(`User ${userId} left cart room: ${cartRoom}`);

      // Clean up user connection tracking
      if (
        userConnections.has(userKey) &&
        userConnections.get(userKey) === socket
      ) {
        userConnections.delete(userKey);
      }
    });

    // Handle disconnect
    socket.on("disconnect", () => {
      console.log(`Cart socket disconnected: ${socket.id}`);

      // Clean up user connection tracking
      if (socket.userKey && userConnections.has(socket.userKey)) {
        if (userConnections.get(socket.userKey) === socket) {
          userConnections.delete(socket.userKey);
        }
      }
    });
  });

  return cartNamespace;
};

/**
 * Send current cart state to a specific socket
 */
const sendCurrentCartState = async (socket, userId, foodChainId, outletId) => {
  try {
    const cart = await Cart.findOne({
      userId,
      foodChainId,
      outletId,
    }).populate("items.dishId", "name price image isVeg category");

    if (cart) {
      socket.emit("cart-update", {
        type: "cart-state",
        data: {
          items: cart.items,
          appliedOffers: cart.appliedOffers,
          appliedCoupons: cart.appliedCoupons,
          subtotal: cart.subtotal,
          totalOfferDiscount: cart.totalOfferDiscount,
          totalCouponDiscount: cart.totalCouponDiscount,
          totalDiscount: cart.totalDiscount,
          taxAmount: cart.taxAmount,
          deliveryFee: cart.deliveryFee,
          packagingFee: cart.packagingFee,
          finalTotal: cart.finalTotal,
          updatedAt: cart.updatedAt,
        },
      });
    } else {
      // Send empty cart state
      socket.emit("cart-update", {
        type: "cart-state",
        data: {
          items: [],
          appliedOffers: [],
          appliedCoupons: [],
          subtotal: 0,
          totalOfferDiscount: 0,
          totalCouponDiscount: 0,
          totalDiscount: 0,
          taxAmount: 0,
          deliveryFee: 0,
          packagingFee: 0,
          finalTotal: 0,
          updatedAt: new Date(),
        },
      });
    }
  } catch (error) {
    console.error("Error sending cart state:", error);
    socket.emit("error", { message: "Failed to load cart state" });
  }
};

/**
 * Emit cart update to all connected clients for a specific user/outlet
 */
export const emitCartUpdate = async (
  userId,
  foodChainId,
  outletId,
  updateType = "update"
) => {
  try {
    const io = global.io;
    if (!io) {
      console.error("Global io object not available for cart updates");
      return;
    }

    const cartNamespace = io.of("/cart-updates");
    const cartRoom = `cart-${userId}-${foodChainId}-${outletId}`;

    console.log(`🛒 Emitting cart update to room: ${cartRoom}`);

    // Get updated cart data
    const cart = await Cart.findOne({
      userId,
      foodChainId,
      outletId,
    }).populate("items.dishId", "name price image isVeg category");

    const cartData = cart
      ? {
          items: cart.items,
          appliedOffers: cart.appliedOffers,
          appliedCoupons: cart.appliedCoupons,
          subtotal: cart.subtotal,
          totalOfferDiscount: cart.totalOfferDiscount,
          totalCouponDiscount: cart.totalCouponDiscount,
          totalDiscount: cart.totalDiscount,
          taxAmount: cart.taxAmount,
          deliveryFee: cart.deliveryFee,
          packagingFee: cart.packagingFee,
          finalTotal: cart.finalTotal,
          updatedAt: cart.updatedAt,
        }
      : {
          items: [],
          appliedOffers: [],
          appliedCoupons: [],
          subtotal: 0,
          totalOfferDiscount: 0,
          totalCouponDiscount: 0,
          totalDiscount: 0,
          taxAmount: 0,
          deliveryFee: 0,
          packagingFee: 0,
          finalTotal: 0,
          updatedAt: new Date(),
        };

    // Emit to all clients in the cart room
    cartNamespace.to(cartRoom).emit("cart-update", {
      type: updateType,
      data: cartData,
      timestamp: new Date(),
    });

    console.log(`✅ Cart update emitted to room: ${cartRoom}`);

    // Get connected clients count for debugging
    const room = cartNamespace.adapter.rooms.get(cartRoom);
    console.log(`📊 Connected clients in cart room: ${room ? room.size : 0}`);
  } catch (error) {
    console.error("Error emitting cart update:", error);
  }
};

/**
 * Emit cart operation result (for AI operations)
 */
export const emitCartOperation = async (
  userId,
  foodChainId,
  outletId,
  operation
) => {
  try {
    const io = global.io;
    if (!io) {
      console.error("Global io object not available for cart operation");
      return;
    }

    const cartNamespace = io.of("/cart-updates");
    const cartRoom = `cart-${userId}-${foodChainId}-${outletId}`;

    console.log(`🤖 Emitting cart operation to room: ${cartRoom}`, operation);

    // Emit the operation result
    cartNamespace.to(cartRoom).emit("cart-operation", {
      type: "ai-operation",
      operation: operation.operation,
      dish: operation.dish,
      quantity: operation.quantity,
      success: operation.success,
      message: operation.message,
      timestamp: new Date(),
    });

    // Also emit updated cart state
    await emitCartUpdate(userId, foodChainId, outletId, "ai-update");
  } catch (error) {
    console.error("Error emitting cart operation:", error);
  }
};
