console.log("🚀 Testing Enhanced AI System");

try {
  console.log("✅ Simple test working");
  
  // Test the import
  import('./services/agentic-response-service.js')
    .then(module => {
      console.log("✅ Successfully imported agentic-response-service");
      console.log("Available exports:", Object.keys(module));
      
      const { AgenticResponseGenerator } = module;
      if (AgenticResponseGenerator) {
        console.log("✅ AgenticResponseGenerator class found");
        
        // Test instantiation
        const generator = new AgenticResponseGenerator(
          "test-user",
          "test-outlet", 
          "test-chain",
          "en"
        );
        console.log("✅ AgenticResponseGenerator instantiated successfully");
        console.log("Generator context:", generator.context);
        
      } else {
        console.error("❌ AgenticResponseGenerator not found in exports");
      }
    })
    .catch(error => {
      console.error("❌ Import failed:", error);
    });
    
} catch (error) {
  console.error("❌ Test failed:", error);
}
