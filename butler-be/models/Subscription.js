import mongoose from "mongoose";

const subscriptionSchema = new mongoose.Schema({
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
  },
  planId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "SubscriptionPlan",
    required: true,
  },
  outletCount: {
    type: Number,
    required: true,
    min: 1,
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0,
  },
  status: {
    type: String,
    enum: ["active", "pending", "expired", "cancelled"],
    default: "pending",
  },
  startDate: {
    type: Date,
  },
  endDate: {
    type: Date,
  },
  autoRenew: {
    type: Boolean,
    default: true,
  },
  isExemptedFromNextPayment: {
    type: Boolean,
    default: false,
  },
  exemptionReason: {
    type: String,
  },
  lastInvoiceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "SubscriptionInvoice",
  },
  razorpaySubscriptionId: {
    type: String,
  },
  planHistory: [
    {
      planId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "SubscriptionPlan",
      },
      name: { type: String },
      price: { type: Number },
      startDate: { type: Date },
      endDate: { type: Date },
    },
  ],
  trialEndDate: {
    type: Date,
    default: function () {
      // Set trial end date to 15 days from now by default
      const date = new Date();
      date.setDate(date.getDate() + 15);
      return date;
    },
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.model("Subscription", subscriptionSchema);
