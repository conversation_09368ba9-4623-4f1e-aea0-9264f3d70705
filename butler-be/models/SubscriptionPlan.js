import mongoose from "mongoose";

const subscriptionPlanSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true 
  },
  description: { 
    type: String 
  },
  price: { 
    type: Number, 
    required: true,
    min: 0
  },
  currency: { 
    type: String, 
    default: "INR" 
  },
  interval: { 
    type: String,
    enum: ["monthly", "quarterly", "yearly"],
    default: "monthly"
  },
  features: [{ 
    type: String 
  }],
  isActive: { 
    type: Boolean, 
    default: true 
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

export default mongoose.model("SubscriptionPlan", subscriptionPlanSchema);
