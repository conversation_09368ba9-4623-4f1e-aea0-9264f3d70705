import mongoose from "mongoose";

const employeeSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true 
  },
  email: { 
    type: String, 
    required: true 
  },
  phone: { 
    type: String 
  },
  role: {
    type: String,
    enum: ["manager", "chef", "delivery", "cashier", "waiter"],
    required: true,
  },
  foodChain: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
  },
  status: {
    type: String,
    enum: ["active", "inactive"],
    default: "active",
  },
  // Portal access - if true, a User account will be created
  hasPortalAccess: {
    type: Boolean,
    default: false,
  },
  // Reference to User account if portal access is enabled
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    sparse: true,
  },
  // Employment details
  employeeId: {
    type: String,
    unique: true,
    sparse: true,
  },
  dateOfJoining: {
    type: Date,
    default: Date.now,
  },
  dateOfLeaving: {
    type: Date,
  },
  salary: {
    type: Number,
  },
  address: {
    type: String,
  },
  emergencyContact: {
    name: { type: String },
    phone: { type: String },
    relation: { type: String },
  },
  // Outlet assignments
  outlets: [{
    outletId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Outlet",
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    assignedAt: {
      type: Date,
      default: Date.now,
    },
  }],
  // Permissions and settings
  permissions: [{
    type: String,
  }],
  // Tracking
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  },
});

// Generate employee ID before saving
employeeSchema.pre('save', async function(next) {
  if (this.isNew && !this.employeeId) {
    const count = await mongoose.model('Employee').countDocuments({ foodChain: this.foodChain });
    this.employeeId = `EMP${String(count + 1).padStart(4, '0')}`;
  }
  this.updatedAt = Date.now();
  next();
});

// Index for better performance
employeeSchema.index({ foodChain: 1, status: 1 });
employeeSchema.index({ email: 1, foodChain: 1 });

export default mongoose.model("Employee", employeeSchema);
