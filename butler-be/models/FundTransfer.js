import mongoose from "mongoose";

const fundTransferSchema = new mongoose.Schema({
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 1,
  },
  razorpayPayoutId: {
    type: String,
  },
  razorpayFundAccountId: {
    type: String,
    required: false, // Made optional for route transfers
  },
  razorpayAccountId: {
    type: String,
    required: false, // For route transfers
  },
  status: {
    type: String,
    enum: ["initiated", "processing", "completed", "failed", "reversed"],
    default: "initiated",
  },
  reference: {
    type: String,
  },
  description: {
    type: String,
  },
  failureReason: {
    type: String,
  },
  transferMethod: {
    type: String,
    enum: ["IMPS", "NEFT", "RTGS", "UPI", "route"],
    default: "IMPS",
  },
  transferFee: {
    type: Number,
    default: 0,
  },
  transferredBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: false, // Made optional for system-initiated transfers
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.model("FundTransfer", fundTransferSchema);
