import mongoose from "mongoose";

const inventoryItemSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true 
  },
  description: { 
    type: String 
  },
  category: { 
    type: String,
    enum: ["ingredient", "packaging", "equipment", "other"],
    default: "ingredient"
  },
  unit: { 
    type: String, 
    required: true 
  },
  quantity: { 
    type: Number, 
    required: true,
    min: 0
  },
  minQuantity: { 
    type: Number, 
    default: 10 
  },
  costPerUnit: { 
    type: Number 
  },
  supplier: { 
    type: String 
  },
  supplierContact: { 
    type: String 
  },
  location: { 
    type: String 
  },
  expiryDate: { 
    type: Date 
  },
  lastRestocked: { 
    type: Date 
  },
  outletId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Outlet", 
    required: true 
  },
  foodChainId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "FoodChain", 
    required: true 
  },
  dishesUsedIn: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Dish" 
  }],
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create indexes for faster queries
inventoryItemSchema.index({ outletId: 1 });
inventoryItemSchema.index({ foodChainId: 1 });
inventoryItemSchema.index({ category: 1 });
inventoryItemSchema.index({ quantity: 1 });
inventoryItemSchema.index({ expiryDate: 1 });

// Virtual for checking if item is low on stock
inventoryItemSchema.virtual('isLowStock').get(function() {
  return this.quantity <= this.minQuantity;
});

// Virtual for checking if item is expired
inventoryItemSchema.virtual('isExpired').get(function() {
  if (!this.expiryDate) return false;
  return new Date() > this.expiryDate;
});

// Method to update quantity
inventoryItemSchema.methods.updateQuantity = function(change) {
  this.quantity += change;
  this.updatedAt = Date.now();
  if (change > 0) {
    this.lastRestocked = Date.now();
  }
  return this.save();
};

const inventoryTransactionSchema = new mongoose.Schema({
  itemId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "InventoryItem", 
    required: true 
  },
  type: { 
    type: String, 
    enum: ["addition", "deduction", "adjustment", "waste", "transfer"],
    required: true 
  },
  quantity: { 
    type: Number, 
    required: true 
  },
  previousQuantity: { 
    type: Number, 
    required: true 
  },
  newQuantity: { 
    type: Number, 
    required: true 
  },
  reason: { 
    type: String 
  },
  orderId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Order" 
  },
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "User", 
    required: true 
  },
  outletId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Outlet", 
    required: true 
  },
  foodChainId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "FoodChain", 
    required: true 
  },
  transferToOutletId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Outlet" 
  },
  timestamp: { 
    type: Date, 
    default: Date.now 
  }
});

// Create indexes for faster queries
inventoryTransactionSchema.index({ itemId: 1 });
inventoryTransactionSchema.index({ outletId: 1 });
inventoryTransactionSchema.index({ foodChainId: 1 });
inventoryTransactionSchema.index({ timestamp: -1 });
inventoryTransactionSchema.index({ type: 1 });

export const InventoryItem = mongoose.model("InventoryItem", inventoryItemSchema);
export const InventoryTransaction = mongoose.model("InventoryTransaction", inventoryTransactionSchema);
