import mongoose from "mongoose";

const campaignRecipientSchema = new mongoose.Schema({
  campaignId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Campaign", 
    required: true 
  },
  customerId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "User", 
    required: true 
  },
  status: { 
    type: String, 
    enum: ["pending", "sent", "failed", "opened", "clicked"], 
    default: "pending" 
  },
  sentAt: { 
    type: Date 
  },
  openedAt: { 
    type: Date 
  },
  clickedAt: { 
    type: Date 
  },
  errorMessage: {
    type: String
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create indexes for faster queries
campaignRecipientSchema.index({ campaignId: 1 });
campaignRecipientSchema.index({ customerId: 1 });
campaignRecipientSchema.index({ status: 1 });

export default mongoose.model("CampaignRecipient", campaignRecipientSchema);
