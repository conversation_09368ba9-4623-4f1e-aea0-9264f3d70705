import mongoose from "mongoose";

const cartOperationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true
  },
  conversationId: {
    type: String,
    required: true,
    index: true
  },
  operation: {
    type: String,
    enum: ["add", "remove", "update", "clear"],
    required: true
  },
  dishId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Dish"
  },
  quantity: {
    type: Number,
    default: 1,
    min: 1
  },
  status: {
    type: String,
    enum: ["pending", "completed", "failed"],
    default: "pending"
  },
  metadata: {
    type: Object
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create compound indices for faster lookups
cartOperationSchema.index({ userId: 1, conversationId: 1 });
cartOperationSchema.index({ createdAt: -1 });

export default mongoose.model("CartOperation", cartOperationSchema);
