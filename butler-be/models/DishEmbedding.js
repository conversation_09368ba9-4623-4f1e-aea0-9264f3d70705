import mongoose from "mongoose";

const dishEmbeddingSchema = new mongoose.Schema({
  dishId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Dish",
    required: true,
    index: true
  },
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
    index: true
  },
  outletId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Outlet",
    required: true,
    index: true
  },
  embedding: {
    type: [Number],
    required: true
  },
  metadata: {
    name: { type: String },
    description: { type: String },
    category: { type: String },
    price: { type: Number },
    tags: [{ type: String }],
    cuisine: { type: String }
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create a compound index for faster lookups
dishEmbeddingSchema.index({ foodChainId: 1, outletId: 1 });

// Update the updatedAt timestamp before saving
dishEmbeddingSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

export default mongoose.model("DishEmbedding", dishEmbeddingSchema);
