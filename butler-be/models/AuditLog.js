import mongoose from "mongoose";

const auditLogSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  action: {
    type: String,
    required: true,
    enum: [
      "login",
      "logout",
      "create",
      "update",
      "delete",
      "view",
      "export",
      "import",
      "password_change",
      "password_reset",
      "enable_2fa",
      "disable_2fa",
      "verify_2fa",
      "failed_login",
      "account_lock",
      "account_unlock",
      "permission_change",
      "status_change",
      "payment_process",
      "payment_refund",
      "transfer_funds",
      "other"
    ],
  },
  resourceType: {
    type: String,
    required: true,
    enum: [
      "user",
      "admin",
      "super_admin",
      "food_chain",
      "outlet",
      "dish",
      "category",
      "order",
      "payment",
      "conversation",
      "inventory",
      "notification",
      "system",
      "other"
    ],
  },
  resourceId: {
    type: mongoose.Schema.Types.ObjectId,
    required: false,
  },
  description: {
    type: String,
    required: true,
  },
  details: {
    type: Object,
    default: {},
  },
  ipAddress: {
    type: String,
  },
  userAgent: {
    type: String,
  },
  status: {
    type: String,
    enum: ["success", "failure", "warning", "info"],
    default: "success",
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
  },
  outletId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Outlet",
  },
});

// Index for faster queries
auditLogSchema.index({ userId: 1, timestamp: -1 });
auditLogSchema.index({ resourceType: 1, resourceId: 1 });
auditLogSchema.index({ foodChainId: 1, timestamp: -1 });
auditLogSchema.index({ timestamp: -1 });

export default mongoose.model("AuditLog", auditLogSchema);
