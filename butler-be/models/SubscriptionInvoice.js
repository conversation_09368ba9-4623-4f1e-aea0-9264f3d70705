import mongoose from "mongoose";

const subscriptionInvoiceSchema = new mongoose.Schema({
  subscriptionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Subscription",
    required: true,
  },
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
  },
  invoiceNumber: {
    type: String,
    required: true,
    unique: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 0,
  },
  currency: {
    type: String,
    default: "INR",
  },
  status: {
    type: String,
    enum: [
      "created",
      "paid",
      "failed",
      "cancelled",
      "expired",
      "partially_paid",
      "refunded",
      "partially_refunded",
    ],
    default: "created",
  },
  dueDate: {
    type: Date,
    required: true,
  },
  paidDate: {
    type: Date,
  },
  razorpayPaymentLinkId: {
    type: String,
  },
  razorpayInvoiceId: {
    type: String,
  },
  razorpayPaymentId: {
    type: String,
  },
  paymentLink: {
    type: String,
  },
  items: [
    {
      description: { type: String, required: true },
      quantity: { type: Number, required: true },
      unitPrice: { type: Number, required: true },
      amount: { type: Number, required: true },
    },
  ],
  notes: {
    type: String,
  },
  paymentHistory: [
    {
      status: { type: String, required: true },
      paymentId: { type: String },
      invoiceId: { type: String },
      refundId: { type: String },
      amount: { type: Number },
      method: { type: String },
      timestamp: { type: Date, default: Date.now },
      details: { type: String },
    },
  ],
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.model("SubscriptionInvoice", subscriptionInvoiceSchema);
