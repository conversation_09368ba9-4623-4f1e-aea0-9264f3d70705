import mongoose from "mongoose";

const notificationSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "User", 
    required: true 
  },
  title: { 
    type: String, 
    required: true 
  },
  message: { 
    type: String, 
    required: true 
  },
  type: { 
    type: String, 
    enum: ["info", "success", "warning", "error", "order", "payment", "inventory", "system"],
    default: "info" 
  },
  priority: { 
    type: String, 
    enum: ["low", "normal", "high", "urgent"],
    default: "normal" 
  },
  isRead: { 
    type: Boolean, 
    default: false 
  },
  actionLink: { 
    type: String 
  },
  resourceType: { 
    type: String,
    enum: ["order", "payment", "inventory", "user", "outlet", "foodChain", "system", "other"],
  },
  resourceId: { 
    type: mongoose.Schema.Types.ObjectId 
  },
  metadata: { 
    type: Object 
  },
  foodChainId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "FoodChain" 
  },
  outletId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Outlet" 
  },
  expiresAt: { 
    type: Date 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create indexes for faster queries
notificationSchema.index({ userId: 1, isRead: 1 });
notificationSchema.index({ userId: 1, createdAt: -1 });
notificationSchema.index({ foodChainId: 1 });
notificationSchema.index({ outletId: 1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ priority: 1 });

// Virtual for checking if notification is expired
notificationSchema.virtual('isExpired').get(function() {
  if (!this.expiresAt) return false;
  return new Date() > this.expiresAt;
});

// Method to mark notification as read
notificationSchema.methods.markAsRead = function() {
  this.isRead = true;
  return this.save();
};

// Static method to create a notification
notificationSchema.statics.createNotification = async function(notificationData) {
  try {
    const notification = new this(notificationData);
    await notification.save();
    return notification;
  } catch (error) {
    console.error("Error creating notification:", error);
    throw error;
  }
};

// Static method to create notifications for multiple users
notificationSchema.statics.createNotifications = async function(users, notificationData) {
  try {
    const notifications = [];
    for (const userId of users) {
      const notification = new this({
        ...notificationData,
        userId
      });
      await notification.save();
      notifications.push(notification);
    }
    return notifications;
  } catch (error) {
    console.error("Error creating notifications:", error);
    throw error;
  }
};

// Static method to get unread notifications count for a user
notificationSchema.statics.getUnreadCount = async function(userId) {
  try {
    return await this.countDocuments({ 
      userId, 
      isRead: false,
      $or: [
        { expiresAt: { $exists: false } },
        { expiresAt: { $gt: new Date() } }
      ]
    });
  } catch (error) {
    console.error("Error getting unread count:", error);
    throw error;
  }
};

export default mongoose.model("Notification", notificationSchema);
