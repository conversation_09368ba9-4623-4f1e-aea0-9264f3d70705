import mongoose from "mongoose";

const campaignSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true 
  },
  description: { 
    type: String 
  },
  type: { 
    type: String, 
    enum: ["email", "sms", "whatsapp"], 
    required: true 
  },
  status: { 
    type: String, 
    enum: ["draft", "scheduled", "sending", "sent", "failed", "cancelled"], 
    default: "draft" 
  },
  targetAudience: {
    type: { 
      type: String, 
      enum: ["all", "segment", "specific"], 
      default: "all" 
    },
    segment: { 
      type: String, 
      enum: ["active", "inactive", "new", "returning"], 
    },
    specificCustomers: [{ 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "User" 
    }]
  },
  content: {
    subject: { 
      type: String 
    },
    body: { 
      type: String, 
      required: true 
    },
    template: { 
      type: String 
    },
    attachments: [{ 
      type: String 
    }]
  },
  scheduledDate: { 
    type: Date 
  },
  sentDate: { 
    type: Date 
  },
  sentCount: { 
    type: Number, 
    default: 0 
  },
  openCount: { 
    type: Number, 
    default: 0 
  },
  clickCount: { 
    type: Number, 
    default: 0 
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "User", 
    required: true 
  },
  foodChainId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "FoodChain", 
    required: true 
  },
  associatedCoupon: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Coupon"
  },
  associatedOffer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Offer"
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create indexes for faster queries
campaignSchema.index({ foodChainId: 1 });
campaignSchema.index({ status: 1 });
campaignSchema.index({ type: 1 });
campaignSchema.index({ scheduledDate: 1 });

export default mongoose.model("Campaign", campaignSchema);
