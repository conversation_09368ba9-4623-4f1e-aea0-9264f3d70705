import mongoose from 'mongoose';

const categorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  foodChain: { type: mongoose.Schema.Types.ObjectId, ref: 'FoodChain', required: true },
  dishes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Dish' }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

export default mongoose.model('Category', categorySchema);