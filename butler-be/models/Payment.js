import mongoose from "mongoose";

const paymentSchema = new mongoose.Schema({
  orderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Order",
    required: true,
  },
  razorpayOrderId: {
    type: String,
    required: true,
  },
  razorpayPaymentId: {
    type: String,
  },
  razorpayPaymentLinkId: {
    type: String,
    required: true,
  },
  paymentLink: {
    type: String,
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    default: "INR",
  },
  status: {
    type: String,
    enum: [
      "created",
      "paid",
      "failed",
      "cancelled",
      "refunded",
      "partially_refunded",
    ],
    default: "created",
  },
  paymentMethod: {
    type: String,
    enum: ["cash", "online"],
    default: "online",
  },
  // Refund related fields
  refundAmount: {
    type: Number,
    default: 0,
  },
  refundReason: {
    type: String,
  },
  refundId: {
    type: String,
  },
  refundStatus: {
    type: String,
    enum: ["pending", "processed", "failed"],
  },
  // Partial payment tracking
  isPartialPayment: {
    type: Boolean,
    default: false,
  },
  remainingAmount: {
    type: Number,
    default: 0,
  },
  relatedPayments: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Payment",
    },
  ],
  notes: {
    type: String,
  },
  metadata: {
    type: Object,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  lastCheckedAt: {
    type: Date,
  },
});

export default mongoose.model("Payment", paymentSchema);
