import mongoose from "mongoose";

const foodChainSchema = new mongoose.Schema({
  name: { type: String, required: true },
  tagline: { type: String, required: true },
  contact: { type: String, required: true },
  email: { type: String, required: true },
  website: { type: String },
  status: {
    type: String,
    enum: ["active", "inactive", "suspended"],
    default: "active",
  },
  outlets: [{ type: mongoose.Schema.Types.ObjectId, ref: "Outlet" }],
  categories: [{ type: mongoose.Schema.Types.ObjectId, ref: "Category" }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  theme: {
    primaryColor: { type: String, default: "#000000" },
    secondaryColor: { type: String, default: "#000000" },
    accentColor: { type: String, default: "#000000" },
    logoUrl: { type: String, default: "" },
    favIcon: { type: String, default: "" },
  },
  bankDetails: {
    accountName: { type: String },
    accountNumber: { type: String },
    ifscCode: { type: String },
    bankName: { type: String },
    branchName: { type: String },
    upiId: { type: String },
  },
  // Razorpay Fund Account (old method)
  razorpayFundAccountId: { type: String },
  razorpayContactId: { type: String },
  razorpayVirtualAccountId: { type: String },

  // Razorpay Route (new method)
  razorpayAccountId: { type: String },
  razorpayAccountStatus: {
    type: String,
    enum: [
      "created",
      "activated",
      "needs_clarification",
      "rejected",
      "suspended",
    ],
    default: "created",
  },
  razorpayRouteEnabled: { type: Boolean, default: false },
  razorpayStakeholders: [{ type: String }],
  businessType: {
    type: String,
    enum: [
      "individual",
      "proprietorship",
      "partnership",
      "llp",
      "private_limited",
      "public_limited",
      "trust",
      "society",
      "ngo",
      "educational_institutes",
      "not_yet_registered",
      "other",
    ],
    default: "individual",
  },
  subcategory: {
    type: String,
    enum: [
      "restaurant",
      "online_food_ordering",
      "food_court",
      "catering",
      "alcohol",
      "restaurant_search_and_booking",
      "dairy_products",
      "bakeries",
    ],
    default: "restaurant",
  },
  contactPerson: { type: String },
  phone: { type: String },
  address: {
    street: { type: String },
    street2: { type: String },
    city: { type: String },
    state: { type: String },
    postalCode: { type: String },
    country: { type: String, default: "IN" },
  },
  legalInfo: {
    pan: { type: String },
    gst: { type: String },
  },
  bankAccountInfo: {
    name: { type: String },
    number: { type: String },
    ifsc: { type: String },
  },
});

export default mongoose.model("FoodChain", foodChainSchema);
