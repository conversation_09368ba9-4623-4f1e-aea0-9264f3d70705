import mongoose from "mongoose";

const offerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  description: {
    type: String,
  },
  offerType: {
    type: String,
    enum: [
      "BOGO",
      "combo",
      "discount",
      "freeItem",
      "quantityDiscount",
      "multiDishType",
      "minimumAmount",
      "dayOfWeek",
      "dateRange",
      "customerTier",
      "firstTime",
      "timeBasedSpecial",
    ],
    required: true,
  },
  discountDetails: {
    discountType: {
      type: String,
      enum: ["percentage", "fixed"],
    },
    discountValue: {
      type: Number,
      min: 0,
    },
    // BOGO and quantity discount fields
    buyQuantity: {
      type: Number,
      min: 1,
    },
    getQuantity: {
      type: Number,
      min: 1,
    },
    // Free item fields
    freeItemId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Dish",
    },
    freeItemQuantity: {
      type: Number,
      default: 1,
      min: 1,
    },
    // Combo offer fields
    comboItems: [
      {
        dishId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Dish",
        },
        quantity: {
          type: Number,
          default: 1,
          min: 1,
        },
      },
    ],
    comboPrice: {
      type: Number,
      min: 0,
    },
    // Minimum amount fields
    minimumOrderValue: {
      type: Number,
      min: 0,
    },
    maxDiscount: {
      type: Number,
      min: 0,
    },
    // Multi-dish type requirements
    requiredCategories: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Category",
      },
    ],
    minimumCategoriesCount: {
      type: Number,
      min: 1,
    },
    // Customer tier restrictions
    customerTiers: [
      {
        type: String,
        enum: ["new", "regular", "vip", "premium"],
      },
    ],
    // Time-based restrictions
    timeRestrictions: {
      startTime: { type: String }, // HH:MM format
      endTime: { type: String }, // HH:MM format
      daysOfWeek: [
        {
          type: Number,
          min: 0,
          max: 6, // 0 = Sunday, 6 = Saturday
        },
      ],
    },
  },
  startDate: {
    type: Date,
    required: true,
  },
  endDate: {
    type: Date,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
  },
  applicableOutlets: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Outlet",
    },
  ],
  applicableDishes: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Dish",
    },
  ],
  termsAndConditions: {
    type: String,
  },
  bannerImage: {
    type: String,
  },
  displayOnApp: {
    type: Boolean,
    default: true,
  },
  // Offer stacking and usage rules
  stackingRules: {
    canStackWithOthers: {
      type: Boolean,
      default: false,
    },
    stackableOfferTypes: [
      {
        type: String,
      },
    ],
    priority: {
      type: Number,
      default: 0, // Higher number = higher priority
    },
  },
  usageRules: {
    usageLimit: {
      type: Number,
      default: 0, // 0 means unlimited
    },
    usedCount: {
      type: Number,
      default: 0,
    },
    perCustomerLimit: {
      type: Number,
      default: 0, // 0 means unlimited per customer
    },
    perOrderLimit: {
      type: Number,
      default: 1, // How many times this offer can be applied in a single order
    },
  },
  // Auto-application settings
  autoApply: {
    type: Boolean,
    default: false,
  },
  // Analytics tracking
  analytics: {
    totalUsage: {
      type: Number,
      default: 0,
    },
    totalSavings: {
      type: Number,
      default: 0,
    },
    uniqueCustomers: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    ],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Create indexes for faster queries
offerSchema.index({ foodChainId: 1 });
offerSchema.index({ isActive: 1 });
offerSchema.index({ startDate: 1, endDate: 1 });
offerSchema.index({ offerType: 1 });
offerSchema.index({ autoApply: 1 });
offerSchema.index({ "stackingRules.priority": -1 });
offerSchema.index({ applicableOutlets: 1 });
offerSchema.index({ "discountDetails.customerTiers": 1 });

// Virtual for checking if offer is expired
offerSchema.virtual("isExpired").get(function () {
  return new Date() > this.endDate;
});

// Virtual for checking if offer is valid to use
offerSchema.virtual("isValid").get(function () {
  const now = new Date();
  return this.isActive && now >= this.startDate && now <= this.endDate;
});

// Virtual for checking if offer has usage limit reached
offerSchema.virtual("isUsageLimitReached").get(function () {
  return (
    this.usageRules.usageLimit > 0 &&
    this.usageRules.usedCount >= this.usageRules.usageLimit
  );
});

// Virtual for checking if offer is time-restricted for current time
offerSchema.virtual("isTimeRestricted").get(function () {
  if (!this.discountDetails.timeRestrictions) return false;

  const now = new Date();
  const currentDay = now.getDay();
  const currentTime =
    now.getHours().toString().padStart(2, "0") +
    ":" +
    now.getMinutes().toString().padStart(2, "0");

  const { startTime, endTime, daysOfWeek } =
    this.discountDetails.timeRestrictions;

  // Check day of week restriction
  if (daysOfWeek && daysOfWeek.length > 0 && !daysOfWeek.includes(currentDay)) {
    return true; // Restricted (not available today)
  }

  // Check time restriction
  if (startTime && endTime) {
    if (currentTime < startTime || currentTime > endTime) {
      return true; // Restricted (not available at this time)
    }
  }

  return false; // Not restricted
});

export default mongoose.model("Offer", offerSchema);
