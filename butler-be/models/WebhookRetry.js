import mongoose from "mongoose";

const webhookRetrySchema = new mongoose.Schema({
  event: {
    type: String,
    required: true,
  },
  payload: {
    type: Object,
    required: true,
  },
  headers: {
    type: Object,
  },
  attempts: {
    type: Number,
    default: 0,
  },
  maxAttempts: {
    type: Number,
    default: 5,
  },
  lastAttemptAt: {
    type: Date,
  },
  nextAttemptAt: {
    type: Date,
  },
  status: {
    type: String,
    enum: ["pending", "processing", "failed", "completed"],
    default: "pending",
  },
  error: {
    type: String,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Create indexes for faster queries
webhookRetrySchema.index({ status: 1, nextAttemptAt: 1 });
webhookRetrySchema.index({ event: 1 });

export default mongoose.model("WebhookRetry", webhookRetrySchema);
