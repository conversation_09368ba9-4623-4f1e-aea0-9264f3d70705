import mongoose from "mongoose";

const orderSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  outletId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Outlet",
    required: true,
  },
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
  },
  items: [
    {
      dishId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Dish",
        required: true,
      },
      dishName: {
        type: String,
        required: true, // Store dish name to handle deleted dishes
      },
      quantity: {
        type: Number,
        required: true,
        min: 1,
      },
      price: {
        type: Number,
        required: true,
      },
      isServed: {
        type: Boolean,
        default: false,
      },
      servedQuantity: {
        type: Number,
        default: 0,
        min: 0,
      },
      servedAt: {
        type: Date,
      },
      servedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    },
  ],
  totalAmount: {
    type: Number,
    required: true,
  },
  couponCode: {
    type: String,
    uppercase: true,
    trim: true,
  },
  couponDiscount: {
    type: Number,
    default: 0,
    min: 0,
  },
  // Applied offers information
  appliedOffers: [
    {
      offerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Offer",
        required: true,
      },
      offerName: {
        type: String,
        required: true,
      },
      offerType: {
        type: String,
        required: true,
      },
      discount: {
        type: Number,
        required: true,
        min: 0,
      },
      freeItems: [
        {
          dishId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Dish",
          },
          dishName: String,
          quantity: {
            type: Number,
            min: 1,
          },
          price: {
            type: Number,
            min: 0,
          },
        },
      ],
    },
  ],
  offerDiscount: {
    type: Number,
    default: 0,
    min: 0,
  },
  finalAmount: {
    type: Number,
    min: 0,
  },
  status: {
    type: String,
    enum: [
      "pending",
      "confirmed",
      "preparing",
      "ready",
      "completed",
      "cancelled",
      "rejected",
      "modified",
    ],
    default: "pending",
  },
  priority: {
    type: String,
    enum: ["low", "normal", "high", "urgent"],
    default: "normal",
  },
  assignedTo: {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    role: { type: String, enum: ["chef", "delivery", "manager"] },
    assignedAt: { type: Date },
  },
  kitchenNotes: { type: String },
  modificationReason: { type: String },
  cancellationReason: { type: String },
  orderNumber: {
    type: String,
    required: true,
    unique: true,
  },
  specialInstructions: String,
  tableNumber: String,
  paymentStatus: {
    type: String,
    enum: ["pending", "requested", "paid", "failed"],
    default: "pending",
  },
  paymentMethod: {
    type: String,
    enum: ["cash", "online"],
    default: "cash",
  },
  inventoryProcessed: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Add these indexes to your Order schema
orderSchema.index({ foodChainId: 1, status: 1 });
orderSchema.index({ userId: 1 });
orderSchema.index({ createdAt: -1 });

// Calculate final amount before saving
orderSchema.pre("save", function (next) {
  // If finalAmount is not set and totalAmount is available, calculate it
  if (this.totalAmount !== undefined && this.finalAmount === undefined) {
    // Calculate total discounts (coupon + offers)
    const totalDiscounts =
      (this.couponDiscount || 0) + (this.offerDiscount || 0);
    this.finalAmount = Math.max(0, this.totalAmount - totalDiscounts);
  }
  next();
});

// Generate unique order number before validation
orderSchema.pre("validate", async function (next) {
  try {
    if (this.isNew && !this.orderNumber) {
      const date = new Date();
      const year = date.getFullYear().toString().slice(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const datePrefix = `${year}${month}${day}`;

      // Use a more reliable approach to generate unique order numbers
      let isUnique = false;
      let orderNumber = "";
      let maxAttempts = 10;
      let attempt = 0;

      while (!isUnique && attempt < maxAttempts) {
        attempt++;

        try {
          // Get the latest order for today
          const latestOrder = await this.constructor
            .findOne({
              orderNumber: { $regex: `^${datePrefix}` },
            })
            .sort({ orderNumber: -1 });

          let sequentialNumber = 1;

          if (latestOrder && latestOrder.orderNumber) {
            // Extract the sequential part and increment
            try {
              const currentSequential = parseInt(
                latestOrder.orderNumber.substring(6)
              );
              if (!isNaN(currentSequential)) {
                sequentialNumber = currentSequential + 1;
              }
            } catch (err) {
              console.error("Error parsing order number:", err);
              // Use a fallback if parsing fails
              sequentialNumber = Math.floor(Math.random() * 1000) + 1;
            }
          }

          // Format: YYMMDDxxxx (xxxx is sequential number)
          orderNumber = `${datePrefix}${sequentialNumber
            .toString()
            .padStart(4, "0")}`;

          // Check if this order number already exists
          const existingOrder = await this.constructor.findOne({ orderNumber });

          if (!existingOrder) {
            isUnique = true;
          }
        } catch (err) {
          console.error("Error generating order number:", err);
          // Use a fallback if the query fails
          const randomNum = Math.floor(Math.random() * 9000) + 1000;
          orderNumber = `${datePrefix}${randomNum}`;
          isUnique = true; // Assume it's unique to exit the loop
        }
      }

      if (!isUnique) {
        // If we couldn't generate a unique number after several attempts,
        // use timestamp to ensure uniqueness
        const timestamp = Date.now().toString().slice(-6);
        orderNumber = `${datePrefix}${timestamp}`;
      }

      this.orderNumber = orderNumber;
    }
    next();
  } catch (error) {
    console.error("Error in order number generation:", error);
    // Generate a fallback order number in case of any error
    if (this.isNew && !this.orderNumber) {
      const date = new Date();
      const timestamp = Date.now().toString();
      this.orderNumber = `${date.getFullYear().toString().slice(-2)}${(
        date.getMonth() + 1
      )
        .toString()
        .padStart(2, "0")}${date
        .getDate()
        .toString()
        .padStart(2, "0")}${timestamp.slice(-6)}`;
    }
    next();
  }
});

export default mongoose.model("Order", orderSchema);
