import { detectCartOperation } from './services/cart-service.js';

console.log("🧪 Testing Conversation Issues from conversation.md");
console.log("=================================================");

async function testConversationIssues() {
  const testCases = [
    {
      name: "Add it to cart (pronoun resolution)",
      message: "add it to my cart",
      expectedOperation: "add",
      expectedItem: "should resolve to recent dish",
      context: "After user saw recommendations"
    },
    {
      name: "Order it (pronoun resolution)", 
      message: "order it",
      expectedOperation: "order",
      expectedItem: null,
      context: "Should place order for cart contents"
    },
    {
      name: "Place order",
      message: "place order", 
      expectedOperation: "order",
      expectedItem: null,
      context: "Should place order for cart contents"
    },
    {
      name: "Remove specific item",
      message: "remove Chicken Burger meal from cart",
      expectedOperation: "remove", 
      expectedItem: "Chicken Burger meal",
      context: "Should remove specific item"
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    console.log(`Message: "${testCase.message}"`);
    console.log(`Context: ${testCase.context}`);
    
    try {
      // Test with mock user context for pronoun resolution
      const result = await detectCartOperation(
        testCase.message,
        "test-user-123", // userId
        "test-conversation-456" // conversationId
      );
      
      console.log("Result:", JSON.stringify(result, null, 2));
      
      if (result) {
        console.log(`✅ Operation detected: ${result.operation}`);
        if (result.item) {
          console.log(`✅ Item: ${result.item}`);
        }
        
        // Check if expected operation matches
        if (result.operation === testCase.expectedOperation) {
          console.log("✅ Operation type matches expected");
        } else {
          console.log(`❌ Expected operation: ${testCase.expectedOperation}, Got: ${result.operation}`);
        }
      } else {
        console.log("❌ No cart operation detected");
      }
      
    } catch (error) {
      console.error(`❌ Error testing ${testCase.name}:`, error.message);
    }
    
    console.log("---");
  }
}

// Test specific patterns
async function testOrderPatterns() {
  console.log("\n🔍 Testing Order Patterns Specifically");
  console.log("=====================================");
  
  const orderMessages = [
    "order it",
    "place order", 
    "order now",
    "checkout",
    "place my order",
    "confirm order"
  ];
  
  for (const message of orderMessages) {
    console.log(`\nTesting: "${message}"`);
    const result = await detectCartOperation(message);
    
    if (result && result.operation === "order") {
      console.log("✅ Order operation detected correctly");
    } else {
      console.log(`❌ Expected order operation, got:`, result);
    }
  }
}

// Test remove patterns
async function testRemovePatterns() {
  console.log("\n🔍 Testing Remove Patterns Specifically");
  console.log("======================================");
  
  const removeMessages = [
    "remove Chicken Burger meal from cart",
    "remove chicken burger from my cart",
    "delete Chicken Burger meal from cart",
    "take out chicken burger"
  ];
  
  for (const message of removeMessages) {
    console.log(`\nTesting: "${message}"`);
    const result = await detectCartOperation(message);
    
    if (result && result.operation === "remove") {
      console.log(`✅ Remove operation detected: ${result.item}`);
    } else {
      console.log(`❌ Expected remove operation, got:`, result);
    }
  }
}

// Run all tests
async function runAllTests() {
  try {
    await testConversationIssues();
    await testOrderPatterns();
    await testRemovePatterns();
    console.log("\n🎉 All tests completed!");
  } catch (error) {
    console.error("❌ Test execution failed:", error);
  }
}

runAllTests();
