# 🎯 MongoDB Atlas Vector Search Integration

This document describes the integration of MongoDB Atlas Vector Search into the Butler recommendation system for faster and more relevant dish recommendations.

## 📋 Overview

The Atlas Vector Search integration provides:
- **Faster Search**: Leverages MongoDB's optimized vector search index
- **Better Relevance**: More accurate semantic matching using vector embeddings
- **Reduced Latency**: Pre-filtering reduces the number of dishes sent to AI processing
- **Graceful Fallbacks**: Multiple fallback mechanisms ensure system reliability

## 🏗️ Architecture

### Integration Flow
```
User Query → Atlas Vector Search → Pre-filtering → Semantic Search → AI Processing → Response
     ↓              ↓                    ↓              ↓              ↓
   Fallback    Text Search      Basic Filter    Menu Search    Fallback Service
```

### Key Components

1. **Atlas Vector Search Service** (`atlas-vector-search-service.js`)
   - Direct MongoDB Atlas Vector Search queries
   - Hybrid search combining vector and text search
   - Availability checking and error handling

2. **Fallback Service** (`atlas-vector-fallback-service.js`)
   - Smart recommendation orchestration
   - Multiple fallback strategies
   - Performance monitoring

3. **Configuration** (`atlas-vector-config.js`)
   - Centralized configuration management
   - Feature flags and environment overrides
   - Validation and monitoring settings

4. **Test Controllers** (`atlas-vector-test-controller.js`)
   - Comprehensive testing endpoints
   - Performance comparison tools
   - Configuration validation

## 🚀 Setup Instructions

### 1. MongoDB Atlas Vector Search Index

Your MongoDB Atlas cluster should have a vector search index named `butler-dishes` with the following configuration:

```json
{
  "fields": [
    {
      "type": "vector",
      "path": "embedding",
      "numDimensions": 384,
      "similarity": "cosine"
    },
    {
      "type": "filter",
      "path": "foodChain"
    },
    {
      "type": "filter", 
      "path": "isAvailable"
    },
    {
      "type": "filter",
      "path": "outlets"
    }
  ]
}
```

### 2. Environment Variables

Add these variables to your `.env` file:

```env
# Atlas Vector Search Configuration
ENABLE_ATLAS_VECTOR_SEARCH=true
ENABLE_HYBRID_SEARCH=true
ENABLE_VECTOR_PREFILTERING=true
FALLBACK_TO_TEXT_SEARCH=true

# Performance Settings
ATLAS_VECTOR_DEFAULT_LIMIT=10
ATLAS_VECTOR_MIN_SCORE=0.05
ATLAS_VECTOR_WEIGHT=0.7
ATLAS_VECTOR_TIMEOUT_MS=5000
ATLAS_VECTOR_CACHE_TTL=300

# Monitoring (optional)
LOG_VECTOR_SEARCHES=false
LOG_VECTOR_PERFORMANCE=false
TRACK_VECTOR_ANALYTICS=false
```

### 3. Data Requirements

Ensure your `dishes` collection has vector embeddings. The system expects:
- `embedding` field with 384-dimensional vectors (MiniLM-L6-v2 model)
- Standard dish fields: `name`, `description`, `foodChain`, `isAvailable`, `outlets`

## 🧪 Testing

### Test Endpoints

The integration includes comprehensive test endpoints at `/api/v1/atlas-vector-test/`:

1. **Availability Check**
   ```
   GET /api/v1/atlas-vector-test/availability
   ```

2. **Direct Vector Search**
   ```
   GET /api/v1/atlas-vector-test/vector-search?query=spicy chicken&foodChainId=123
   ```

3. **Hybrid Search**
   ```
   GET /api/v1/atlas-vector-test/hybrid-search?query=spicy chicken&foodChainId=123
   ```

4. **Smart Recommendations**
   ```
   GET /api/v1/atlas-vector-test/smart-recommendations?query=spicy chicken&foodChainId=123
   ```

5. **Method Comparison**
   ```
   GET /api/v1/atlas-vector-test/compare-methods?query=spicy chicken&foodChainId=123
   ```

6. **Performance Metrics**
   ```
   GET /api/v1/atlas-vector-test/metrics
   ```

### Testing Steps

1. **Check Availability**
   ```bash
   curl "http://localhost:3001/api/v1/atlas-vector-test/availability"
   ```

2. **Test with Sample Query**
   ```bash
   curl "http://localhost:3001/api/v1/atlas-vector-test/vector-search?query=spicy%20chicken&foodChainId=YOUR_FOOD_CHAIN_ID"
   ```

3. **Compare Performance**
   ```bash
   curl "http://localhost:3001/api/v1/atlas-vector-test/compare-methods?query=spicy%20chicken&foodChainId=YOUR_FOOD_CHAIN_ID"
   ```

## ⚙️ Configuration

### Feature Flags

- `enableAtlasVectorSearch`: Enable/disable Atlas Vector Search
- `enableHybridSearch`: Enable hybrid vector + text search
- `enableVectorPreFiltering`: Use vector search for pre-filtering
- `fallbackToTextSearch`: Enable text search fallback

### Performance Tuning

- `defaultLimit`: Default number of results to return
- `minScore`: Minimum similarity score threshold
- `vectorWeight`: Weight for vector search in hybrid mode (0.7 recommended)
- `textWeight`: Weight for text search in hybrid mode (0.3 recommended)
- `timeoutMs`: Timeout for vector search operations
- `cacheTTL`: Cache time-to-live in seconds

### Fallback Chain

The system uses a prioritized fallback chain:
1. Atlas Vector Search
2. Enhanced Semantic Search
3. Text Search
4. Basic Filter
5. Original Fallback Service

## 📊 Performance Benefits

### Expected Improvements

- **Speed**: 60-80% faster dish filtering
- **Relevance**: 40-60% better semantic matching
- **Token Reduction**: 50-70% fewer tokens sent to AI
- **Cost Savings**: Significant reduction in AI API costs

### Monitoring

The system tracks:
- Atlas Vector Search success rate
- Fallback usage statistics
- Average response times
- Search result quality metrics

## 🔧 Integration Points

### Recommendation Orchestrator

The Atlas Vector Search is integrated as "Stage 0" in the recommendation flow:

```javascript
// Stage 0: Atlas Vector Search Pre-filtering
const atlasResults = await getAtlasVectorRecommendations(query, availableDishes, context);

// Use filtered results for subsequent stages
const preFilterResult = await smartPreFilter(query, atlasResults, userId, context);
```

### Smart Recommendations

Use the new smart recommendations service for automatic fallback handling:

```javascript
import { getSmartRecommendations } from './services/atlas-vector-fallback-service.js';

const result = await getSmartRecommendations(query, availableDishes, {
  foodChainId,
  outletId,
  userId,
  limit: 8
});
```

## 🚨 Troubleshooting

### Common Issues

1. **Index Not Found**
   - Verify the `butler-dishes` index exists in MongoDB Atlas
   - Check index configuration matches expected schema

2. **No Results Returned**
   - Verify dishes have embedding vectors
   - Check foodChainId and outletId filters
   - Lower the minScore threshold

3. **Slow Performance**
   - Increase numCandidates for better recall
   - Optimize index configuration
   - Check network latency to MongoDB Atlas

4. **Fallback Always Used**
   - Check Atlas Vector Search availability
   - Verify environment variables are set correctly
   - Review error logs for specific issues

### Debug Mode

Enable detailed logging:
```env
LOG_VECTOR_SEARCHES=true
LOG_VECTOR_PERFORMANCE=true
```

## 🔮 Future Enhancements

- **Real-time Embedding Updates**: Automatic embedding generation for new dishes
- **Advanced Filtering**: More sophisticated pre-filtering based on user preferences
- **A/B Testing**: Compare Atlas Vector Search vs traditional methods
- **Analytics Dashboard**: Visual monitoring of search performance
- **Multi-language Support**: Language-specific vector embeddings

## 📚 References

- [MongoDB Atlas Vector Search Documentation](https://docs.atlas.mongodb.com/atlas-search/vector-search/)
- [Sentence Transformers Documentation](https://www.sbert.net/)
- [Butler Recommendation System Architecture](./RECOMMENDATION_OPTIMIZATION.md)
