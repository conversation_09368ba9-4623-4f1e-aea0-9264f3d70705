import mongoose from "mongoose";
import Dish from "./models/Dish.js";
import Category from "./models/Category.js";
import FoodChain from "./models/FoodChain.js";
import Outlet from "./models/Outlet.js";
import User from "./models/User.js";
import { AgenticResponseGenerator } from "./services/agentic-response-service.js";
import dotenv from "dotenv";

dotenv.config();

const testAISimple = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB Atlas");

    console.log("\n=== TESTING AI WITH CURRENT MENU ===\n");

    // Get sample data
    const sampleFoodChain = await FoodChain.findOne({});
    const sampleOutlet = await Outlet.findOne({});
    const sampleUser = await User.findOne({});

    if (!sampleFoodChain || !sampleOutlet) {
      console.log("❌ Missing sample data");
      return;
    }

    console.log(`Using food chain: ${sampleFoodChain.name}`);
    console.log(`Using outlet: ${sampleOutlet.name}`);

    // Get current dishes
    const currentDishes = await Dish.find({
      foodChain: sampleFoodChain._id,
      isAvailable: true,
    }).populate("category", "name");

    console.log(`\nCurrent menu size: ${currentDishes.length} dishes`);

    if (currentDishes.length > 0) {
      console.log("Sample dishes:");
      currentDishes.slice(0, 5).forEach((dish) => {
        console.log(
          `  - ${dish.name} (₹${dish.price}) - ${
            dish.isVeg ? "Veg" : "Non-Veg"
          }`
        );
      });
    }

    // Initialize AI response generator
    const aiGenerator = new AgenticResponseGenerator(
      sampleUser?._id,
      sampleOutlet._id,
      sampleFoodChain._id,
      "en"
    );

    const testQueries = [
      "What do you recommend?",
      "Show me vegetarian options",
      "I want something non-veg",
      "What's your most popular dish?",
      "Show me the menu",
    ];

    console.log("\n=== TESTING AI RESPONSES ===");

    for (let i = 0; i < testQueries.length; i++) {
      const query = testQueries[i];
      console.log(`\n${i + 1}. Testing: "${query}"`);

      const startTime = Date.now();

      try {
        const response = await aiGenerator.processMessage(
          query,
          `test-conversation-${i}`,
          [], // conversation history
          currentDishes
        );

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        console.log(`   ✅ Response Time: ${responseTime}ms`);
        console.log(`   Status: ${response.success ? "Success" : "Failed"}`);

        if (response.success) {
          console.log(
            `   Message: ${response.components?.message?.substring(0, 100)}...`
          );

          if (
            response.components?.recommendations &&
            response.components.recommendations.length > 0
          ) {
            console.log(
              `   Recommendations: ${response.components.recommendations.length} dishes`
            );
            console.log(
              `   Sample: ${response.components.recommendations
                .slice(0, 2)
                .map((d) => d.name)
                .join(", ")}`
            );
          }

          if (responseTime > 5000) {
            console.log(`   ⚠️  Slow response (${responseTime}ms)`);
          } else if (responseTime < 1000) {
            console.log(`   🚀 Fast response (${responseTime}ms)`);
          }
        } else {
          console.log(
            `   ❌ Error: ${response.metadata?.error || "Unknown error"}`
          );
        }
      } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        console.log(
          `   ❌ Exception after ${responseTime}ms: ${error.message}`
        );
      }

      // Small delay between requests
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    console.log("\n=== AI SIMPLE TEST COMPLETED ===");
    console.log("\n✅ RESULTS:");
    console.log(`- Menu Size: ${currentDishes.length} dishes`);
    console.log(`- AI can process menu and respond to queries`);
    console.log(`- All test queries completed`);

    process.exit(0);
  } catch (error) {
    console.error("Error during testing:", error);
    process.exit(1);
  }
};

testAISimple();
