import AuditLog from "../models/AuditLog.js";

/**
 * Middleware to log audit events
 * @param {Object} options - Options for the audit log
 * @param {string} options.action - The action being performed
 * @param {string} options.resourceType - The type of resource being acted upon
 * @param {string} options.description - Description of the action
 */
export const auditLogger = (options) => {
  return async (req, res, next) => {
    // Store the original end method
    const originalEnd = res.end;
    
    // Override the end method
    res.end = async function(chunk, encoding) {
      // Restore the original end method
      res.end = originalEnd;
      
      // Call the original end method
      res.end(chunk, encoding);
      
      try {
        // Only log if user is authenticated
        if (req.user && req.user._id) {
          const statusCode = res.statusCode;
          const status = statusCode >= 200 && statusCode < 400 ? "success" : "failure";
          
          // Create audit log entry
          const auditLog = new AuditLog({
            userId: req.user._id,
            action: options.action,
            resourceType: options.resourceType,
            resourceId: req.params.id || req.body._id || null,
            description: options.description,
            details: {
              method: req.method,
              url: req.originalUrl,
              body: sanitizeBody(req.body),
              params: req.params,
              query: req.query,
              statusCode: statusCode,
            },
            ipAddress: req.ip || req.connection.remoteAddress,
            userAgent: req.headers["user-agent"],
            status: status,
            foodChainId: req.user.foodChain || null,
            outletId: req.body.outletId || req.params.outletId || null,
          });
          
          await auditLog.save();
        }
      } catch (error) {
        console.error("Error creating audit log:", error);
      }
    };
    
    next();
  };
};

/**
 * Sanitize request body to remove sensitive information
 * @param {Object} body - Request body
 * @returns {Object} Sanitized body
 */
const sanitizeBody = (body) => {
  if (!body) return {};
  
  const sanitized = { ...body };
  
  // Remove sensitive fields
  const sensitiveFields = ["password", "token", "secret", "creditCard", "cardNumber", "cvv"];
  
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = "***REDACTED***";
    }
  });
  
  return sanitized;
};

/**
 * Create an audit log entry directly
 * @param {Object} logData - Audit log data
 */
export const createAuditLog = async (logData) => {
  try {
    const auditLog = new AuditLog({
      userId: logData.userId,
      action: logData.action,
      resourceType: logData.resourceType,
      resourceId: logData.resourceId || null,
      description: logData.description,
      details: logData.details || {},
      ipAddress: logData.ipAddress || null,
      userAgent: logData.userAgent || null,
      status: logData.status || "success",
      foodChainId: logData.foodChainId || null,
      outletId: logData.outletId || null,
    });
    
    await auditLog.save();
    return auditLog;
  } catch (error) {
    console.error("Error creating audit log:", error);
    throw error;
  }
};

export default auditLogger;
