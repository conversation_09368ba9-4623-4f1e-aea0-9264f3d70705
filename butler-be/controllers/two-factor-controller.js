import User from "../models/User.js";
import crypto from "crypto";
import { createAuditLog } from "../middlewares/auditLogger.js";
import speakeasy from "speakeasy";
import qrcode from "qrcode";

/**
 * Generate a new 2FA secret for a user
 */
export const generate2FASecret = async (req, res) => {
  try {
    const userId = req.user._id;
    
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }
    
    // Generate a new secret
    const secret = speakeasy.generateSecret({
      name: `<PERSON> App (${user.email})`,
    });
    
    // Save the secret to the user
    user.twoFactorSecret = secret.base32;
    await user.save();
    
    // Generate QR code
    const qrCodeUrl = await qrcode.toDataURL(secret.otpauth_url);
    
    // Create audit log
    await createAuditLog({
      userId: user._id,
      action: "enable_2fa",
      resourceType: "user",
      resourceId: user._id,
      description: "2FA secret generated",
      status: "info",
      foodChainId: user.foodChain || null,
    });
    
    res.status(200).json({
      success: true,
      data: {
        secret: secret.base32,
        qrCode: qrCodeUrl,
      },
      message: "2FA secret generated successfully",
    });
  } catch (error) {
    console.error("Error generating 2FA secret:", error);
    res.status(500).json({
      success: false,
      message: "Error generating 2FA secret",
      error: error.message,
    });
  }
};

/**
 * Verify a 2FA token and enable 2FA for the user
 */
export const verify2FAToken = async (req, res) => {
  try {
    const { token } = req.body;
    const userId = req.user._id;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        message: "Token is required",
      });
    }
    
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }
    
    // Verify the token
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: "base32",
      token,
    });
    
    if (!verified) {
      return res.status(400).json({
        success: false,
        message: "Invalid token",
      });
    }
    
    // Enable 2FA for the user
    user.twoFactorEnabled = true;
    
    // Generate backup codes
    const backupCodes = [];
    for (let i = 0; i < 10; i++) {
      backupCodes.push(crypto.randomBytes(4).toString("hex"));
    }
    user.twoFactorBackupCodes = backupCodes;
    
    await user.save();
    
    // Create audit log
    await createAuditLog({
      userId: user._id,
      action: "enable_2fa",
      resourceType: "user",
      resourceId: user._id,
      description: "2FA enabled",
      status: "success",
      foodChainId: user.foodChain || null,
    });
    
    res.status(200).json({
      success: true,
      data: {
        backupCodes,
      },
      message: "2FA enabled successfully",
    });
  } catch (error) {
    console.error("Error verifying 2FA token:", error);
    res.status(500).json({
      success: false,
      message: "Error verifying 2FA token",
      error: error.message,
    });
  }
};

/**
 * Disable 2FA for a user
 */
export const disable2FA = async (req, res) => {
  try {
    const { token } = req.body;
    const userId = req.user._id;
    
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }
    
    // Verify the token if 2FA is enabled
    if (user.twoFactorEnabled) {
      if (!token) {
        return res.status(400).json({
          success: false,
          message: "Token is required",
        });
      }
      
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: "base32",
        token,
      });
      
      if (!verified) {
        return res.status(400).json({
          success: false,
          message: "Invalid token",
        });
      }
    }
    
    // Disable 2FA for the user
    user.twoFactorEnabled = false;
    user.twoFactorSecret = undefined;
    user.twoFactorBackupCodes = [];
    await user.save();
    
    // Create audit log
    await createAuditLog({
      userId: user._id,
      action: "disable_2fa",
      resourceType: "user",
      resourceId: user._id,
      description: "2FA disabled",
      status: "success",
      foodChainId: user.foodChain || null,
    });
    
    res.status(200).json({
      success: true,
      message: "2FA disabled successfully",
    });
  } catch (error) {
    console.error("Error disabling 2FA:", error);
    res.status(500).json({
      success: false,
      message: "Error disabling 2FA",
      error: error.message,
    });
  }
};

/**
 * Validate a 2FA token during login
 */
export const validate2FAToken = async (req, res) => {
  try {
    const { token, userId } = req.body;
    
    if (!token || !userId) {
      return res.status(400).json({
        success: false,
        message: "Token and userId are required",
      });
    }
    
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }
    
    // Check if 2FA is enabled
    if (!user.twoFactorEnabled) {
      return res.status(400).json({
        success: false,
        message: "2FA is not enabled for this user",
      });
    }
    
    // Verify the token
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: "base32",
      token,
    });
    
    // Check if token is a backup code
    const isBackupCode = user.twoFactorBackupCodes.includes(token);
    
    if (!verified && !isBackupCode) {
      // Create audit log for failed verification
      await createAuditLog({
        userId: user._id,
        action: "verify_2fa",
        resourceType: "user",
        resourceId: user._id,
        description: "Failed 2FA verification attempt",
        status: "failure",
        foodChainId: user.foodChain || null,
      });
      
      return res.status(400).json({
        success: false,
        message: "Invalid token",
      });
    }
    
    // If it's a backup code, remove it from the list
    if (isBackupCode) {
      user.twoFactorBackupCodes = user.twoFactorBackupCodes.filter(
        (code) => code !== token
      );
      await user.save();
    }
    
    // Create audit log
    await createAuditLog({
      userId: user._id,
      action: "verify_2fa",
      resourceType: "user",
      resourceId: user._id,
      description: isBackupCode ? "2FA verified with backup code" : "2FA verified",
      status: "success",
      foodChainId: user.foodChain || null,
    });
    
    res.status(200).json({
      success: true,
      message: "2FA verified successfully",
    });
  } catch (error) {
    console.error("Error validating 2FA token:", error);
    res.status(500).json({
      success: false,
      message: "Error validating 2FA token",
      error: error.message,
    });
  }
};

/**
 * Generate new backup codes for a user
 */
export const generateBackupCodes = async (req, res) => {
  try {
    const { token } = req.body;
    const userId = req.user._id;
    
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }
    
    // Check if 2FA is enabled
    if (!user.twoFactorEnabled) {
      return res.status(400).json({
        success: false,
        message: "2FA is not enabled for this user",
      });
    }
    
    // Verify the token
    if (!token) {
      return res.status(400).json({
        success: false,
        message: "Token is required",
      });
    }
    
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: "base32",
      token,
    });
    
    if (!verified) {
      return res.status(400).json({
        success: false,
        message: "Invalid token",
      });
    }
    
    // Generate new backup codes
    const backupCodes = [];
    for (let i = 0; i < 10; i++) {
      backupCodes.push(crypto.randomBytes(4).toString("hex"));
    }
    user.twoFactorBackupCodes = backupCodes;
    await user.save();
    
    // Create audit log
    await createAuditLog({
      userId: user._id,
      action: "enable_2fa",
      resourceType: "user",
      resourceId: user._id,
      description: "New 2FA backup codes generated",
      status: "success",
      foodChainId: user.foodChain || null,
    });
    
    res.status(200).json({
      success: true,
      data: {
        backupCodes,
      },
      message: "New backup codes generated successfully",
    });
  } catch (error) {
    console.error("Error generating backup codes:", error);
    res.status(500).json({
      success: false,
      message: "Error generating backup codes",
      error: error.message,
    });
  }
};
