import { InventoryItem, InventoryTransaction } from "../models/Inventory.js";
import Dish from "../models/Dish.js";
import Order from "../models/Order.js";
import mongoose from "mongoose";

// Get all inventory items for a food chain
export const getAllInventoryItems = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const {
      outletId,
      category,
      search,
      page = 1,
      limit = 20,
      lowStock,
    } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    // Build query
    let query = { foodChainId };

    // Add outlet filter if provided
    if (outletId) {
      query.outletId = outletId;
    }

    // Add category filter if provided
    if (category) {
      query.category = category;
    }

    // Add low stock filter if requested
    if (lowStock === "true") {
      query.$expr = { $lte: ["$quantity", "$minQuantity"] };
    }

    // Add search filter if provided
    if (search) {
      const searchRegex = new RegExp(search, "i");
      query.$or = [
        { name: searchRegex },
        { description: searchRegex },
        { supplier: searchRegex },
      ];
    }

    // Get total count for pagination
    const totalItems = await InventoryItem.countDocuments(query);

    // Get inventory items with pagination
    const inventoryItems = await InventoryItem.find(query)
      .sort({ name: 1 })
      .skip(skip)
      .limit(limitNum)
      .populate({
        path: "dishesUsedIn",
        select: "name",
      })
      .populate({
        path: "outletId",
        select: "address",
      });
    // Add virtual properties
    const itemsWithVirtuals = inventoryItems.map((item) => {
      const plainItem = item.toObject({ virtuals: true });
      return {
        ...plainItem,
        isLowStock: item.isLowStock,
        isExpired: item.isExpired,
      };
    });

    res.status(200).json({
      success: true,
      data: {
        items: itemsWithVirtuals,
        pagination: {
          total: totalItems,
          page: pageNum,
          limit: limitNum,
          pages: Math.ceil(totalItems / limitNum),
        },
      },
    });
  } catch (error) {
    console.error("Error in getAllInventoryItems:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching inventory items",
      error: error.message,
    });
  }
};

// Get a single inventory item
export const getInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const inventoryItem = await InventoryItem.findOne({
      _id: id,
      foodChainId,
    }).populate({
      path: "dishesUsedIn",
      select: "name",
    });

    if (!inventoryItem) {
      return res.status(404).json({
        success: false,
        message: "Inventory item not found",
      });
    }

    // Add virtual properties
    const itemWithVirtuals = {
      ...inventoryItem.toObject({ virtuals: true }),
      isLowStock: inventoryItem.isLowStock,
      isExpired: inventoryItem.isExpired,
    };

    res.status(200).json({
      success: true,
      data: itemWithVirtuals,
    });
  } catch (error) {
    console.error("Error in getInventoryItem:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching inventory item",
      error: error.message,
    });
  }
};

// Create a new inventory item
export const createInventoryItem = async (req, res) => {
  try {
    const {
      name,
      description,
      category,
      unit,
      quantity,
      minQuantity,
      costPerUnit,
      supplier,
      supplierContact,
      location,
      expiryDate,
      outletId,
    } = req.body;

    const foodChainId = req.user.foodChain;

    // Validate required fields
    if (!name || !unit || quantity === undefined || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Name, unit, quantity, and outletId are required",
      });
    }

    // Create new inventory item
    const inventoryItem = new InventoryItem({
      name,
      description,
      category: category || "ingredient",
      unit,
      quantity,
      minQuantity: minQuantity || 10,
      costPerUnit,
      supplier,
      supplierContact,
      location,
      expiryDate,
      outletId,
      foodChainId,
      lastRestocked: Date.now(),
    });

    await inventoryItem.save();

    // Create an initial transaction for the addition
    const transaction = new InventoryTransaction({
      itemId: inventoryItem._id,
      type: "addition",
      quantity,
      previousQuantity: 0,
      newQuantity: quantity,
      reason: "Initial stock",
      userId: req.user._id,
      outletId,
      foodChainId,
    });

    await transaction.save();

    res.status(201).json({
      success: true,
      message: "Inventory item created successfully",
      data: inventoryItem,
    });
  } catch (error) {
    console.error("Error in createInventoryItem:", error);
    res.status(500).json({
      success: false,
      message: "Error creating inventory item",
      error: error.message,
    });
  }
};

// Update an inventory item
export const updateInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      category,
      unit,
      minQuantity,
      costPerUnit,
      supplier,
      supplierContact,
      location,
      expiryDate,
    } = req.body;

    const foodChainId = req.user.foodChain;

    // Find the inventory item
    const inventoryItem = await InventoryItem.findOne({
      _id: id,
      foodChainId,
    });

    if (!inventoryItem) {
      return res.status(404).json({
        success: false,
        message: "Inventory item not found",
      });
    }

    // Update fields
    if (name) inventoryItem.name = name;
    if (description !== undefined) inventoryItem.description = description;
    if (category) inventoryItem.category = category;
    if (unit) inventoryItem.unit = unit;
    if (minQuantity !== undefined) inventoryItem.minQuantity = minQuantity;
    if (costPerUnit !== undefined) inventoryItem.costPerUnit = costPerUnit;
    if (supplier !== undefined) inventoryItem.supplier = supplier;
    if (supplierContact !== undefined)
      inventoryItem.supplierContact = supplierContact;
    if (location !== undefined) inventoryItem.location = location;
    if (expiryDate !== undefined) inventoryItem.expiryDate = expiryDate;

    inventoryItem.updatedAt = Date.now();

    await inventoryItem.save();

    res.status(200).json({
      success: true,
      message: "Inventory item updated successfully",
      data: inventoryItem,
    });
  } catch (error) {
    console.error("Error in updateInventoryItem:", error);
    res.status(500).json({
      success: false,
      message: "Error updating inventory item",
      error: error.message,
    });
  }
};

// Delete an inventory item
export const deleteInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    // Check if the item is used in any dishes
    const dishesUsingItem = await Dish.find({
      foodChain: foodChainId,
      "ingredients.inventoryItemId": id,
    });

    if (dishesUsingItem.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Cannot delete inventory item as it is used in dishes",
        data: {
          dishes: dishesUsingItem.map((dish) => ({
            id: dish._id,
            name: dish.name,
          })),
        },
      });
    }

    // Delete the inventory item
    const result = await InventoryItem.deleteOne({
      _id: id,
      foodChainId,
    });

    if (result.deletedCount === 0) {
      return res.status(404).json({
        success: false,
        message: "Inventory item not found",
      });
    }

    // Delete associated transactions
    await InventoryTransaction.deleteMany({
      itemId: id,
      foodChainId,
    });

    res.status(200).json({
      success: true,
      message: "Inventory item deleted successfully",
    });
  } catch (error) {
    console.error("Error in deleteInventoryItem:", error);
    res.status(500).json({
      success: false,
      message: "Error deleting inventory item",
      error: error.message,
    });
  }
};

// Update inventory quantity
export const updateInventoryQuantity = async (req, res) => {
  try {
    const { id } = req.params;
    const { quantity, type, reason } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate required fields
    if (quantity === undefined || !type) {
      return res.status(400).json({
        success: false,
        message: "Quantity and type are required",
      });
    }

    // Validate type
    const validTypes = [
      "addition",
      "deduction",
      "adjustment",
      "waste",
      "transfer",
    ];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        message: `Invalid type. Type must be one of: ${validTypes.join(", ")}`,
      });
    }

    // Find the inventory item
    const inventoryItem = await InventoryItem.findOne({
      _id: id,
      foodChainId,
    });

    if (!inventoryItem) {
      return res.status(404).json({
        success: false,
        message: "Inventory item not found",
      });
    }

    const previousQuantity = inventoryItem.quantity;
    let newQuantity;

    // Calculate new quantity based on type
    switch (type) {
      case "addition":
        newQuantity = previousQuantity + quantity;
        break;
      case "deduction":
      case "waste":
        newQuantity = previousQuantity - quantity;
        if (newQuantity < 0) {
          return res.status(400).json({
            success: false,
            message: "Insufficient quantity available",
          });
        }
        break;
      case "adjustment":
        newQuantity = quantity;
        break;
      case "transfer":
        // For transfer, additional validation is needed
        if (!req.body.transferToOutletId) {
          return res.status(400).json({
            success: false,
            message: "Transfer requires a destination outlet",
          });
        }
        newQuantity = previousQuantity - quantity;
        if (newQuantity < 0) {
          return res.status(400).json({
            success: false,
            message: "Insufficient quantity available for transfer",
          });
        }
        break;
    }

    // Update the inventory item
    inventoryItem.quantity = newQuantity;
    inventoryItem.updatedAt = Date.now();
    if (type === "addition") {
      inventoryItem.lastRestocked = Date.now();
    }

    await inventoryItem.save();

    // Create a transaction record
    const transaction = new InventoryTransaction({
      itemId: id,
      type,
      quantity,
      previousQuantity,
      newQuantity,
      reason: reason || `Manual ${type}`,
      userId: req.user._id,
      outletId: inventoryItem.outletId,
      foodChainId,
      transferToOutletId:
        type === "transfer" ? req.body.transferToOutletId : undefined,
    });

    await transaction.save();

    // If it's a transfer, create a new inventory item or update existing one in the destination outlet
    if (type === "transfer") {
      const { transferToOutletId } = req.body;

      // Check if the item already exists in the destination outlet
      let destinationItem = await InventoryItem.findOne({
        name: inventoryItem.name,
        outletId: transferToOutletId,
        foodChainId,
      });

      if (destinationItem) {
        // Update existing item
        const destPreviousQuantity = destinationItem.quantity;
        destinationItem.quantity += quantity;
        destinationItem.updatedAt = Date.now();
        destinationItem.lastRestocked = Date.now();
        await destinationItem.save();

        // Create a transaction for the destination
        const destTransaction = new InventoryTransaction({
          itemId: destinationItem._id,
          type: "addition",
          quantity,
          previousQuantity: destPreviousQuantity,
          newQuantity: destinationItem.quantity,
          reason: `Transfer from ${inventoryItem.outletId}`,
          userId: req.user._id,
          outletId: transferToOutletId,
          foodChainId,
        });

        await destTransaction.save();
      } else {
        // Create new item in destination outlet
        const newItem = new InventoryItem({
          name: inventoryItem.name,
          description: inventoryItem.description,
          category: inventoryItem.category,
          unit: inventoryItem.unit,
          quantity,
          minQuantity: inventoryItem.minQuantity,
          costPerUnit: inventoryItem.costPerUnit,
          supplier: inventoryItem.supplier,
          supplierContact: inventoryItem.supplierContact,
          location: inventoryItem.location,
          expiryDate: inventoryItem.expiryDate,
          outletId: transferToOutletId,
          foodChainId,
          lastRestocked: Date.now(),
        });

        await newItem.save();

        // Create a transaction for the new item
        const newItemTransaction = new InventoryTransaction({
          itemId: newItem._id,
          type: "addition",
          quantity,
          previousQuantity: 0,
          newQuantity: quantity,
          reason: `Transfer from ${inventoryItem.outletId}`,
          userId: req.user._id,
          outletId: transferToOutletId,
          foodChainId,
        });

        await newItemTransaction.save();
      }
    }

    res.status(200).json({
      success: true,
      message: `Inventory quantity ${
        type === "adjustment"
          ? "adjusted"
          : type === "addition"
          ? "added"
          : "deducted"
      } successfully`,
      data: {
        inventoryItem,
        transaction,
      },
    });
  } catch (error) {
    console.error("Error in updateInventoryQuantity:", error);
    res.status(500).json({
      success: false,
      message: "Error updating inventory quantity",
      error: error.message,
    });
  }
};

// Get inventory transactions
export const getInventoryTransactions = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const {
      itemId,
      outletId,
      type,
      startDate,
      endDate,
      page = 1,
      limit = 20,
    } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    // Build query
    let query = { foodChainId };

    // Add item filter if provided
    if (itemId) {
      query.itemId = itemId;
    }

    // Add outlet filter if provided
    if (outletId) {
      query.outletId = outletId;
    }

    // Add type filter if provided
    if (type) {
      query.type = type;
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) {
        query.timestamp.$gte = new Date(startDate);
      }
      if (endDate) {
        query.timestamp.$lte = new Date(endDate);
      }
    }

    // Get total count for pagination
    const totalTransactions = await InventoryTransaction.countDocuments(query);

    // Get transactions with pagination
    const transactions = await InventoryTransaction.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limitNum)
      .populate("itemId", "name unit")
      .populate("userId", "name")
      .populate("outletId", "name")
      .populate("transferToOutletId", "name");

    res.status(200).json({
      success: true,
      data: {
        transactions,
        pagination: {
          total: totalTransactions,
          page: pageNum,
          limit: limitNum,
          pages: Math.ceil(totalTransactions / limitNum),
        },
      },
    });
  } catch (error) {
    console.error("Error in getInventoryTransactions:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching inventory transactions",
      error: error.message,
    });
  }
};

// Get inventory categories
export const getInventoryCategories = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;

    // Get distinct categories
    const categories = await InventoryItem.distinct("category", {
      foodChainId,
    });

    res.status(200).json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error("Error in getInventoryCategories:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching inventory categories",
      error: error.message,
    });
  }
};

// Get low stock items
export const getLowStockItems = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { outletId } = req.query;

    // Build query
    let query = {
      foodChainId,
      $expr: { $lte: ["$quantity", "$minQuantity"] },
    };

    // Add outlet filter if provided
    if (outletId) {
      query.outletId = outletId;
    }

    // Get low stock items
    const lowStockItems = await InventoryItem.find(query)
      .sort({ quantity: 1 })
      .populate({
        path: "dishesUsedIn",
        select: "name",
      });

    res.status(200).json({
      success: true,
      data: lowStockItems,
    });
  } catch (error) {
    console.error("Error in getLowStockItems:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching low stock items",
      error: error.message,
    });
  }
};

// Process inventory for an order
export const processOrderInventory = async (orderId, status, userId) => {
  try {
    const order = await Order.findById(orderId).populate({
      path: "items.dishId",
      populate: {
        path: "ingredients.inventoryItemId",
      },
    });

    if (!order) {
      console.error(`Order ${orderId} not found`);
      return { success: false, message: "Order not found" };
    }

    // Process inventory based on order status
    // Deduct inventory for any status that indicates order is being processed
    const statusesThatRequireInventory = [
      "preparing",
      "ready",
      "completed",
      "confirmed",
    ];

    if (statusesThatRequireInventory.includes(status)) {
      // Only deduct inventory if it hasn't been processed yet
      if (order.inventoryProcessed) {
        console.log(
          `Inventory already processed for order ${orderId}, skipping deduction`
        );
        return { success: true, message: "Inventory already processed" };
      }
      // Deduct inventory when order is moved to any processing status
      for (const orderItem of order.items) {
        const dish = orderItem.dishId;
        const quantity = orderItem.quantity;

        if (dish && dish.ingredients && dish.ingredients.length > 0) {
          for (const ingredient of dish.ingredients) {
            if (ingredient.inventoryItemId) {
              const inventoryItem = await InventoryItem.findById(
                ingredient.inventoryItemId
              );

              if (inventoryItem) {
                const deductionAmount = ingredient.quantity * quantity;
                const previousQuantity = inventoryItem.quantity;
                const newQuantity = previousQuantity - deductionAmount;

                if (newQuantity < 0) {
                  console.warn(
                    `Insufficient inventory for ${inventoryItem.name}, proceeding anyway`
                  );
                  // We'll still proceed with the deduction even if it goes negative
                }

                // Update inventory
                inventoryItem.quantity = Math.max(0, newQuantity); // Don't go below 0
                inventoryItem.updatedAt = Date.now();
                await inventoryItem.save();

                // Create transaction record
                const transaction = new InventoryTransaction({
                  itemId: inventoryItem._id,
                  type: "deduction",
                  quantity: deductionAmount,
                  previousQuantity,
                  newQuantity: inventoryItem.quantity,
                  reason: `Order #${order.orderNumber}`,
                  orderId: order._id,
                  userId: userId || order.userId,
                  outletId: order.outletId,
                  foodChainId: order.foodChainId,
                });

                await transaction.save();
              }
            }
          }
        }
      }

      // Mark inventory as processed
      order.inventoryProcessed = true;
      await order.save();

      return { success: true, message: "Inventory deducted for order" };
    } else if (status === "cancelled" || status === "rejected") {
      // Restore inventory when order is cancelled or rejected
      // First check if inventory was already deducted (look for deduction transactions)
      const existingTransactions = await InventoryTransaction.find({
        orderId: order._id,
        type: "deduction",
      });

      if (existingTransactions.length > 0) {
        // Inventory was deducted, so restore it
        for (const transaction of existingTransactions) {
          const inventoryItem = await InventoryItem.findById(
            transaction.itemId
          );

          if (inventoryItem) {
            const previousQuantity = inventoryItem.quantity;
            const newQuantity = previousQuantity + transaction.quantity;

            // Update inventory
            inventoryItem.quantity = newQuantity;
            inventoryItem.updatedAt = Date.now();
            await inventoryItem.save();

            // Create transaction record for the restoration
            const restoreTransaction = new InventoryTransaction({
              itemId: inventoryItem._id,
              type: "addition",
              quantity: transaction.quantity,
              previousQuantity,
              newQuantity,
              reason: `Order #${order.orderNumber} ${status}`,
              orderId: order._id,
              userId: userId || order.userId,
              outletId: order.outletId,
              foodChainId: order.foodChainId,
            });

            await restoreTransaction.save();
          }
        }
        return {
          success: true,
          message: "Inventory restored for cancelled order",
        };
      }
    }

    return { success: true, message: "No inventory changes needed" };
  } catch (error) {
    console.error("Error processing order inventory:", error);
    return { success: false, message: error.message };
  }
};

// Link inventory items to a dish
export const linkInventoryToDish = async (req, res) => {
  try {
    const { dishId } = req.params;
    const { ingredients } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate required fields
    if (!ingredients || !Array.isArray(ingredients)) {
      return res.status(400).json({
        success: false,
        message: "Ingredients array is required",
      });
    }

    // Find the dish
    const dish = await Dish.findOne({
      _id: dishId,
      foodChain: foodChainId,
    });

    if (!dish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found",
      });
    }

    // Process each ingredient
    const updatedIngredients = [];
    for (const ingredient of ingredients) {
      if (!ingredient.inventoryItemId) {
        // If no inventory item is linked, just add the ingredient as is
        updatedIngredients.push(ingredient);
        continue;
      }

      // Verify the inventory item exists and belongs to the food chain
      const inventoryItem = await InventoryItem.findOne({
        _id: ingredient.inventoryItemId,
        foodChainId,
      });

      if (!inventoryItem) {
        return res.status(404).json({
          success: false,
          message: `Inventory item ${ingredient.inventoryItemId} not found`,
        });
      }

      // Add the dish to the inventory item's dishesUsedIn array if not already there
      if (!inventoryItem.dishesUsedIn.includes(dishId)) {
        inventoryItem.dishesUsedIn.push(dishId);
        await inventoryItem.save();
      }

      // Add the ingredient to the updated list
      updatedIngredients.push({
        inventoryItemId: ingredient.inventoryItemId,
        name: ingredient.name || inventoryItem.name,
        quantity: ingredient.quantity,
        unit: ingredient.unit || inventoryItem.unit,
        isOptional: ingredient.isOptional || false,
        canBeExcluded: ingredient.canBeExcluded || false,
      });
    }

    // Update the dish with the new ingredients
    dish.ingredients = updatedIngredients;
    dish.updatedAt = Date.now();
    await dish.save();

    res.status(200).json({
      success: true,
      message: "Dish ingredients updated successfully",
      data: dish,
    });
  } catch (error) {
    console.error("Error in linkInventoryToDish:", error);
    res.status(500).json({
      success: false,
      message: "Error linking inventory to dish",
      error: error.message,
    });
  }
};

// Get inventory summary
export const getInventorySummary = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { outletId } = req.query;

    // Build query
    let query = { foodChainId };
    if (outletId) {
      query.outletId = outletId;
    }

    // Get counts
    const totalItems = await InventoryItem.countDocuments(query);
    const lowStockItems = await InventoryItem.countDocuments({
      ...query,
      $expr: { $lte: ["$quantity", "$minQuantity"] },
    });
    const expiredItems = await InventoryItem.countDocuments({
      ...query,
      expiryDate: { $lt: new Date() },
    });

    // Get category breakdown
    const categoryAggregation = await InventoryItem.aggregate([
      { $match: query },
      { $group: { _id: "$category", count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    // Get recent transactions
    const recentTransactions = await InventoryTransaction.find({ foodChainId })
      .sort({ timestamp: -1 })
      .limit(5)
      .populate("itemId", "name")
      .populate("userId", "name")
      .populate("outletId", "name");

    // Calculate total inventory value
    const valueAggregation = await InventoryItem.aggregate([
      { $match: query },
      { $match: { costPerUnit: { $exists: true, $ne: null } } },
      {
        $group: {
          _id: null,
          totalValue: { $sum: { $multiply: ["$quantity", "$costPerUnit"] } },
          averageCost: { $avg: "$costPerUnit" },
        },
      },
    ]);

    const totalValue =
      valueAggregation.length > 0 ? valueAggregation[0].totalValue : 0;
    const averageCost =
      valueAggregation.length > 0 ? valueAggregation[0].averageCost : 0;

    res.status(200).json({
      success: true,
      data: {
        totalItems,
        lowStockItems,
        expiredItems,
        categories: categoryAggregation.map((cat) => ({
          name: cat._id,
          count: cat.count,
        })),
        recentTransactions,
        totalValue,
        averageCost,
      },
    });
  } catch (error) {
    console.error("Error in getInventorySummary:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching inventory summary",
      error: error.message,
    });
  }
};
