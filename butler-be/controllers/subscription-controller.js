import SubscriptionPlan from "../models/SubscriptionPlan.js";
import Subscription from "../models/Subscription.js";
import SubscriptionInvoice from "../models/SubscriptionInvoice.js";
import FoodChain from "../models/FoodChain.js";
import Outlet from "../models/Outlet.js";
import User from "../models/User.js";
import {
  createSubscriptionPaymentLink,
  getSubscriptionPaymentLinkDetails,
} from "../utils/razorpaySubscription.js";
import {
  createSubscriptionInvoice,
  getInvoiceDetails as getRazorpayInvoiceDetails,
  verifyInvoicePaymentSignature,
} from "../utils/razorpayInvoice.js";
import { saveWebhookForRetry } from "../services/webhook-retry-service.js";
import {
  createInvoicePaymentNotification,
  createSubscriptionStatusNotification,
  createTrialEndingNotification,
  createInvoiceDueNotification,
} from "../services/notification-service.js";
import mongoose from "mongoose";
import crypto from "crypto";

/**
 * Create a new subscription plan (Super Admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createSubscriptionPlan = async (req, res) => {
  try {
    // Only super admins can create subscription plans
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can create subscription plans",
      });
    }

    const { name, description, price, interval, features, isDefault } =
      req.body;

    // Validate required fields
    if (!name || !price) {
      return res.status(400).json({
        success: false,
        message: "Name and price are required",
      });
    }

    // If this plan is set as default, unset any existing default plans
    if (isDefault) {
      await SubscriptionPlan.updateMany(
        { isDefault: true },
        { isDefault: false }
      );
    }

    // Create new subscription plan
    const subscriptionPlan = new SubscriptionPlan({
      name,
      description,
      price,
      interval: interval || "monthly",
      features: features || [],
      isDefault: isDefault || false,
    });

    await subscriptionPlan.save();

    res.status(201).json({
      success: true,
      message: "Subscription plan created successfully",
      data: subscriptionPlan,
    });
  } catch (error) {
    console.error("Error creating subscription plan:", error);
    res.status(500).json({
      success: false,
      message: "Error creating subscription plan",
      error: error.message,
    });
  }
};

/**
 * Get all subscription plans
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllSubscriptionPlans = async (req, res) => {
  try {
    const { active } = req.query;

    // Build query
    const query = {};
    if (active === "true") {
      query.isActive = true;
    }

    const subscriptionPlans = await SubscriptionPlan.find(query).sort({
      price: 1,
    });

    res.status(200).json({
      success: true,
      data: subscriptionPlans,
    });
  } catch (error) {
    console.error("Error fetching subscription plans:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching subscription plans",
      error: error.message,
    });
  }
};

/**
 * Update a subscription plan (Super Admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateSubscriptionPlan = async (req, res) => {
  try {
    // Only super admins can update subscription plans
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can update subscription plans",
      });
    }

    const { planId } = req.params;
    const {
      name,
      description,
      price,
      interval,
      features,
      isActive,
      isDefault,
    } = req.body;

    // Find the subscription plan
    const subscriptionPlan = await SubscriptionPlan.findById(planId);
    if (!subscriptionPlan) {
      return res.status(404).json({
        success: false,
        message: "Subscription plan not found",
      });
    }

    // If this plan is being set as default, unset any existing default plans
    if (isDefault) {
      await SubscriptionPlan.updateMany(
        { _id: { $ne: planId }, isDefault: true },
        { isDefault: false }
      );
    }

    // Update fields
    if (name) subscriptionPlan.name = name;
    if (description !== undefined) subscriptionPlan.description = description;
    if (price) subscriptionPlan.price = price;
    if (interval) subscriptionPlan.interval = interval;
    if (features) subscriptionPlan.features = features;
    if (isActive !== undefined) subscriptionPlan.isActive = isActive;
    if (isDefault !== undefined) subscriptionPlan.isDefault = isDefault;

    subscriptionPlan.updatedAt = Date.now();

    await subscriptionPlan.save();

    res.status(200).json({
      success: true,
      message: "Subscription plan updated successfully",
      data: subscriptionPlan,
    });
  } catch (error) {
    console.error("Error updating subscription plan:", error);
    res.status(500).json({
      success: false,
      message: "Error updating subscription plan",
      error: error.message,
    });
  }
};

/**
 * Create a new subscription for a food chain (Super Admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createSubscription = async (req, res) => {
  try {
    // Only super admins can create subscriptions
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can create subscriptions",
      });
    }

    const { foodChainId, planId, outletCount, autoRenew } = req.body;

    // Validate required fields
    if (!foodChainId || !planId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and plan ID are required",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Find the subscription plan
    const subscriptionPlan = await SubscriptionPlan.findById(planId);
    if (!subscriptionPlan) {
      return res.status(404).json({
        success: false,
        message: "Subscription plan not found",
      });
    }

    // Check if the food chain already has an active subscription
    const existingSubscription = await Subscription.findOne({
      foodChainId,
      status: "active",
    });

    if (existingSubscription) {
      return res.status(400).json({
        success: false,
        message: "Food chain already has an active subscription",
      });
    }

    // Get actual outlet count if not provided
    let actualOutletCount = outletCount;
    if (!actualOutletCount) {
      const outlets = await Outlet.countDocuments({ foodChain: foodChainId });
      actualOutletCount = outlets;
    }

    if (actualOutletCount < 1) {
      return res.status(400).json({
        success: false,
        message: "Outlet count must be at least 1",
      });
    }

    // Calculate total amount
    const totalAmount = subscriptionPlan.price * actualOutletCount;

    // Set start date to first day of current month
    const startDate = new Date();
    startDate.setDate(1); // First day of month
    startDate.setHours(0, 0, 0, 0); // Start of day

    // Set end date to last day of current month if monthly subscription
    let endDate = null;
    if (subscriptionPlan.interval === "monthly") {
      endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0); // Last day of month
      endDate.setHours(23, 59, 59, 999); // End of day
    } else if (subscriptionPlan.interval === "quarterly") {
      endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 3);
      endDate.setDate(0); // Last day of month
      endDate.setHours(23, 59, 59, 999); // End of day
    } else if (subscriptionPlan.interval === "yearly") {
      endDate = new Date(startDate);
      endDate.setFullYear(endDate.getFullYear() + 1);
      endDate.setDate(0); // Last day of month
      endDate.setHours(23, 59, 59, 999); // End of day
    }

    // Create new subscription
    const subscription = new Subscription({
      foodChainId,
      planId,
      outletCount: actualOutletCount,
      totalAmount,
      status: "pending",
      startDate,
      endDate,
      autoRenew: autoRenew !== undefined ? autoRenew : true, // Default to true
    });

    await subscription.save();

    // Set trial end date to 15 days from now
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 15);
    subscription.trialEndDate = trialEndDate;

    // Initialize plan history
    subscription.planHistory = [
      {
        planId: subscriptionPlan._id,
        name: subscriptionPlan.name,
        price: subscriptionPlan.price,
        startDate: startDate,
        endDate: null, // Will be set when plan changes or subscription ends
      },
    ];

    await subscription.save();

    // Note: We don't create an invoice immediately.
    // The first 15 days are free (trial period).
    // Invoices will be generated at the beginning of the next month with prorated amounts
    // based on the number of days the plan was used after the trial period.

    res.status(201).json({
      success: true,
      message:
        "Subscription created successfully with a 15-day trial period. You will be billed for the prorated amount at the beginning of next month.",
      data: {
        subscription,
      },
    });
  } catch (error) {
    console.error("Error creating subscription:", error);
    res.status(500).json({
      success: false,
      message: "Error creating subscription",
      error: error.message,
    });
  }
};

/**
 * Get all subscriptions (Super Admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllSubscriptions = async (req, res) => {
  try {
    // Only super admins can view all subscriptions
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can view all subscriptions",
      });
    }

    const { status, page = 1, limit = 10 } = req.query;

    // Build query
    const query = {};
    if (status) {
      query.status = status;
    }

    // Pagination
    const skip = (page - 1) * limit;

    const subscriptions = await Subscription.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate("foodChainId", "name email phone")
      .populate("planId", "name price interval")
      .populate("lastInvoiceId", "invoiceNumber status amount dueDate");

    const total = await Subscription.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        subscriptions,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching subscriptions",
      error: error.message,
    });
  }
};

/**
 * Get food chain subscription (Admin or Super Admin)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFoodChainSubscription = async (req, res) => {
  try {
    const { foodChainId } = req.params;

    // Check if user has permission to view this food chain's subscription
    if (
      req.user.role !== "super_admin" &&
      (!req.user.foodChain || req.user.foodChain.toString() !== foodChainId)
    ) {
      return res.status(403).json({
        success: false,
        message:
          "You don't have permission to view this food chain's subscription",
      });
    }

    // Find the subscription
    const subscription = await Subscription.findOne({ foodChainId })
      .sort({ createdAt: -1 })
      .populate("planId", "name price interval features")
      .populate(
        "lastInvoiceId",
        "invoiceNumber status amount dueDate paymentLink"
      );

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: "No subscription found for this food chain",
      });
    }

    // Get all invoices for this subscription
    const invoices = await SubscriptionInvoice.find({ foodChainId })
      .sort({ createdAt: -1 })
      .limit(10);

    res.status(200).json({
      success: true,
      data: {
        subscription,
        invoices,
      },
    });
  } catch (error) {
    console.error("Error fetching food chain subscription:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching food chain subscription",
      error: error.message,
    });
  }
};

/**
 * Get subscription invoice details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getInvoiceDetails = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    // Find the invoice
    const invoice = await SubscriptionInvoice.findById(invoiceId)
      .populate("subscriptionId", "outletCount status startDate endDate")
      .populate({
        path: "subscriptionId",
        populate: {
          path: "planId",
          select: "name price interval features",
        },
      })
      .populate("foodChainId", "name email phone address");

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found",
      });
    }

    // Check if user has permission to view this invoice
    if (
      req.user.role !== "super_admin" &&
      (!req.user.foodChain ||
        req.user.foodChain.toString() !== invoice.foodChainId._id.toString())
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to view this invoice",
      });
    }

    // If invoice has a Razorpay invoice ID, get the latest status
    if (invoice.razorpayInvoiceId) {
      try {
        const razorpayInvoiceDetails = await getRazorpayInvoiceDetails(
          invoice.razorpayInvoiceId
        );

        // Update invoice status if it has changed
        // Map Razorpay invoice status to our status
        let newStatus = invoice.status;
        if (razorpayInvoiceDetails.status === "paid") {
          newStatus = "paid";
        } else if (razorpayInvoiceDetails.status === "issued") {
          newStatus = "created";
        } else if (razorpayInvoiceDetails.status === "cancelled") {
          newStatus = "cancelled";
        }

        if (newStatus !== invoice.status) {
          invoice.status = newStatus;

          // If payment is completed, update paid date
          if (newStatus === "paid" && !invoice.paidDate) {
            invoice.paidDate = new Date();

            // Also update the subscription status to active if it was pending
            const subscription = await Subscription.findById(
              invoice.subscriptionId
            );
            if (subscription && subscription.status === "pending") {
              subscription.status = "active";
              await subscription.save();
            }
          }

          await invoice.save();
        }
      } catch (error) {
        console.error("Error fetching Razorpay invoice details:", error);
        // Continue without updating status
      }
    }

    res.status(200).json({
      success: true,
      data: invoice,
    });
  } catch (error) {
    console.error("Error fetching invoice details:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching invoice details",
      error: error.message,
    });
  }
};

/**
 * Create a new invoice for an existing subscription (Super Admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createInvoice = async (req, res) => {
  try {
    // Only super admins can create invoices
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can create invoices",
      });
    }

    const { subscriptionId } = req.params;
    const { amount, dueDate, notes } = req.body;

    // Find the subscription
    const subscription = await Subscription.findById(subscriptionId)
      .populate("planId", "name price interval")
      .populate("foodChainId", "name email phone");

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found",
      });
    }

    // Generate invoice number
    const invoiceNumber = `INV-${Date.now()}-${subscription.foodChainId._id
      .toString()
      .substring(0, 6)}`;

    // Create invoice
    const invoice = new SubscriptionInvoice({
      subscriptionId,
      foodChainId: subscription.foodChainId._id,
      invoiceNumber,
      amount: amount || subscription.totalAmount,
      notes,
      items: [
        {
          description: `${subscription.planId.name} - ${subscription.planId.interval} subscription`,
          quantity: subscription.outletCount,
          unitPrice: subscription.planId.price,
          amount: amount || subscription.totalAmount,
        },
      ],
    });

    await invoice.save();

    // Update subscription with latest invoice ID
    subscription.lastInvoiceId = invoice._id;
    await subscription.save();

    // Create Razorpay invoice instead of payment link
    const razorpayInvoice = await createSubscriptionInvoice(
      invoice,
      subscription.foodChainId
    );

    // Update invoice with Razorpay invoice details
    invoice.razorpayInvoiceId = razorpayInvoice.id;
    invoice.paymentLink = razorpayInvoice.short_url;
    await invoice.save();

    res.status(201).json({
      success: true,
      message: "Invoice created successfully",
      data: {
        invoice,
        paymentLink: razorpayInvoice.short_url,
        invoiceId: razorpayInvoice.id,
      },
    });
  } catch (error) {
    console.error("Error creating invoice:", error);
    res.status(500).json({
      success: false,
      message: "Error creating invoice",
      error: error.message,
    });
  }
};

/**
 * Handle subscription payment webhook from Razorpay
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
/**
 * Handle successful payment webhook
 * @param {Object} payload - The webhook payload
 */
const handlePaymentSuccess = async (payload) => {
  try {
    if (!payload.payment || !payload.payment.entity) {
      console.error("Invalid payment payload");
      return;
    }

    const paymentEntity = payload.payment.entity;

    // Find the invoice using notes from the payment
    if (!paymentEntity.notes || !paymentEntity.notes.invoiceId) {
      console.error("Invoice ID not found in payment notes");
      return;
    }

    const invoiceId = paymentEntity.notes.invoiceId;
    const invoice = await SubscriptionInvoice.findById(invoiceId);

    if (!invoice) {
      console.error(`Invoice not found: ${invoiceId}`);
      return;
    }

    // Update invoice status
    invoice.status = "paid";
    invoice.paidDate = new Date();
    invoice.razorpayPaymentId = paymentEntity.id;

    // Add payment details to history
    if (!invoice.paymentHistory) {
      invoice.paymentHistory = [];
    }

    invoice.paymentHistory.push({
      status: "success",
      paymentId: paymentEntity.id,
      amount: paymentEntity.amount / 100, // Convert from paise to rupees
      method: paymentEntity.method || "unknown",
      timestamp: new Date(),
      details: `Payment ${paymentEntity.status} via ${
        paymentEntity.method || "unknown"
      }`,
    });

    await invoice.save();

    // Update subscription status
    const subscription = await Subscription.findById(invoice.subscriptionId);
    if (subscription && subscription.status === "pending") {
      subscription.status = "active";
      await subscription.save();
      console.log(`Subscription ${subscription._id} activated after payment`);

      // Create subscription status notification
      await createSubscriptionStatusNotification(subscription, "active", {
        invoiceId: invoice._id,
        invoiceNumber: invoice.invoiceNumber,
        paymentId: paymentEntity.id,
        amount: invoice.amount,
      });
    }

    // Create payment success notification
    await createInvoicePaymentNotification(invoice, "paid", {
      paymentId: paymentEntity.id,
      method: paymentEntity.method || "unknown",
      amount: paymentEntity.amount / 100,
    });

    console.log(`Payment success processed for invoice ${invoiceId}`);
  } catch (error) {
    console.error(`Error processing payment success for invoice:`, error);
    // Save for retry
    await saveWebhookForRetry({
      event: "payment.captured",
      payload: payload,
      error: error,
    });
  }
};

/**
 * Handle failed payment webhook
 * @param {Object} payload - The webhook payload
 */
const handlePaymentFailure = async (payload) => {
  try {
    if (!payload.payment || !payload.payment.entity) {
      console.error("Invalid payment payload");
      return;
    }

    const paymentEntity = payload.payment.entity;

    // Find the invoice using notes from the payment
    if (!paymentEntity.notes || !paymentEntity.notes.invoiceId) {
      console.error("Invoice ID not found in payment notes");
      return;
    }

    const invoiceId = paymentEntity.notes.invoiceId;
    const invoice = await SubscriptionInvoice.findById(invoiceId);

    if (!invoice) {
      console.error(`Invoice not found: ${invoiceId}`);
      return;
    }

    // Don't change status if already paid
    if (invoice.status === "paid") {
      console.log(
        `Invoice ${invoiceId} already paid, ignoring failure webhook`
      );
      return;
    }

    // Update invoice status
    invoice.status = "failed";

    // Add payment attempt to history
    if (!invoice.paymentHistory) {
      invoice.paymentHistory = [];
    }

    const errorDetails =
      paymentEntity.error_description ||
      paymentEntity.error_code ||
      "Unknown error";

    invoice.paymentHistory.push({
      status: "failed",
      paymentId: paymentEntity.id,
      amount: paymentEntity.amount / 100, // Convert from paise to rupees
      method: paymentEntity.method || "unknown",
      timestamp: new Date(),
      details: `Payment failed: ${errorDetails}`,
    });

    await invoice.save();

    // Create payment failure notification
    await createInvoicePaymentNotification(invoice, "failed", {
      paymentId: paymentEntity.id,
      method: paymentEntity.method || "unknown",
      amount: paymentEntity.amount / 100,
      errorDetails: errorDetails,
    });

    console.log(`Payment failure processed for invoice ${invoiceId}`);
  } catch (error) {
    console.error(`Error processing payment failure for invoice:`, error);
    // Save for retry
    await saveWebhookForRetry({
      event: "payment.failed",
      payload: payload,
      error: error,
    });
  }
};

/**
 * Handle invoice paid webhook
 * @param {Object} payload - The webhook payload
 */
const handleInvoicePaid = async (payload) => {
  try {
    if (!payload.invoice || !payload.invoice.entity) {
      console.error("Invalid invoice payload");
      return;
    }

    const invoiceEntity = payload.invoice.entity;
    const razorpayInvoiceId = invoiceEntity.id;

    // Find our invoice using Razorpay invoice ID
    const invoice = await SubscriptionInvoice.findOne({ razorpayInvoiceId });

    if (!invoice) {
      console.error(`Invoice not found with Razorpay ID: ${razorpayInvoiceId}`);
      return;
    }

    // Update invoice status
    invoice.status = "paid";
    invoice.paidDate = new Date();

    // Add to payment history
    if (!invoice.paymentHistory) {
      invoice.paymentHistory = [];
    }

    invoice.paymentHistory.push({
      status: "success",
      invoiceId: razorpayInvoiceId,
      amount: invoiceEntity.amount / 100, // Convert from paise to rupees
      timestamp: new Date(),
      details: "Invoice marked as paid",
    });

    await invoice.save();

    // Update subscription status
    const subscription = await Subscription.findById(invoice.subscriptionId);
    if (subscription && subscription.status === "pending") {
      subscription.status = "active";
      await subscription.save();
      console.log(
        `Subscription ${subscription._id} activated after invoice payment`
      );

      // Create subscription status notification
      await createSubscriptionStatusNotification(subscription, "active", {
        invoiceId: invoice._id,
        invoiceNumber: invoice.invoiceNumber,
        amount: invoice.amount,
      });
    }

    // Create payment success notification
    await createInvoicePaymentNotification(invoice, "paid", {
      invoiceId: razorpayInvoiceId,
      amount: invoiceEntity.amount / 100,
    });

    console.log(`Invoice paid webhook processed for invoice ${invoice._id}`);
  } catch (error) {
    console.error(`Error processing invoice paid webhook:`, error);
    // Save for retry
    await saveWebhookForRetry({
      event: "invoice.paid",
      payload: payload,
      error: error,
    });
  }
};

/**
 * Handle invoice partially paid webhook
 * @param {Object} payload - The webhook payload
 */
const handleInvoicePartiallyPaid = async (payload) => {
  if (!payload.invoice || !payload.invoice.entity) {
    console.error("Invalid invoice payload");
    return;
  }

  const invoiceEntity = payload.invoice.entity;
  const razorpayInvoiceId = invoiceEntity.id;

  // Find our invoice using Razorpay invoice ID
  const invoice = await SubscriptionInvoice.findOne({ razorpayInvoiceId });

  if (!invoice) {
    console.error(`Invoice not found with Razorpay ID: ${razorpayInvoiceId}`);
    return;
  }

  // Update invoice status
  invoice.status = "partially_paid";

  // Add to payment history
  if (!invoice.paymentHistory) {
    invoice.paymentHistory = [];
  }

  invoice.paymentHistory.push({
    status: "partial",
    invoiceId: razorpayInvoiceId,
    amount: invoiceEntity.amount_paid / 100, // Convert from paise to rupees
    timestamp: new Date(),
    details: `Invoice partially paid: ${invoiceEntity.amount_paid / 100} of ${
      invoiceEntity.amount / 100
    }`,
  });

  await invoice.save();
  console.log(
    `Invoice partially paid webhook processed for invoice ${invoice._id}`
  );
};

/**
 * Handle invoice expired webhook
 * @param {Object} payload - The webhook payload
 */
const handleInvoiceExpired = async (payload) => {
  if (!payload.invoice || !payload.invoice.entity) {
    console.error("Invalid invoice payload");
    return;
  }

  const invoiceEntity = payload.invoice.entity;
  const razorpayInvoiceId = invoiceEntity.id;

  // Find our invoice using Razorpay invoice ID
  const invoice = await SubscriptionInvoice.findOne({ razorpayInvoiceId });

  if (!invoice) {
    console.error(`Invoice not found with Razorpay ID: ${razorpayInvoiceId}`);
    return;
  }

  // Don't update if already paid
  if (invoice.status === "paid") {
    console.log(
      `Invoice ${invoice._id} already paid, ignoring expired webhook`
    );
    return;
  }

  // Update invoice status
  invoice.status = "expired";

  // Add to payment history
  if (!invoice.paymentHistory) {
    invoice.paymentHistory = [];
  }

  invoice.paymentHistory.push({
    status: "expired",
    invoiceId: razorpayInvoiceId,
    timestamp: new Date(),
    details: "Invoice expired",
  });

  await invoice.save();
  console.log(`Invoice expired webhook processed for invoice ${invoice._id}`);
};

/**
 * Handle invoice cancelled webhook
 * @param {Object} payload - The webhook payload
 */
const handleInvoiceCancelled = async (payload) => {
  if (!payload.invoice || !payload.invoice.entity) {
    console.error("Invalid invoice payload");
    return;
  }

  const invoiceEntity = payload.invoice.entity;
  const razorpayInvoiceId = invoiceEntity.id;

  // Find our invoice using Razorpay invoice ID
  const invoice = await SubscriptionInvoice.findOne({ razorpayInvoiceId });

  if (!invoice) {
    console.error(`Invoice not found with Razorpay ID: ${razorpayInvoiceId}`);
    return;
  }

  // Don't update if already paid
  if (invoice.status === "paid") {
    console.log(
      `Invoice ${invoice._id} already paid, ignoring cancelled webhook`
    );
    return;
  }

  // Update invoice status
  invoice.status = "cancelled";

  // Add to payment history
  if (!invoice.paymentHistory) {
    invoice.paymentHistory = [];
  }

  invoice.paymentHistory.push({
    status: "cancelled",
    invoiceId: razorpayInvoiceId,
    timestamp: new Date(),
    details: "Invoice cancelled",
  });

  await invoice.save();
  console.log(`Invoice cancelled webhook processed for invoice ${invoice._id}`);
};

/**
 * Handle refund webhook
 * @param {Object} payload - The webhook payload
 */
const handleRefund = async (payload) => {
  if (!payload.refund || !payload.refund.entity) {
    console.error("Invalid refund payload");
    return;
  }

  const refundEntity = payload.refund.entity;
  const paymentId = refundEntity.payment_id;

  // Find invoice by payment ID
  const invoice = await SubscriptionInvoice.findOne({
    razorpayPaymentId: paymentId,
  });

  if (!invoice) {
    console.error(`Invoice not found for payment ID: ${paymentId}`);
    return;
  }

  // Add refund to payment history
  if (!invoice.paymentHistory) {
    invoice.paymentHistory = [];
  }

  invoice.paymentHistory.push({
    status: "refunded",
    refundId: refundEntity.id,
    paymentId: paymentId,
    amount: refundEntity.amount / 100, // Convert from paise to rupees
    timestamp: new Date(),
    details: `Refund ${refundEntity.status}: ${refundEntity.amount / 100} (${
      refundEntity.status
    })`,
  });

  // If full refund, update invoice status
  if (refundEntity.amount === refundEntity.payment_amount) {
    invoice.status = "refunded";
  } else {
    invoice.status = "partially_refunded";
  }

  await invoice.save();
  console.log(`Refund webhook processed for invoice ${invoice._id}`);
};

export const handleSubscriptionPaymentWebhook = async (req, res) => {
  try {
    // Verify webhook signature
    const webhookSignature = req.headers["x-razorpay-signature"];
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;

    if (webhookSignature && webhookSecret) {
      const isValidSignature = crypto
        .createHmac("sha256", webhookSecret)
        .update(JSON.stringify(req.body))
        .digest("hex");

      if (webhookSignature !== isValidSignature) {
        console.error("Invalid webhook signature");
        return res.status(400).json({
          success: false,
          message: "Invalid webhook signature",
        });
      }
    } else {
      console.warn(
        "Webhook signature verification skipped - missing signature or secret"
      );
    }

    const { payload } = req.body;

    if (!payload) {
      return res.status(400).json({
        success: false,
        message: "Invalid webhook payload - missing payload",
      });
    }

    // Log the webhook event for monitoring
    console.log(`Processing Razorpay webhook: ${req.body.event}`);

    try {
      // Handle different webhook events
      switch (req.body.event) {
        case "payment.authorized":
        case "payment.captured":
          await handlePaymentSuccess(payload);
          break;
        case "payment.failed":
          await handlePaymentFailure(payload);
          break;
        case "invoice.paid":
          await handleInvoicePaid(payload);
          break;
        case "invoice.partially_paid":
          await handleInvoicePartiallyPaid(payload);
          break;
        case "invoice.expired":
          await handleInvoiceExpired(payload);
          break;
        case "invoice.cancelled":
          await handleInvoiceCancelled(payload);
          break;
        case "refund.created":
        case "refund.processed":
          await handleRefund(payload);
          break;
        default:
          console.log(`Unhandled webhook event: ${req.body.event}`);
      }
    } catch (handlerError) {
      console.error(
        `Error in webhook handler for ${req.body.event}:`,
        handlerError
      );

      // Save webhook for retry
      await saveWebhookForRetry({
        event: req.body.event,
        payload: payload,
        headers: {
          "x-razorpay-signature": webhookSignature,
        },
        error: handlerError,
      });

      // Still return 200 to Razorpay to prevent them from retrying
      // We'll handle retries ourselves
      return res.status(200).json({
        success: true,
        message: "Webhook received and queued for processing",
      });
    }

    res.status(200).json({
      success: true,
      message: "Webhook processed successfully",
    });
  } catch (error) {
    console.error("Error processing subscription payment webhook:", error);

    try {
      // Try to save webhook for retry even in case of general error
      await saveWebhookForRetry({
        event: req.body.event || "unknown",
        payload: req.body.payload || {},
        headers: req.headers || {},
        error: error,
      });
    } catch (retryError) {
      console.error("Error saving webhook for retry:", retryError);
    }

    // Return 200 to prevent Razorpay from retrying
    res.status(200).json({
      success: true,
      message:
        "Webhook received but encountered an error during processing. It has been queued for retry.",
    });
  }
};

/**
 * Verify subscription payment after completion
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const verifySubscriptionPayment = async (req, res) => {
  try {
    const {
      razorpay_payment_id,
      razorpay_payment_link_id,
      razorpay_signature,
      razorpay_invoice_id,
      razorpay_order_id,
    } = req.body;

    // Verify signature if available
    if (razorpay_payment_id && razorpay_order_id && razorpay_signature) {
      const isValid = verifyInvoicePaymentSignature(
        razorpay_payment_id,
        razorpay_order_id,
        razorpay_signature
      );

      if (!isValid) {
        return res.status(400).json({
          success: false,
          message: "Invalid payment signature",
        });
      }
    }

    // Check for required parameters
    if (
      !razorpay_payment_id ||
      (!razorpay_payment_link_id && !razorpay_invoice_id)
    ) {
      return res.status(400).json({
        success: false,
        message:
          "Payment ID and either payment link ID or invoice ID are required",
      });
    }

    // Find the invoice by payment link ID or invoice ID
    const invoice = await SubscriptionInvoice.findOne({
      $or: [
        { razorpayPaymentLinkId: razorpay_payment_link_id },
        { razorpayInvoiceId: razorpay_payment_link_id },
        { razorpayInvoiceId: razorpay_invoice_id },
      ],
    });

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found",
      });
    }

    // Update invoice with payment ID
    invoice.razorpayPaymentId = razorpay_payment_id;

    // Get invoice details to confirm status
    let paymentStatus;
    try {
      // Try to get invoice details first
      if (invoice.razorpayInvoiceId) {
        const invoiceDetails = await getRazorpayInvoiceDetails(
          invoice.razorpayInvoiceId
        );
        paymentStatus =
          invoiceDetails.status === "paid" ? "paid" : invoiceDetails.status;
      } else {
        // Fall back to payment link if invoice ID is not available
        const paymentLinkDetails = await getSubscriptionPaymentLinkDetails(
          razorpay_payment_link_id
        );
        paymentStatus = paymentLinkDetails.status;
      }
    } catch (error) {
      console.error("Error fetching payment details:", error);
      // Assume payment is successful if we have a payment ID and signature
      paymentStatus = "paid";
    }

    // Update invoice status if it's completed
    if (paymentStatus === "paid" && invoice.status !== "paid") {
      invoice.status = "paid";
      invoice.paidDate = new Date();

      // Add payment details to history
      if (!invoice.paymentHistory) {
        invoice.paymentHistory = [];
      }

      invoice.paymentHistory.push({
        status: "success",
        paymentId: razorpay_payment_id,
        timestamp: new Date(),
        details: `Payment verified successfully`,
      });

      await invoice.save();

      // Update subscription status
      const subscription = await Subscription.findById(invoice.subscriptionId)
        .populate("foodChainId", "name")
        .populate("planId", "name interval");

      if (subscription && subscription.status === "pending") {
        subscription.status = "active";
        await subscription.save();

        return res.status(200).json({
          success: true,
          message: "Payment verified successfully",
          data: {
            subscription,
            invoice,
          },
        });
      }
    } else if (paymentStatus === "failed" && invoice.status !== "failed") {
      invoice.status = "failed";

      // Add payment details to history
      if (!invoice.paymentHistory) {
        invoice.paymentHistory = [];
      }

      invoice.paymentHistory.push({
        status: "failed",
        paymentId: razorpay_payment_id,
        timestamp: new Date(),
        details: `Payment verification failed`,
      });

      await invoice.save();
    }

    res.status(200).json({
      success: true,
      message: "Payment verification completed",
      data: {
        invoice,
        paymentStatus: paymentStatus,
      },
    });
  } catch (error) {
    console.error("Error verifying subscription payment:", error);
    res.status(500).json({
      success: false,
      message: "Error verifying payment",
      error: error.message,
    });
  }
};

/**
 * Exempt a food chain from the next subscription payment (Super Admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const exemptFromNextPayment = async (req, res) => {
  try {
    // Only super admins can exempt subscriptions
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can exempt subscriptions from payment",
      });
    }

    const { subscriptionId } = req.params;
    const { reason } = req.body;

    // Find the subscription
    const subscription = await Subscription.findById(subscriptionId);
    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found",
      });
    }

    // Update subscription
    subscription.isExemptedFromNextPayment = true;
    subscription.exemptionReason = reason || "Exempted by super admin";
    await subscription.save();

    res.status(200).json({
      success: true,
      message: "Subscription exempted from next payment",
      data: subscription,
    });
  } catch (error) {
    console.error("Error exempting subscription from payment:", error);
    res.status(500).json({
      success: false,
      message: "Error exempting subscription from payment",
      error: error.message,
    });
  }
};

/**
 * Manually trigger monthly subscription billing (Super Admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const triggerMonthlyBilling = async (req, res) => {
  try {
    // Only super admins can trigger billing
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can trigger monthly billing",
      });
    }

    console.log(
      "Manual billing trigger initiated by super admin:",
      req.user.email
    );

    // Import the function from the cron utility
    const { triggerMonthlyBilling: triggerBilling } = await import(
      "../utils/subscriptionCron.js"
    );

    // Execute the billing process
    const result = await triggerBilling();

    // Log the result
    console.log("Manual billing process completed with result:", result);

    // Return the result to the client
    res.status(200).json({
      success: true,
      message: result.success
        ? "Monthly billing process completed successfully"
        : "Monthly billing process completed with errors",
      data: result,
    });
  } catch (error) {
    console.error("Error triggering monthly billing:", error);
    res.status(500).json({
      success: false,
      message: "Error triggering monthly billing",
      error: error.message,
    });
  }
};

/**
 * Change a food chain's subscription plan (Admin or Super Admin)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const upgradeSubscriptionPlan = async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const { planId } = req.body;

    // Validate required fields
    if (!planId) {
      return res.status(400).json({
        success: false,
        message: "Plan ID is required",
      });
    }

    // Find the subscription
    const subscription = await Subscription.findById(subscriptionId);
    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found",
      });
    }

    // Check if user has permission to change this subscription
    if (
      req.user.role !== "super_admin" &&
      (!req.user.foodChain ||
        req.user.foodChain.toString() !== subscription.foodChainId.toString())
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to change this subscription",
      });
    }

    // Find the new plan
    const newPlan = await SubscriptionPlan.findById(planId);
    if (!newPlan) {
      return res.status(404).json({
        success: false,
        message: "Subscription plan not found",
      });
    }

    // Find the current plan
    const currentPlan = await SubscriptionPlan.findById(subscription.planId);
    if (!currentPlan) {
      return res.status(404).json({
        success: false,
        message: "Current subscription plan not found",
      });
    }

    // Check if the plan is actually changing
    if (newPlan._id.toString() === currentPlan._id.toString()) {
      return res.status(400).json({
        success: false,
        message: "The selected plan is the same as the current plan",
      });
    }

    // Determine if this is an upgrade or downgrade
    const isUpgrade = newPlan.price > currentPlan.price;
    const isDowngrade = newPlan.price < currentPlan.price;

    // If super admin, allow both upgrade and downgrade
    // If regular admin, only allow upgrade
    if (isDowngrade && req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message:
          "Only super admins can downgrade subscription plans. Please contact support for downgrading your plan.",
      });
    }

    // Calculate new total amount
    const totalAmount = newPlan.price * subscription.outletCount;

    // Record the plan change in subscription history if not already present
    if (!subscription.planHistory) {
      subscription.planHistory = [];
    }

    // Add the current plan to history before changing
    subscription.planHistory.push({
      planId: currentPlan._id,
      name: currentPlan.name,
      price: currentPlan.price,
      startDate: subscription.startDate || new Date(),
      endDate: new Date(),
    });

    // Add new plan to history
    subscription.planHistory.push({
      planId: newPlan._id,
      name: newPlan.name,
      price: newPlan.price,
      startDate: new Date(),
      endDate: null, // Will be set when plan changes again or subscription ends
    });

    // Update subscription with new plan
    subscription.planId = planId;
    subscription.totalAmount = totalAmount;

    // Save the updated subscription
    await subscription.save();

    // Note: We don't create an invoice immediately.
    // Invoices will be generated at the beginning of the next month with prorated amounts
    // based on the number of days each plan was used.

    // Create appropriate message based on whether this is an upgrade or downgrade
    const actionType =
      newPlan.price > currentPlan.price ? "upgraded" : "changed";
    const message = `Subscription plan ${actionType} successfully. You will be billed for the prorated amount at the beginning of next month.`;

    res.status(200).json({
      success: true,
      message,
      data: {
        subscription,
        isUpgrade: newPlan.price > currentPlan.price,
        isDowngrade: newPlan.price < currentPlan.price,
        previousPlan: {
          id: currentPlan._id,
          name: currentPlan.name,
          price: currentPlan.price,
        },
        newPlan: {
          id: newPlan._id,
          name: newPlan.name,
          price: newPlan.price,
        },
      },
    });
  } catch (error) {
    console.error("Error upgrading subscription plan:", error);
    res.status(500).json({
      success: false,
      message: "Error upgrading subscription plan",
      error: error.message,
    });
  }
};

/**
 * Manually check for trial periods ending soon (Super Admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const checkTrialPeriods = async (req, res) => {
  try {
    // Only super admins can trigger this check
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can trigger trial period checks",
      });
    }

    console.log(
      "Manual trial period check initiated by super admin:",
      req.user.email
    );

    // Import the function from the cron utility
    const { checkTrialPeriods: checkTrials } = await import(
      "../utils/subscriptionCron.js"
    );

    // Execute the check
    const result = await checkTrials();

    // Log the result
    console.log("Manual trial period check completed with result:", result);

    // Return the result to the client
    res.status(200).json({
      success: true,
      message: `Trial period check completed. Found ${
        result.count || 0
      } subscriptions with trial periods ending soon.`,
      data: result,
    });
  } catch (error) {
    console.error("Error checking trial periods:", error);
    res.status(500).json({
      success: false,
      message: "Error checking trial periods",
      error: error.message,
    });
  }
};

export default {
  createSubscriptionPlan,
  getAllSubscriptionPlans,
  updateSubscriptionPlan,
  createSubscription,
  getAllSubscriptions,
  getFoodChainSubscription,
  getInvoiceDetails,
  createInvoice,
  handleSubscriptionPaymentWebhook,
  verifySubscriptionPayment,
  exemptFromNextPayment,
  triggerMonthlyBilling,
  upgradeSubscriptionPlan,
  checkTrialPeriods,
};
