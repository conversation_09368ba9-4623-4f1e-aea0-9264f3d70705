import Coupon from "../models/Coupon.js";
import User from "../models/User.js";
import Order from "../models/Order.js";

// Create a new coupon
export const createCoupon = async (req, res) => {
  try {
    const {
      code,
      description,
      discountType,
      discountValue,
      minOrderValue,
      maxDiscount,
      startDate,
      endDate,
      usageLimit,
      applicableOutlets,
      applicableDishes,
      customerRestrictions,
    } = req.body;

    // Check if coupon code already exists
    const existingCoupon = await Coupon.findOne({
      code: code.toUpperCase(),
      foodChainId: req.user.foodChain,
    });

    if (existingCoupon) {
      return res.status(400).json({
        success: false,
        message: "Coupon code already exists",
      });
    }

    // Create new coupon
    const newCoupon = new Coupon({
      code: code.toUpperCase(),
      description,
      discountType,
      discountValue,
      minOrderValue: minOrderValue || 0,
      maxDiscount,
      startDate,
      endDate,
      usageLimit: usageLimit || 0,
      createdBy: req.user._id,
      foodChainId: req.user.foodChain,
      applicableOutlets,
      applicableDishes,
      customerRestrictions,
    });

    await newCoupon.save();

    res.status(201).json({
      success: true,
      message: "Coupon created successfully",
      data: newCoupon,
    });
  } catch (error) {
    console.error("Error creating coupon:", error);
    res.status(500).json({
      success: false,
      message: "Error creating coupon",
      error: error.message,
    });
  }
};

// Get all coupons for a food chain
export const getAllCoupons = async (req, res) => {
  try {
    const { status, search } = req.query;
    const foodChainId = req.user.foodChain;

    let query = { foodChainId };

    // Filter by status if provided
    if (status === "active") {
      const now = new Date();
      query.isActive = true;
      query.startDate = { $lte: now };
      query.endDate = { $gte: now };
    } else if (status === "inactive") {
      query.isActive = false;
    } else if (status === "expired") {
      const now = new Date();
      query.endDate = { $lt: now };
    } else if (status === "upcoming") {
      const now = new Date();
      query.startDate = { $gt: now };
    }

    // Search by code or description
    if (search) {
      query.$or = [
        { code: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    const coupons = await Coupon.find(query)
      .sort({ createdAt: -1 })
      .populate("createdBy", "name email")
      .populate("applicableOutlets", "name")
      .populate("applicableDishes", "name");

    res.status(200).json({
      success: true,
      count: coupons.length,
      data: coupons,
    });
  } catch (error) {
    console.error("Error fetching coupons:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching coupons",
      error: error.message,
    });
  }
};

// Get a single coupon by ID
export const getCouponById = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const coupon = await Coupon.findOne({ _id: id, foodChainId })
      .populate("createdBy", "name email")
      .populate("applicableOutlets", "name")
      .populate("applicableDishes", "name");

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found",
      });
    }

    res.status(200).json({
      success: true,
      data: coupon,
    });
  } catch (error) {
    console.error("Error fetching coupon:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching coupon",
      error: error.message,
    });
  }
};

// Update a coupon
export const updateCoupon = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;
    const updateData = req.body;

    // If code is being updated, check if it already exists
    if (updateData.code) {
      const existingCoupon = await Coupon.findOne({
        code: updateData.code.toUpperCase(),
        foodChainId,
        _id: { $ne: id },
      });

      if (existingCoupon) {
        return res.status(400).json({
          success: false,
          message: "Coupon code already exists",
        });
      }

      updateData.code = updateData.code.toUpperCase();
    }

    // Update the coupon
    updateData.updatedAt = new Date();
    const updatedCoupon = await Coupon.findOneAndUpdate(
      { _id: id, foodChainId },
      updateData,
      { new: true }
    );

    if (!updatedCoupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Coupon updated successfully",
      data: updatedCoupon,
    });
  } catch (error) {
    console.error("Error updating coupon:", error);
    res.status(500).json({
      success: false,
      message: "Error updating coupon",
      error: error.message,
    });
  }
};

// Delete a coupon
export const deleteCoupon = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const deletedCoupon = await Coupon.findOneAndDelete({
      _id: id,
      foodChainId,
    });

    if (!deletedCoupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Coupon deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting coupon:", error);
    res.status(500).json({
      success: false,
      message: "Error deleting coupon",
      error: error.message,
    });
  }
};

// Validate a coupon
export const validateCoupon = async (req, res) => {
  try {
    const { code, orderId, customerId, outletId, amount } = req.body;
    const foodChainId = req.user.foodChain;

    // Find the coupon
    const coupon = await Coupon.findOne({
      code: code.toUpperCase(),
      foodChainId,
    });

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found",
      });
    }

    // Check if coupon is active
    if (!coupon.isActive) {
      return res.status(400).json({
        success: false,
        message: "Coupon is inactive",
      });
    }

    // Check if coupon is expired
    const now = new Date();
    if (now < coupon.startDate || now > coupon.endDate) {
      return res.status(400).json({
        success: false,
        message: "Coupon is not valid at this time",
      });
    }

    // Check usage limit
    if (coupon.usageLimit > 0 && coupon.usedCount >= coupon.usageLimit) {
      return res.status(400).json({
        success: false,
        message: "Coupon usage limit reached",
      });
    }

    // Check minimum order value
    if (amount < coupon.minOrderValue) {
      return res.status(400).json({
        success: false,
        message: `Minimum order value of ${coupon.minOrderValue} required`,
      });
    }

    // Check if coupon is applicable to the outlet
    if (
      coupon.applicableOutlets &&
      coupon.applicableOutlets.length > 0 &&
      !coupon.applicableOutlets.includes(outletId)
    ) {
      return res.status(400).json({
        success: false,
        message: "Coupon not applicable to this outlet",
      });
    }

    // Check customer restrictions
    if (customerId && coupon.customerRestrictions) {
      // Check if first-time only
      if (coupon.customerRestrictions.firstTimeOnly) {
        const previousOrders = await Order.countDocuments({
          customer: customerId,
          foodChainId,
        });

        if (previousOrders > 0) {
          return res.status(400).json({
            success: false,
            message: "Coupon is for first-time customers only",
          });
        }
      }

      // Check if specific customers only
      if (
        coupon.customerRestrictions.specificCustomers &&
        coupon.customerRestrictions.specificCustomers.length > 0 &&
        !coupon.customerRestrictions.specificCustomers.includes(customerId)
      ) {
        return res.status(400).json({
          success: false,
          message: "Coupon not applicable to this customer",
        });
      }
    }

    // Calculate discount
    let discount = 0;
    if (coupon.discountType === "percentage") {
      discount = (amount * coupon.discountValue) / 100;
      // Apply max discount if specified
      if (coupon.maxDiscount && discount > coupon.maxDiscount) {
        discount = coupon.maxDiscount;
      }
    } else {
      // Fixed discount
      discount = coupon.discountValue;
      // Ensure discount doesn't exceed order amount
      if (discount > amount) {
        discount = amount;
      }
    }

    res.status(200).json({
      success: true,
      message: "Coupon is valid",
      data: {
        coupon,
        discount,
        finalAmount: amount - discount,
      },
    });
  } catch (error) {
    console.error("Error validating coupon:", error);
    res.status(500).json({
      success: false,
      message: "Error validating coupon",
      error: error.message,
    });
  }
};

// Apply a coupon to an order
export const applyCoupon = async (req, res) => {
  try {
    const { code, orderId } = req.body;
    const foodChainId = req.user.foodChain;

    // Find the order
    const order = await Order.findOne({
      _id: orderId,
      foodChainId,
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Find the coupon
    const coupon = await Coupon.findOne({
      code: code.toUpperCase(),
      foodChainId,
    });

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found",
      });
    }

    // Validate coupon (reuse validation logic)
    const validationResult = await validateCouponInternal(
      coupon,
      order.totalAmount,
      order.customer,
      order.outlet
    );

    if (!validationResult.success) {
      return res.status(400).json(validationResult);
    }

    // Apply discount to order
    order.couponCode = coupon.code;
    order.couponDiscount = validationResult.data.discount;
    order.finalAmount = order.totalAmount - validationResult.data.discount;
    await order.save();

    // Increment coupon usage count
    coupon.usedCount += 1;
    await coupon.save();

    res.status(200).json({
      success: true,
      message: "Coupon applied successfully",
      data: {
        order,
        discount: validationResult.data.discount,
      },
    });
  } catch (error) {
    console.error("Error applying coupon:", error);
    res.status(500).json({
      success: false,
      message: "Error applying coupon",
      error: error.message,
    });
  }
};

// Function to automatically reapply coupon when order total changes
export const reapplyCouponToOrder = async (order) => {
  try {
    if (!order.couponCode) {
      return { success: true, order };
    }

    // Find the coupon
    const coupon = await Coupon.findOne({
      code: order.couponCode.toUpperCase(),
      foodChainId: order.foodChainId,
    });

    if (!coupon) {
      // Coupon no longer exists, remove it from order
      order.couponCode = undefined;
      order.couponDiscount = 0;
      order.finalAmount = order.totalAmount;
      return {
        success: true,
        order,
        message: "Coupon no longer exists and was removed",
      };
    }

    // Validate coupon with new total amount
    const validationResult = await validateCouponInternal(
      coupon,
      order.totalAmount,
      order.userId,
      order.outletId
    );

    if (!validationResult.success) {
      // Coupon is no longer valid, remove it from order
      order.couponCode = undefined;
      order.couponDiscount = 0;
      order.finalAmount = order.totalAmount;
      return {
        success: true,
        order,
        message: `Coupon is no longer valid: ${validationResult.message}`,
      };
    }

    // Update order with new discount
    order.couponDiscount = validationResult.data.discount;
    order.finalAmount = order.totalAmount - validationResult.data.discount;

    return {
      success: true,
      order,
      message: "Coupon reapplied successfully",
      discount: validationResult.data.discount,
    };
  } catch (error) {
    console.error("Error reapplying coupon:", error);
    // In case of error, remove coupon to be safe
    order.couponCode = undefined;
    order.couponDiscount = 0;
    order.finalAmount = order.totalAmount;
    return {
      success: false,
      order,
      message: "Error reapplying coupon, coupon removed",
      error: error.message,
    };
  }
};

// Internal function to validate coupon (used by both validate and apply endpoints)
const validateCouponInternal = async (coupon, amount, customerId, outletId) => {
  try {
    // Check if coupon is active
    if (!coupon.isActive) {
      return {
        success: false,
        message: "Coupon is inactive",
      };
    }

    // Check if coupon is expired
    const now = new Date();
    if (now < coupon.startDate || now > coupon.endDate) {
      return {
        success: false,
        message: "Coupon is not valid at this time",
      };
    }

    // Check usage limit
    if (coupon.usageLimit > 0 && coupon.usedCount >= coupon.usageLimit) {
      return {
        success: false,
        message: "Coupon usage limit reached",
      };
    }

    // Check minimum order value
    if (amount < coupon.minOrderValue) {
      return {
        success: false,
        message: `Minimum order value of ${coupon.minOrderValue} required`,
      };
    }

    // Check if coupon is applicable to the outlet
    if (
      coupon.applicableOutlets &&
      coupon.applicableOutlets.length > 0 &&
      !coupon.applicableOutlets.includes(outletId)
    ) {
      return {
        success: false,
        message: "Coupon not applicable to this outlet",
      };
    }

    // Check customer restrictions
    if (customerId && coupon.customerRestrictions) {
      // Check if first-time only
      if (coupon.customerRestrictions.firstTimeOnly) {
        const previousOrders = await Order.countDocuments({
          customer: customerId,
        });

        if (previousOrders > 0) {
          return {
            success: false,
            message: "Coupon is for first-time customers only",
          };
        }
      }

      // Check if specific customers only
      if (
        coupon.customerRestrictions.specificCustomers &&
        coupon.customerRestrictions.specificCustomers.length > 0 &&
        !coupon.customerRestrictions.specificCustomers.includes(customerId)
      ) {
        return {
          success: false,
          message: "Coupon not applicable to this customer",
        };
      }
    }

    // Calculate discount
    let discount = 0;
    if (coupon.discountType === "percentage") {
      discount = (amount * coupon.discountValue) / 100;
      // Apply max discount if specified
      if (coupon.maxDiscount && discount > coupon.maxDiscount) {
        discount = coupon.maxDiscount;
      }
    } else {
      // Fixed discount
      discount = coupon.discountValue;
      // Ensure discount doesn't exceed order amount
      if (discount > amount) {
        discount = amount;
      }
    }

    return {
      success: true,
      message: "Coupon is valid",
      data: {
        coupon,
        discount,
        finalAmount: amount - discount,
      },
    };
  } catch (error) {
    console.error("Error in validateCouponInternal:", error);
    return {
      success: false,
      message: "Error validating coupon",
      error: error.message,
    };
  }
};
