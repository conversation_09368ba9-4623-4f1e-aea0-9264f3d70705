import Campaign from "../models/Campaign.js";
import CampaignRecipient from "../models/CampaignRecipient.js";
import User from "../models/User.js";
import Coupon from "../models/Coupon.js";
import Offer from "../models/Offer.js";
import { sendEmail } from "../utils/emailService.js";
import dotenv from "dotenv";

dotenv.config();

// Check if campaigns are enabled
const isCampaignsEnabled = () => {
  return process.env.ENABLE_CAMPAIGNS === "true";
};

// Create a new campaign
export const createCampaign = async (req, res) => {
  try {
    // Check if campaigns are enabled
    if (!isCampaignsEnabled()) {
      return res.status(403).json({
        success: false,
        message: "Campaign feature is disabled",
      });
    }

    const {
      name,
      description,
      type,
      targetAudience,
      content,
      scheduledDate,
      associatedCoupon,
      associatedOffer,
    } = req.body;

    // Validate required fields
    if (!name || !type || !content.body) {
      return res.status(400).json({
        success: false,
        message: "Name, type, and content body are required",
      });
    }

    // Validate email campaigns have subject
    if (type === "email" && !content.subject) {
      return res.status(400).json({
        success: false,
        message: "Subject is required for email campaigns",
      });
    }

    // Create new campaign
    const newCampaign = new Campaign({
      name,
      description,
      type,
      targetAudience: targetAudience || { type: "all" },
      content,
      scheduledDate,
      status: scheduledDate ? "scheduled" : "draft",
      createdBy: req.user._id,
      foodChainId: req.user.foodChain,
      associatedCoupon,
      associatedOffer,
    });

    await newCampaign.save();

    // If campaign is scheduled, create recipient records
    if (scheduledDate) {
      await createCampaignRecipients(newCampaign);
    }

    res.status(201).json({
      success: true,
      message: "Campaign created successfully",
      data: newCampaign,
    });
  } catch (error) {
    console.error("Error creating campaign:", error);
    res.status(500).json({
      success: false,
      message: "Error creating campaign",
      error: error.message,
    });
  }
};

// Get all campaigns for a food chain
export const getAllCampaigns = async (req, res) => {
  try {
    // Check if campaigns are enabled
    if (!isCampaignsEnabled()) {
      return res.status(403).json({
        success: false,
        message: "Campaign feature is disabled",
      });
    }

    const { status, type, search } = req.query;
    const foodChainId = req.user.foodChain;

    let query = { foodChainId };

    // Filter by status if provided
    if (status) {
      query.status = status;
    }

    // Filter by type if provided
    if (type) {
      query.type = type;
    }

    // Search by name or description
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    const campaigns = await Campaign.find(query)
      .sort({ createdAt: -1 })
      .populate("createdBy", "name email")
      .populate("associatedCoupon", "code discountType discountValue")
      .populate("associatedOffer", "name offerType");

    // Add recipient counts
    const campaignsWithStats = await Promise.all(
      campaigns.map(async (campaign) => {
        const campaignObj = campaign.toObject();
        
        // Get recipient counts
        const recipientCounts = await CampaignRecipient.aggregate([
          { $match: { campaignId: campaign._id } },
          { $group: { _id: "$status", count: { $sum: 1 } } },
        ]);

        // Initialize counts
        campaignObj.recipientStats = {
          total: 0,
          pending: 0,
          sent: 0,
          failed: 0,
          opened: 0,
          clicked: 0,
        };

        // Update counts from aggregation results
        recipientCounts.forEach((stat) => {
          campaignObj.recipientStats[stat._id] = stat.count;
          campaignObj.recipientStats.total += stat.count;
        });

        return campaignObj;
      })
    );

    res.status(200).json({
      success: true,
      count: campaignsWithStats.length,
      data: campaignsWithStats,
    });
  } catch (error) {
    console.error("Error fetching campaigns:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching campaigns",
      error: error.message,
    });
  }
};

// Get a single campaign by ID
export const getCampaignById = async (req, res) => {
  try {
    // Check if campaigns are enabled
    if (!isCampaignsEnabled()) {
      return res.status(403).json({
        success: false,
        message: "Campaign feature is disabled",
      });
    }

    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const campaign = await Campaign.findOne({ _id: id, foodChainId })
      .populate("createdBy", "name email")
      .populate("associatedCoupon", "code discountType discountValue")
      .populate("associatedOffer", "name offerType");

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: "Campaign not found",
      });
    }

    // Get recipient stats
    const recipientCounts = await CampaignRecipient.aggregate([
      { $match: { campaignId: campaign._id } },
      { $group: { _id: "$status", count: { $sum: 1 } } },
    ]);

    // Initialize counts
    const campaignObj = campaign.toObject();
    campaignObj.recipientStats = {
      total: 0,
      pending: 0,
      sent: 0,
      failed: 0,
      opened: 0,
      clicked: 0,
    };

    // Update counts from aggregation results
    recipientCounts.forEach((stat) => {
      campaignObj.recipientStats[stat._id] = stat.count;
      campaignObj.recipientStats.total += stat.count;
    });

    // Get sample recipients (limit to 10)
    const recipients = await CampaignRecipient.find({ campaignId: campaign._id })
      .populate("customerId", "name email phone")
      .limit(10);

    campaignObj.recipients = recipients;

    res.status(200).json({
      success: true,
      data: campaignObj,
    });
  } catch (error) {
    console.error("Error fetching campaign:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching campaign",
      error: error.message,
    });
  }
};

// Update a campaign
export const updateCampaign = async (req, res) => {
  try {
    // Check if campaigns are enabled
    if (!isCampaignsEnabled()) {
      return res.status(403).json({
        success: false,
        message: "Campaign feature is disabled",
      });
    }

    const { id } = req.params;
    const foodChainId = req.user.foodChain;
    const updateData = req.body;

    // Get current campaign
    const currentCampaign = await Campaign.findOne({ _id: id, foodChainId });

    if (!currentCampaign) {
      return res.status(404).json({
        success: false,
        message: "Campaign not found",
      });
    }

    // Check if campaign can be updated
    if (["sending", "sent", "cancelled"].includes(currentCampaign.status)) {
      return res.status(400).json({
        success: false,
        message: `Campaign cannot be updated in ${currentCampaign.status} status`,
      });
    }

    // If scheduling a draft campaign
    if (
      currentCampaign.status === "draft" &&
      updateData.scheduledDate &&
      !currentCampaign.scheduledDate
    ) {
      updateData.status = "scheduled";
    }

    // Update the campaign
    updateData.updatedAt = new Date();
    const updatedCampaign = await Campaign.findOneAndUpdate(
      { _id: id, foodChainId },
      updateData,
      { new: true }
    );

    // If campaign is newly scheduled, create recipient records
    if (
      updateData.status === "scheduled" &&
      currentCampaign.status === "draft"
    ) {
      await createCampaignRecipients(updatedCampaign);
    }

    res.status(200).json({
      success: true,
      message: "Campaign updated successfully",
      data: updatedCampaign,
    });
  } catch (error) {
    console.error("Error updating campaign:", error);
    res.status(500).json({
      success: false,
      message: "Error updating campaign",
      error: error.message,
    });
  }
};

// Delete a campaign
export const deleteCampaign = async (req, res) => {
  try {
    // Check if campaigns are enabled
    if (!isCampaignsEnabled()) {
      return res.status(403).json({
        success: false,
        message: "Campaign feature is disabled",
      });
    }

    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    // Get current campaign
    const campaign = await Campaign.findOne({ _id: id, foodChainId });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: "Campaign not found",
      });
    }

    // Check if campaign can be deleted
    if (["sending", "sent"].includes(campaign.status)) {
      return res.status(400).json({
        success: false,
        message: `Campaign cannot be deleted in ${campaign.status} status`,
      });
    }

    // Delete campaign recipients
    await CampaignRecipient.deleteMany({ campaignId: id });

    // Delete the campaign
    await Campaign.findOneAndDelete({ _id: id, foodChainId });

    res.status(200).json({
      success: true,
      message: "Campaign deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting campaign:", error);
    res.status(500).json({
      success: false,
      message: "Error deleting campaign",
      error: error.message,
    });
  }
};

// Send a campaign immediately
export const sendCampaign = async (req, res) => {
  try {
    // Check if campaigns are enabled
    if (!isCampaignsEnabled()) {
      return res.status(403).json({
        success: false,
        message: "Campaign feature is disabled",
      });
    }

    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    // Get current campaign
    const campaign = await Campaign.findOne({ _id: id, foodChainId });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: "Campaign not found",
      });
    }

    // Check if campaign can be sent
    if (!["draft", "scheduled"].includes(campaign.status)) {
      return res.status(400).json({
        success: false,
        message: `Campaign cannot be sent in ${campaign.status} status`,
      });
    }

    // Update campaign status to sending
    campaign.status = "sending";
    campaign.sentDate = new Date();
    await campaign.save();

    // Create recipients if not already created (for draft campaigns)
    if (campaign.status === "draft") {
      await createCampaignRecipients(campaign);
    }

    // Start sending the campaign in the background
    processCampaign(campaign._id)
      .then(() => {
        console.log(`Campaign ${campaign._id} sent successfully`);
      })
      .catch((error) => {
        console.error(`Error sending campaign ${campaign._id}:`, error);
      });

    res.status(200).json({
      success: true,
      message: "Campaign sending started",
      data: campaign,
    });
  } catch (error) {
    console.error("Error sending campaign:", error);
    res.status(500).json({
      success: false,
      message: "Error sending campaign",
      error: error.message,
    });
  }
};

// Cancel a scheduled campaign
export const cancelCampaign = async (req, res) => {
  try {
    // Check if campaigns are enabled
    if (!isCampaignsEnabled()) {
      return res.status(403).json({
        success: false,
        message: "Campaign feature is disabled",
      });
    }

    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    // Get current campaign
    const campaign = await Campaign.findOne({ _id: id, foodChainId });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: "Campaign not found",
      });
    }

    // Check if campaign can be cancelled
    if (!["scheduled"].includes(campaign.status)) {
      return res.status(400).json({
        success: false,
        message: `Only scheduled campaigns can be cancelled`,
      });
    }

    // Update campaign status to cancelled
    campaign.status = "cancelled";
    await campaign.save();

    // Update all pending recipients to cancelled
    await CampaignRecipient.updateMany(
      { campaignId: id, status: "pending" },
      { status: "cancelled" }
    );

    res.status(200).json({
      success: true,
      message: "Campaign cancelled successfully",
      data: campaign,
    });
  } catch (error) {
    console.error("Error cancelling campaign:", error);
    res.status(500).json({
      success: false,
      message: "Error cancelling campaign",
      error: error.message,
    });
  }
};

// Get campaign recipients
export const getCampaignRecipients = async (req, res) => {
  try {
    // Check if campaigns are enabled
    if (!isCampaignsEnabled()) {
      return res.status(403).json({
        success: false,
        message: "Campaign feature is disabled",
      });
    }

    const { id } = req.params;
    const { status, page = 1, limit = 20 } = req.query;
    const foodChainId = req.user.foodChain;

    // Check if campaign exists and belongs to the food chain
    const campaign = await Campaign.findOne({ _id: id, foodChainId });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: "Campaign not found",
      });
    }

    // Build query
    let query = { campaignId: id };
    if (status) {
      query.status = status;
    }

    // Count total recipients
    const total = await CampaignRecipient.countDocuments(query);

    // Get paginated recipients
    const recipients = await CampaignRecipient.find(query)
      .populate("customerId", "name email phone")
      .skip((page - 1) * limit)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: recipients.length,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit),
      data: recipients,
    });
  } catch (error) {
    console.error("Error fetching campaign recipients:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching campaign recipients",
      error: error.message,
    });
  }
};

// Helper function to create campaign recipients
const createCampaignRecipients = async (campaign) => {
  try {
    // Get target customers based on audience settings
    const foodChainId = campaign.foodChainId;
    let customerQuery = { role: "user" };

    // Add food chain filter for specific customers
    if (campaign.targetAudience.type === "specific" && campaign.targetAudience.specificCustomers?.length > 0) {
      customerQuery._id = { $in: campaign.targetAudience.specificCustomers };
    }

    // Add segment filters
    if (campaign.targetAudience.type === "segment") {
      if (campaign.targetAudience.segment === "active") {
        // Active customers (ordered in last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        // This would need to be adjusted based on your actual data model
        // For now, we'll just get all customers
        // In a real implementation, you'd query orders and get customers who ordered recently
      } else if (campaign.targetAudience.segment === "inactive") {
        // Inactive customers (no orders in last 90 days)
        const ninetyDaysAgo = new Date();
        ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
        
        // Similar to above, this would need actual order data
      } else if (campaign.targetAudience.segment === "new") {
        // New customers (registered in last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        customerQuery.createdAt = { $gte: thirtyDaysAgo };
      }
    }

    // Get customers
    const customers = await User.find(customerQuery).select("_id");

    // Create recipient records
    const recipientRecords = customers.map((customer) => ({
      campaignId: campaign._id,
      customerId: customer._id,
      status: "pending",
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    // Insert recipients in batches to avoid memory issues
    if (recipientRecords.length > 0) {
      await CampaignRecipient.insertMany(recipientRecords);
    }

    return recipientRecords.length;
  } catch (error) {
    console.error("Error creating campaign recipients:", error);
    throw error;
  }
};

// Process a campaign (send to all recipients)
const processCampaign = async (campaignId) => {
  try {
    // Get campaign
    const campaign = await Campaign.findById(campaignId)
      .populate("associatedCoupon")
      .populate("associatedOffer");

    if (!campaign) {
      throw new Error("Campaign not found");
    }

    // Get all pending recipients
    const recipients = await CampaignRecipient.find({
      campaignId,
      status: "pending",
    }).populate("customerId");

    console.log(`Processing campaign ${campaignId} with ${recipients.length} recipients`);

    // Process each recipient
    let sentCount = 0;
    let failedCount = 0;

    for (const recipient of recipients) {
      try {
        if (!recipient.customerId) {
          // Skip if customer doesn't exist
          recipient.status = "failed";
          recipient.errorMessage = "Customer not found";
          await recipient.save();
          failedCount++;
          continue;
        }

        const customer = recipient.customerId;

        // Skip if no email for email campaigns
        if (campaign.type === "email" && !customer.email) {
          recipient.status = "failed";
          recipient.errorMessage = "Customer has no email";
          await recipient.save();
          failedCount++;
          continue;
        }

        // Skip if no phone for SMS/WhatsApp campaigns
        if ((campaign.type === "sms" || campaign.type === "whatsapp") && !customer.phone) {
          recipient.status = "failed";
          recipient.errorMessage = "Customer has no phone number";
          await recipient.save();
          failedCount++;
          continue;
        }

        // Process based on campaign type
        if (campaign.type === "email") {
          // Prepare email content
          let emailContent = campaign.content.body;
          
          // Replace placeholders
          emailContent = emailContent
            .replace(/{{name}}/g, customer.name || "Customer")
            .replace(/{{email}}/g, customer.email || "");
          
          // Add coupon or offer details if associated
          if (campaign.associatedCoupon) {
            const coupon = campaign.associatedCoupon;
            emailContent = emailContent
              .replace(/{{coupon_code}}/g, coupon.code)
              .replace(/{{coupon_value}}/g, coupon.discountValue + (coupon.discountType === "percentage" ? "%" : ""));
          }
          
          if (campaign.associatedOffer) {
            const offer = campaign.associatedOffer;
            emailContent = emailContent
              .replace(/{{offer_name}}/g, offer.name);
          }

          // Send email
          await sendEmail({
            to: customer.email,
            subject: campaign.content.subject,
            html: emailContent,
            campaignId: campaign._id.toString(),
            recipientId: recipient._id.toString(),
          });
        } else if (campaign.type === "sms") {
          // SMS implementation would go here
          // This is a placeholder as we'll use a third-party service later
          console.log(`Would send SMS to ${customer.phone}`);
        } else if (campaign.type === "whatsapp") {
          // WhatsApp implementation would go here
          // This is a placeholder as we'll use a third-party service later
          console.log(`Would send WhatsApp to ${customer.phone}`);
        }

        // Update recipient status
        recipient.status = "sent";
        recipient.sentAt = new Date();
        await recipient.save();
        sentCount++;
      } catch (error) {
        console.error(`Error processing recipient ${recipient._id}:`, error);
        recipient.status = "failed";
        recipient.errorMessage = error.message;
        await recipient.save();
        failedCount++;
      }
    }

    // Update campaign status and counts
    campaign.status = "sent";
    campaign.sentCount += sentCount;
    await campaign.save();

    console.log(`Campaign ${campaignId} completed: ${sentCount} sent, ${failedCount} failed`);
    return { sentCount, failedCount };
  } catch (error) {
    console.error(`Error processing campaign ${campaignId}:`, error);
    
    // Update campaign status to failed
    await Campaign.findByIdAndUpdate(campaignId, {
      status: "failed",
      updatedAt: new Date(),
    });
    
    throw error;
  }
};

// Track email open
export const trackEmailOpen = async (req, res) => {
  try {
    const { campaignId, recipientId } = req.params;

    // Update recipient status
    await CampaignRecipient.findByIdAndUpdate(recipientId, {
      status: "opened",
      openedAt: new Date(),
    });

    // Update campaign open count
    await Campaign.findByIdAndUpdate(campaignId, {
      $inc: { openCount: 1 },
    });

    // Return a transparent 1x1 pixel GIF
    const pixel = Buffer.from("R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7", "base64");
    res.setHeader("Content-Type", "image/gif");
    res.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate");
    res.setHeader("Pragma", "no-cache");
    res.setHeader("Expires", "0");
    res.end(pixel);
  } catch (error) {
    console.error("Error tracking email open:", error);
    
    // Still return the pixel even if there's an error
    const pixel = Buffer.from("R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7", "base64");
    res.setHeader("Content-Type", "image/gif");
    res.end(pixel);
  }
};

// Track email click
export const trackEmailClick = async (req, res) => {
  try {
    const { campaignId, recipientId } = req.params;
    const { url } = req.query;

    // Update recipient status
    await CampaignRecipient.findByIdAndUpdate(recipientId, {
      status: "clicked",
      clickedAt: new Date(),
    });

    // Update campaign click count
    await Campaign.findByIdAndUpdate(campaignId, {
      $inc: { clickCount: 1 },
    });

    // Redirect to the target URL
    if (url) {
      return res.redirect(url);
    }

    res.status(200).json({
      success: true,
      message: "Click tracked successfully",
    });
  } catch (error) {
    console.error("Error tracking email click:", error);
    
    // If there's an error but we have a URL, still redirect
    if (req.query.url) {
      return res.redirect(req.query.url);
    }
    
    res.status(500).json({
      success: false,
      message: "Error tracking click",
      error: error.message,
    });
  }
};
