import {
  trackRecommendationDisplay,
  trackRecommendationInteraction,
  submitRecommendationFeedback,
  getRecommendationAnalytics,
  getFeedbackAnalytics,
  generateRecommendationInsights
} from "../services/recommendation-analytics-service.js";

/**
 * Track when recommendations are displayed to users
 */
export const trackDisplay = async (req, res) => {
  try {
    const {
      userId,
      outletId,
      sessionId,
      recommendationType,
      recommendations,
      context,
      performance
    } = req.body;

    if (!userId || !sessionId || !recommendationType || !recommendations) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields: userId, sessionId, recommendationType, recommendations"
      });
    }

    const analyticsId = await trackRecommendationDisplay({
      userId,
      outletId,
      sessionId,
      recommendationType,
      recommendations,
      context: {
        timeSlot: context?.timeSlot || getCurrentTimeSlot(),
        dayOfWeek: context?.dayOfWeek || getCurrentDayType(),
        season: context?.season || getCurrentSeason(),
        userQuery: context?.userQuery,
        previousOrders: context?.previousOrders || 0,
        userPreferenceConfidence: context?.userPreferenceConfidence || 0
      },
      performance: {
        responseTime: performance?.responseTime || 0,
        fromCache: performance?.fromCache || false,
        algorithmsUsed: performance?.algorithmsUsed || []
      }
    });

    res.status(200).json({
      success: true,
      analyticsId,
      message: "Recommendation display tracked successfully"
    });
  } catch (error) {
    console.error("Error tracking recommendation display:", error);
    res.status(500).json({
      success: false,
      message: "Error tracking recommendation display",
      error: error.message
    });
  }
};

/**
 * Track user interactions with recommendations
 */
export const trackInteraction = async (req, res) => {
  try {
    const {
      analyticsId,
      itemId,
      interactionType
    } = req.body;

    if (!analyticsId || !itemId || !interactionType) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields: analyticsId, itemId, interactionType"
      });
    }

    await trackRecommendationInteraction(analyticsId, itemId, interactionType);

    res.status(200).json({
      success: true,
      message: "Interaction tracked successfully"
    });
  } catch (error) {
    console.error("Error tracking interaction:", error);
    res.status(500).json({
      success: false,
      message: "Error tracking interaction",
      error: error.message
    });
  }
};

/**
 * Submit user feedback for recommendations
 */
export const submitFeedback = async (req, res) => {
  try {
    const {
      analyticsId,
      itemId,
      rating,
      helpful,
      reason,
      feedbackType,
      comment,
      context
    } = req.body;

    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User authentication required"
      });
    }

    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: "Valid rating (1-5) is required"
      });
    }

    const feedbackId = await submitRecommendationFeedback({
      analyticsId,
      itemId,
      rating,
      helpful,
      reason,
      userId,
      feedbackType: feedbackType || 'recommendation_quality',
      comment,
      context: {
        page: context?.page,
        feature: context?.feature,
        sessionId: context?.sessionId || req.sessionID
      }
    });

    res.status(200).json({
      success: true,
      feedbackId,
      message: "Feedback submitted successfully"
    });
  } catch (error) {
    console.error("Error submitting feedback:", error);
    res.status(500).json({
      success: false,
      message: "Error submitting feedback",
      error: error.message
    });
  }
};

/**
 * Get recommendation analytics (Admin only)
 */
export const getAnalytics = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user || !['admin', 'superadmin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: "Admin access required"
      });
    }

    const {
      startDate,
      endDate,
      userId,
      outletId,
      recommendationType
    } = req.query;

    const filters = {};
    
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);
    if (userId) filters.userId = userId;
    if (outletId) filters.outletId = outletId;
    if (recommendationType) filters.recommendationType = recommendationType;

    const analytics = await getRecommendationAnalytics(filters);

    if (!analytics) {
      return res.status(500).json({
        success: false,
        message: "Error retrieving analytics"
      });
    }

    res.status(200).json({
      success: true,
      data: analytics,
      message: "Analytics retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting analytics:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving analytics",
      error: error.message
    });
  }
};

/**
 * Get feedback analytics (Admin only)
 */
export const getFeedbackAnalyticsData = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user || !['admin', 'superadmin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: "Admin access required"
      });
    }

    const {
      startDate,
      endDate,
      feedbackType
    } = req.query;

    const filters = {};
    
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);
    if (feedbackType) filters.feedbackType = feedbackType;

    const feedbackAnalytics = await getFeedbackAnalytics(filters);

    if (!feedbackAnalytics) {
      return res.status(500).json({
        success: false,
        message: "Error retrieving feedback analytics"
      });
    }

    res.status(200).json({
      success: true,
      data: feedbackAnalytics,
      message: "Feedback analytics retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting feedback analytics:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving feedback analytics",
      error: error.message
    });
  }
};

/**
 * Generate insights for continuous improvement (Admin only)
 */
export const getInsights = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user || !['admin', 'superadmin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: "Admin access required"
      });
    }

    const {
      startDate,
      endDate,
      userId,
      outletId,
      recommendationType
    } = req.query;

    const filters = {};
    
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);
    if (userId) filters.userId = userId;
    if (outletId) filters.outletId = outletId;
    if (recommendationType) filters.recommendationType = recommendationType;

    const insights = await generateRecommendationInsights(filters);

    if (!insights) {
      return res.status(500).json({
        success: false,
        message: "Error generating insights"
      });
    }

    res.status(200).json({
      success: true,
      data: insights,
      message: "Insights generated successfully"
    });
  } catch (error) {
    console.error("Error generating insights:", error);
    res.status(500).json({
      success: false,
      message: "Error generating insights",
      error: error.message
    });
  }
};

/**
 * Get user's own feedback history
 */
export const getUserFeedbackHistory = async (req, res) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User authentication required"
      });
    }

    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    const { UserFeedback } = await import("../services/recommendation-analytics-service.js");
    
    const feedbackHistory = await UserFeedback.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-userId');

    const totalCount = await UserFeedback.countDocuments({ userId });

    res.status(200).json({
      success: true,
      data: {
        feedback: feedbackHistory,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalItems: totalCount,
          itemsPerPage: parseInt(limit)
        }
      },
      message: "Feedback history retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting user feedback history:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving feedback history",
      error: error.message
    });
  }
};

/**
 * Helper functions
 */
const getCurrentTimeSlot = () => {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 12) return 'morning';
  if (hour >= 12 && hour < 17) return 'afternoon';
  if (hour >= 17 && hour < 22) return 'evening';
  return 'night';
};

const getCurrentDayType = () => {
  const day = new Date().getDay();
  return (day === 0 || day === 6) ? 'weekend' : 'weekday';
};

const getCurrentSeason = () => {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) return 'spring';
  if (month >= 5 && month <= 7) return 'summer';
  if (month >= 8 && month <= 10) return 'autumn';
  return 'winter';
};
