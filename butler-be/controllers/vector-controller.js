import { 
  generateAllDishEmbeddings, 
  createOrUpdateDishEmbedding,
  findSimilarDishes
} from "../services/vector-service.js";
import Dish from "../models/Dish.js";
import DishEmbedding from "../models/DishEmbedding.js";

/**
 * Generate embeddings for all dishes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateEmbeddings = async (req, res) => {
  try {
    const count = await generateAllDishEmbeddings();
    
    res.status(200).json({
      success: true,
      message: `Successfully generated embeddings for ${count} dishes`,
      count
    });
  } catch (error) {
    console.error("Error generating embeddings:", error);
    res.status(500).json({
      success: false,
      message: "Error generating embeddings",
      error: error.message
    });
  }
};

/**
 * Generate embedding for a specific dish
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateDishEmbedding = async (req, res) => {
  try {
    const { dishId } = req.params;
    
    // Find the dish
    const dish = await Dish.findById(dishId).populate('category', 'name');
    
    if (!dish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found"
      });
    }
    
    // Generate embedding
    const embedding = await createOrUpdateDishEmbedding(dish);
    
    res.status(200).json({
      success: true,
      message: "Successfully generated embedding for dish",
      data: {
        dishId: dish._id,
        name: dish.name,
        embeddingId: embedding._id
      }
    });
  } catch (error) {
    console.error("Error generating dish embedding:", error);
    res.status(500).json({
      success: false,
      message: "Error generating dish embedding",
      error: error.message
    });
  }
};

/**
 * Get similar dishes based on a query
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getSimilarDishes = async (req, res) => {
  try {
    const { query, foodChainId, outletId, limit = 5 } = req.query;
    
    if (!query || !foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Query, foodChainId, and outletId are required"
      });
    }
    
    // Find similar dishes
    const similarDishes = await findSimilarDishes(
      query,
      foodChainId,
      outletId,
      parseInt(limit, 10)
    );
    
    res.status(200).json({
      success: true,
      data: similarDishes
    });
  } catch (error) {
    console.error("Error finding similar dishes:", error);
    res.status(500).json({
      success: false,
      message: "Error finding similar dishes",
      error: error.message
    });
  }
};

/**
 * Get embedding statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getEmbeddingStats = async (req, res) => {
  try {
    const { foodChainId } = req.query;
    
    // Build query
    const query = {};
    if (foodChainId) {
      query.foodChainId = foodChainId;
    }
    
    // Get total dishes
    const totalDishes = await Dish.countDocuments(query);
    
    // Get total embeddings
    const totalEmbeddings = await DishEmbedding.countDocuments(query);
    
    // Get coverage percentage
    const coverage = totalDishes > 0 ? (totalEmbeddings / totalDishes) * 100 : 0;
    
    res.status(200).json({
      success: true,
      data: {
        totalDishes,
        totalEmbeddings,
        coverage: Math.round(coverage * 100) / 100, // Round to 2 decimal places
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    console.error("Error getting embedding stats:", error);
    res.status(500).json({
      success: false,
      message: "Error getting embedding stats",
      error: error.message
    });
  }
};
