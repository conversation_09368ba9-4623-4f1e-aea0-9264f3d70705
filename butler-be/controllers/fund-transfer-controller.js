import FoodChain from "../models/FoodChain.js";
import FundTransfer from "../models/FundTransfer.js";
import {
  createFundAccount,
  createPayout,
  getPayoutDetails,
} from "../utils/razorpayTransfer.js";

/**
 * Create a fund account for a food chain
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createFoodChainFundAccount = async (req, res) => {
  try {
    const { foodChainId } = req.params;

    // Check if user has permission to manage this food chain
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to manage this food chain",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Check if bank details are complete
    if (
      !foodChain.bankDetails ||
      !foodChain.bankDetails.accountNumber ||
      !foodChain.bankDetails.ifscCode
    ) {
      return res.status(400).json({
        success: false,
        message: "Bank details are incomplete",
      });
    }

    // Check if fund account already exists
    if (foodChain.razorpayFundAccountId) {
      return res.status(200).json({
        success: true,
        message: "Fund account already exists",
        data: {
          fundAccountId: foodChain.razorpayFundAccountId,
        },
      });
    }

    // Create fund account
    const fundAccount = await createFundAccount(foodChain);

    // Update food chain with fund account ID
    foodChain.razorpayFundAccountId = fundAccount.id;
    // Store virtual account ID if available
    if (fundAccount.virtual_account_id) {
      foodChain.razorpayVirtualAccountId = fundAccount.virtual_account_id;
    }
    await foodChain.save();

    res.status(201).json({
      success: true,
      message: "Fund account created successfully",
      data: {
        fundAccountId: fundAccount.id,
      },
    });
  } catch (error) {
    console.error("Error creating fund account:", error);
    res.status(500).json({
      success: false,
      message: "Error creating fund account",
      error: error.message,
    });
  }
};

/**
 * Update food chain bank details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateFoodChainBankDetails = async (req, res) => {
  try {
    const { foodChainId } = req.params;
    const { bankDetails } = req.body;

    // Check if user has permission to manage this food chain
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to manage this food chain",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Update bank details
    foodChain.bankDetails = {
      ...foodChain.bankDetails,
      ...bankDetails,
    };

    // If bank details have changed, reset the fund account ID
    if (bankDetails.accountNumber || bankDetails.ifscCode) {
      foodChain.razorpayFundAccountId = null;
    }

    await foodChain.save();

    res.status(200).json({
      success: true,
      message: "Bank details updated successfully",
      data: {
        bankDetails: foodChain.bankDetails,
      },
    });
  } catch (error) {
    console.error("Error updating bank details:", error);
    res.status(500).json({
      success: false,
      message: "Error updating bank details",
      error: error.message,
    });
  }
};

/**
 * Create a fund transfer to a food chain
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createFundTransfer = async (req, res) => {
  try {
    const { foodChainId } = req.params;
    const { amount, description, transferMethod } = req.body;

    // Only super admins can transfer funds
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can transfer funds",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Check if fund account exists
    if (!foodChain.razorpayFundAccountId) {
      // Try to create a fund account if bank details are complete
      if (
        foodChain.bankDetails &&
        foodChain.bankDetails.accountNumber &&
        foodChain.bankDetails.ifscCode
      ) {
        const fundAccount = await createFundAccount(foodChain);
        foodChain.razorpayFundAccountId = fundAccount.id;
        if (fundAccount.virtual_account_id) {
          foodChain.razorpayVirtualAccountId = fundAccount.virtual_account_id;
        }
        await foodChain.save();
      } else {
        return res.status(400).json({
          success: false,
          message: "Fund account not set up. Please update bank details first.",
        });
      }
    }

    // Create a reference for the transfer
    const reference = `transfer_${Date.now()}_${foodChainId.substring(0, 6)}`;

    // Create payout
    const payout = await createPayout({
      fundAccountId: foodChain.razorpayFundAccountId,
      amount,
      reference,
      description: description || `Transfer to ${foodChain.name}`,
      transferMethod,
    });

    // Create fund transfer record
    const fundTransfer = new FundTransfer({
      foodChainId,
      amount,
      razorpayPayoutId: payout.id,
      razorpayFundAccountId: foodChain.razorpayFundAccountId,
      status: payout.status,
      reference: payout.reference,
      description: description || `Transfer to ${foodChain.name}`,
      transferMethod: payout.mode,
      transferFee: payout.fees || 0,
      transferredBy: req.user._id,
    });

    await fundTransfer.save();

    res.status(201).json({
      success: true,
      message: "Fund transfer initiated successfully",
      data: {
        transfer: fundTransfer,
        payout,
      },
    });
  } catch (error) {
    console.error("Error creating fund transfer:", error);
    res.status(500).json({
      success: false,
      message: "Error creating fund transfer",
      error: error.message,
    });
  }
};

/**
 * Get fund transfers for a food chain
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFoodChainFundTransfers = async (req, res) => {
  try {
    const { foodChainId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    // Check if user has permission to view this food chain's transfers
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message:
          "You don't have permission to view this food chain's transfers",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Get fund transfers
    const transfers = await FundTransfer.find({ foodChainId })
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate("transferredBy", "name email");

    // Get total count
    const totalTransfers = await FundTransfer.countDocuments({ foodChainId });

    res.status(200).json({
      success: true,
      data: {
        transfers,
        totalTransfers,
        totalPages: Math.ceil(totalTransfers / limit),
        currentPage: page,
      },
    });
  } catch (error) {
    console.error("Error fetching fund transfers:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching fund transfers",
      error: error.message,
    });
  }
};

/**
 * Get fund transfer details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFundTransferDetails = async (req, res) => {
  try {
    const { transferId } = req.params;

    // Find the transfer
    const transfer = await FundTransfer.findById(transferId)
      .populate("foodChainId", "name email contact")
      .populate("transferredBy", "name email");

    if (!transfer) {
      return res.status(404).json({
        success: false,
        message: "Fund transfer not found",
      });
    }

    // Check if user has permission to view this transfer
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== transfer.foodChainId._id.toString()
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to view this transfer",
      });
    }

    // Get latest status from Razorpay if transfer is not completed or failed
    if (
      transfer.razorpayPayoutId &&
      !["completed", "failed", "reversed"].includes(transfer.status)
    ) {
      try {
        const payoutDetails = await getPayoutDetails(transfer.razorpayPayoutId);

        // Import the mapping function from payment-controller
        const { mapRazorpayStatusToFundTransferStatus } = await import(
          "../controllers/payment-controller.js"
        );

        // Update transfer status if changed
        const newStatus = mapRazorpayStatusToFundTransferStatus(
          payoutDetails.status
        );
        if (newStatus !== transfer.status) {
          transfer.status = newStatus;

          // If failed, store failure reason
          if (
            payoutDetails.status === "failed" &&
            payoutDetails.failure_reason
          ) {
            transfer.failureReason = payoutDetails.failure_reason;
          }

          await transfer.save();
        }
      } catch (error) {
        console.error("Error fetching payout details:", error);
      }
    }

    res.status(200).json({
      success: true,
      data: {
        transfer,
      },
    });
  } catch (error) {
    console.error("Error fetching fund transfer details:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching fund transfer details",
      error: error.message,
    });
  }
};

/**
 * Get transfer history for a food chain
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFoodChainTransferHistory = async (req, res) => {
  try {
    const { foodChainId } = req.params;

    // Check if user has permission to view this food chain's transfers
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message:
          "You don't have permission to view this food chain's transfers",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Get all transfers for this food chain
    const transfers = await FundTransfer.find({ foodChain: foodChainId })
      .sort({ createdAt: -1 })
      .limit(100); // Limit to last 100 transfers

    res.status(200).json({
      success: true,
      data: {
        transfers,
      },
    });
  } catch (error) {
    console.error("Error getting transfer history:", error);
    res.status(500).json({
      success: false,
      message: "Error getting transfer history",
      error: error.message,
    });
  }
};

export default {
  createFoodChainFundAccount,
  updateFoodChainBankDetails,
  createFundTransfer,
  getFoodChainFundTransfers,
  getFundTransferDetails,
  getFoodChainTransferHistory,
};
