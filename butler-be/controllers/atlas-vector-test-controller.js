import { 
  searchDishesWith<PERSON>tlasVector,
  getAtlasVectorRecommendations,
  isAtlasVectorSearchAvailable,
  hybridDishSearch 
} from "../services/atlas-vector-search-service.js";
import { getSmartRecommendations, getFallbackMetrics } from "../services/atlas-vector-fallback-service.js";
import { validateAtlasVectorConfig, logAtlasVectorConfig } from "../config/atlas-vector-config.js";
import Dish from "../models/Dish.js";

/**
 * Test Atlas Vector Search availability
 */
export const testAtlasVectorAvailability = async (req, res) => {
  try {
    console.log("🧪 Testing Atlas Vector Search availability...");
    
    const isAvailable = await isAtlasVectorSearchAvailable();
    const configValidation = validateAtlasVectorConfig();
    
    res.status(200).json({
      success: true,
      atlasVectorSearchAvailable: isAvailable,
      configuration: {
        isValid: configValidation.isValid,
        issues: configValidation.issues
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error("❌ Error testing Atlas Vector Search:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      atlasVectorSearchAvailable: false
    });
  }
};

/**
 * Test Atlas Vector Search with a sample query
 */
export const testAtlasVectorSearch = async (req, res) => {
  try {
    const { query = "spicy chicken", foodChainId, outletId, limit = 5 } = req.query;
    
    if (!foodChainId) {
      return res.status(400).json({
        success: false,
        error: "foodChainId is required for testing"
      });
    }
    
    console.log(`🧪 Testing Atlas Vector Search with query: "${query}"`);
    
    const startTime = Date.now();
    
    // Test direct Atlas Vector Search
    const vectorResults = await searchDishesWithAtlasVector(
      query,
      foodChainId,
      outletId,
      { limit: parseInt(limit) }
    );
    
    const responseTime = Date.now() - startTime;
    
    res.status(200).json({
      success: true,
      query,
      results: vectorResults,
      metadata: {
        resultCount: vectorResults.length,
        responseTime: `${responseTime}ms`,
        searchMethod: 'atlas_vector_search',
        foodChainId,
        outletId
      }
    });
    
  } catch (error) {
    console.error("❌ Error in Atlas Vector Search test:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      query: req.query.query
    });
  }
};

/**
 * Test hybrid search (Atlas Vector + Text Search)
 */
export const testHybridSearch = async (req, res) => {
  try {
    const { 
      query = "spicy chicken", 
      foodChainId, 
      outletId, 
      limit = 5,
      vectorWeight = 0.7,
      textWeight = 0.3
    } = req.query;
    
    if (!foodChainId) {
      return res.status(400).json({
        success: false,
        error: "foodChainId is required for testing"
      });
    }
    
    console.log(`🧪 Testing Hybrid Search with query: "${query}"`);
    
    const startTime = Date.now();
    
    const hybridResults = await hybridDishSearch(
      query,
      foodChainId,
      outletId,
      { 
        limit: parseInt(limit),
        vectorWeight: parseFloat(vectorWeight),
        textWeight: parseFloat(textWeight)
      }
    );
    
    const responseTime = Date.now() - startTime;
    
    res.status(200).json({
      success: true,
      query,
      results: hybridResults,
      metadata: {
        resultCount: hybridResults.length,
        responseTime: `${responseTime}ms`,
        searchMethod: 'hybrid_search',
        weights: {
          vector: parseFloat(vectorWeight),
          text: parseFloat(textWeight)
        },
        foodChainId,
        outletId
      }
    });
    
  } catch (error) {
    console.error("❌ Error in Hybrid Search test:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      query: req.query.query
    });
  }
};

/**
 * Test smart recommendations with fallback
 */
export const testSmartRecommendations = async (req, res) => {
  try {
    const { 
      query = "spicy chicken", 
      foodChainId, 
      outletId, 
      limit = 5 
    } = req.query;
    
    if (!foodChainId) {
      return res.status(400).json({
        success: false,
        error: "foodChainId is required for testing"
      });
    }
    
    console.log(`🧪 Testing Smart Recommendations with query: "${query}"`);
    
    // Get available dishes for the food chain
    const availableDishes = await Dish.find({
      foodChain: foodChainId,
      isAvailable: true,
      ...(outletId && { outlets: { $in: [outletId] } })
    })
    .populate('category', 'name')
    .limit(100) // Limit for testing
    .lean();
    
    if (availableDishes.length === 0) {
      return res.status(404).json({
        success: false,
        error: "No available dishes found for the specified food chain"
      });
    }
    
    const startTime = Date.now();
    
    const smartResults = await getSmartRecommendations(
      query,
      availableDishes,
      {
        foodChainId,
        outletId,
        userId: req.user?._id,
        limit: parseInt(limit),
        language: 'en'
      }
    );
    
    const responseTime = Date.now() - startTime;
    
    res.status(200).json({
      success: true,
      query,
      availableDishesCount: availableDishes.length,
      recommendations: smartResults.recommendations,
      aiResponse: smartResults.aiResponse,
      metadata: {
        ...smartResults.metadata,
        totalResponseTime: `${responseTime}ms`
      }
    });
    
  } catch (error) {
    console.error("❌ Error in Smart Recommendations test:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      query: req.query.query
    });
  }
};

/**
 * Compare Atlas Vector Search vs Traditional Search
 */
export const compareSearchMethods = async (req, res) => {
  try {
    const { 
      query = "spicy chicken", 
      foodChainId, 
      outletId, 
      limit = 5 
    } = req.query;
    
    if (!foodChainId) {
      return res.status(400).json({
        success: false,
        error: "foodChainId is required for comparison"
      });
    }
    
    console.log(`🧪 Comparing search methods for query: "${query}"`);
    
    // Get available dishes
    const availableDishes = await Dish.find({
      foodChain: foodChainId,
      isAvailable: true,
      ...(outletId && { outlets: { $in: [outletId] } })
    })
    .populate('category', 'name')
    .limit(100)
    .lean();
    
    const results = {};
    
    // Test Atlas Vector Search
    try {
      const vectorStart = Date.now();
      const vectorResults = await searchDishesWithAtlasVector(
        query,
        foodChainId,
        outletId,
        { limit: parseInt(limit) }
      );
      results.atlasVectorSearch = {
        results: vectorResults,
        responseTime: Date.now() - vectorStart,
        resultCount: vectorResults.length,
        success: true
      };
    } catch (error) {
      results.atlasVectorSearch = {
        success: false,
        error: error.message
      };
    }
    
    // Test Traditional Text Search
    try {
      const textStart = Date.now();
      const textResults = availableDishes.filter(dish => {
        const searchText = `${dish.name} ${dish.description || ''} ${dish.cuisine || ''}`.toLowerCase();
        return searchText.includes(query.toLowerCase());
      }).slice(0, parseInt(limit));
      
      results.textSearch = {
        results: textResults,
        responseTime: Date.now() - textStart,
        resultCount: textResults.length,
        success: true
      };
    } catch (error) {
      results.textSearch = {
        success: false,
        error: error.message
      };
    }
    
    // Test Smart Recommendations
    try {
      const smartStart = Date.now();
      const smartResults = await getSmartRecommendations(
        query,
        availableDishes,
        { foodChainId, outletId, limit: parseInt(limit) }
      );
      
      results.smartRecommendations = {
        results: smartResults.recommendations,
        responseTime: Date.now() - smartStart,
        resultCount: smartResults.recommendations.length,
        method: smartResults.metadata.method,
        fallbackUsed: smartResults.metadata.fallbackUsed,
        success: true
      };
    } catch (error) {
      results.smartRecommendations = {
        success: false,
        error: error.message
      };
    }
    
    res.status(200).json({
      success: true,
      query,
      availableDishesCount: availableDishes.length,
      comparison: results,
      summary: {
        fastestMethod: Object.entries(results)
          .filter(([, result]) => result.success)
          .sort(([, a], [, b]) => a.responseTime - b.responseTime)[0]?.[0],
        mostResults: Object.entries(results)
          .filter(([, result]) => result.success)
          .sort(([, a], [, b]) => b.resultCount - a.resultCount)[0]?.[0]
      }
    });
    
  } catch (error) {
    console.error("❌ Error in search method comparison:", error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Get Atlas Vector Search performance metrics
 */
export const getAtlasVectorMetrics = async (req, res) => {
  try {
    const fallbackMetrics = getFallbackMetrics();
    const configValidation = validateAtlasVectorConfig();
    
    res.status(200).json({
      success: true,
      metrics: fallbackMetrics,
      configuration: {
        isValid: configValidation.isValid,
        issues: configValidation.issues
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error("❌ Error getting Atlas Vector metrics:", error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Log current Atlas Vector configuration
 */
export const logAtlasVectorConfiguration = async (req, res) => {
  try {
    logAtlasVectorConfig();
    
    const configValidation = validateAtlasVectorConfig();
    
    res.status(200).json({
      success: true,
      message: "Atlas Vector configuration logged to console",
      configuration: configValidation.config,
      validation: {
        isValid: configValidation.isValid,
        issues: configValidation.issues
      }
    });
    
  } catch (error) {
    console.error("❌ Error logging Atlas Vector configuration:", error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
