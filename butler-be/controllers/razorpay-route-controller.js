import FoodChain from "../models/FoodChain.js";
import Order from "../models/Order.js";
import FundTransfer from "../models/FundTransfer.js";
import {
  createRazorpayAccount,
  addStakeholder,
  requestRouteConfiguration,
  addBankAccountToProduct,
  transferFundsToFoodChain,
  getTransferDetails,
} from "../utils/razorpayRoute.js";
import { mapRazorpayStatusToFundTransferStatus } from "./payment-controller.js";
import connectDB from "../config/database.js";

/**
 * Helper function to determine the next step in the multi-step form
 * @param {String} currentStep - The current step
 * @returns {String} - The next step
 */
const getNextStep = (currentStep) => {
  const steps = [
    "business",
    "address",
    "legal",
    "final",
    "stakeholder",
    "activation",
    "completed",
  ];
  const currentIndex = steps.indexOf(currentStep);

  if (currentIndex === -1 || currentIndex === steps.length - 1) {
    return "completed";
  }

  return steps[currentIndex + 1];
};

/**
 * Create a Razorpay account for a food chain
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createFoodChainRazorpayAccount = async (req, res) => {
  try {
    const { foodChainId } = req.params;
    const {
      // Basic business details
      businessType,
      contactPerson,
      phone,
      email,
      // Address details
      address,
      // Legal information
      legalInfo,
      // Step tracking
      currentStep,
      completeSetup,
    } = req.body;

    // Check if user has permission to manage this food chain
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to manage this food chain",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Check if Razorpay account already exists and we're not doing a complete setup
    if (foodChain.razorpayAccountId && !completeSetup) {
      return res.status(200).json({
        success: true,
        message: "Razorpay account already exists",
        data: {
          accountId: foodChain.razorpayAccountId,
          status: foodChain.razorpayAccountStatus,
          currentStep: foodChain.razorpayRouteEnabled
            ? "completed"
            : "stakeholder",
        },
      });
    }

    // Update food chain with provided information
    let updated = false;

    // Update business details
    if (businessType) {
      foodChain.businessType = businessType;
      updated = true;
    }

    if (contactPerson) {
      foodChain.contactPerson = contactPerson;
      updated = true;
    }

    if (phone) {
      foodChain.phone = phone;
      updated = true;
    }

    if (email) {
      foodChain.email = email;
      updated = true;
    }

    // Update address
    if (address) {
      foodChain.address = {
        ...foodChain.address,
        ...address,
      };
      updated = true;
    }

    // Update legal info
    if (legalInfo) {
      foodChain.legalInfo = {
        ...foodChain.legalInfo,
        ...legalInfo,
      };
      updated = true;
    }

    // Save updates if any were made
    if (updated) {
      await foodChain.save();
    }

    // If we're just updating information for a step, return success
    if (currentStep && currentStep !== "final" && !completeSetup) {
      return res.status(200).json({
        success: true,
        message: `Step ${currentStep} information saved successfully`,
        data: {
          foodChain,
          nextStep: getNextStep(currentStep),
        },
      });
    }

    // Create Razorpay account if we're completing the setup or on the final step
    if (
      !foodChain.razorpayAccountId &&
      (completeSetup || currentStep === "final")
    ) {
      // Validate required fields
      if (!foodChain.name || !foodChain.email || !foodChain.phone) {
        return res.status(400).json({
          success: false,
          message:
            "Missing required fields: name, email, and phone are required",
        });
      }

      // Create Razorpay account
      const account = await createRazorpayAccount(foodChain);

      // Update food chain with Razorpay account ID
      foodChain.razorpayAccountId = account.id;
      foodChain.razorpayRouteEnabled = true;
      foodChain.razorpayAccountStatus = account.status || "created";
      await foodChain.save();

      return res.status(201).json({
        success: true,
        message: "Razorpay account created successfully",
        data: {
          accountId: account.id,
          status: account.status,
          nextStep: "stakeholder",
        },
      });
    }

    // If we reach here, we're just returning the current state
    res.status(200).json({
      success: true,
      message: "Food chain payment information updated",
      data: {
        foodChain,
        currentStep: foodChain.razorpayAccountId
          ? foodChain.razorpayRouteEnabled
            ? "completed"
            : "stakeholder"
          : "business",
      },
    });
  } catch (error) {
    console.error("Error creating Razorpay account:", error);
    res.status(500).json({
      success: false,
      message: "Error creating Razorpay account",
      error: error.message,
    });
  }
};

/**
 * Add a stakeholder to a food chain's Razorpay account
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const addFoodChainStakeholder = async (req, res) => {
  try {
    const { foodChainId } = req.params;
    const stakeholderData = req.body;

    // Check if user has permission to manage this food chain
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to manage this food chain",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Check if Razorpay account exists
    if (!foodChain.razorpayAccountId) {
      return res.status(400).json({
        success: false,
        message:
          "Razorpay account not set up. Please complete the business details first.",
        data: {
          nextStep: "business",
        },
      });
    }

    console.log(stakeholderData);
    if (!stakeholderData || !stakeholderData.name || !stakeholderData.email) {
      return res.status(400).json({
        success: false,
        message: "Stakeholder data is incomplete. Name and email are required.",
      });
    }

    // Add stakeholder
    const stakeholder = await addStakeholder(
      foodChain.razorpayAccountId,
      stakeholderData
    );

    // Update food chain with stakeholder info
    if (!foodChain.razorpayStakeholders) {
      foodChain.razorpayStakeholders = [];
    }
    foodChain.razorpayStakeholders.push(stakeholder.id);
    await foodChain.save();

    res.status(201).json({
      success: true,
      message: "Stakeholder added successfully",
      data: {
        stakeholderId: stakeholder.id,
        nextStep: "activation",
      },
    });
  } catch (error) {
    console.error("Error adding stakeholder:", error);
    res.status(500).json({
      success: false,
      message: "Error adding stakeholder",
      error: error.message,
    });
  }
};

/**
 * Request Route configuration for a food chain's Razorpay account
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const requestFoodChainRouteConfiguration = async (req, res) => {
  try {
    const { foodChainId } = req.params;
    const { acceptTerms, skipActivation } = req.body;

    // Check if user has permission to manage this food chain
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to manage this food chain",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Check if Razorpay account exists
    if (!foodChain.razorpayAccountId) {
      return res.status(400).json({
        success: false,
        message:
          "Razorpay account not set up. Please complete the previous steps first.",
        data: {
          nextStep: "business",
        },
      });
    }

    // Check if stakeholders are added
    if (
      !foodChain.razorpayStakeholders ||
      foodChain.razorpayStakeholders.length === 0
    ) {
      return res.status(400).json({
        success: false,
        message:
          "No stakeholders added. Please add at least one stakeholder first.",
        data: {
          nextStep: "stakeholder",
        },
      });
    }

    // Verify terms acceptance
    if (!acceptTerms && !skipActivation) {
      return res.status(400).json({
        success: false,
        message: "You must accept the terms and conditions to activate Route.",
      });
    }

    // If skipping activation (for testing or development)
    if (skipActivation) {
      // Just mark as enabled without actually calling the API
      foodChain.razorpayRouteEnabled = true;
      await foodChain.save();

      return res.status(200).json({
        success: true,
        message: "Route activation skipped (development mode)",
        data: {
          nextStep: "completed",
        },
      });
    }

    // Request Route configuration
    const productConfig = await requestRouteConfiguration(
      foodChain.razorpayAccountId
    );

    // Update food chain with Route configuration status
    foodChain.razorpayRouteEnabled = true;
    await foodChain.save();

    res.status(200).json({
      success: true,
      message: "Route configuration requested successfully",
      data: {
        productConfig,
        nextStep: "completed",
      },
    });
  } catch (error) {
    console.error("Error requesting Route configuration:", error);
    res.status(500).json({
      success: false,
      message: "Error requesting Route configuration",
      error: error.message,
    });
  }
};

/**
 * Transfer funds to a food chain's Razorpay account
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const transferFundsToFoodChainAccount = async (req, res) => {
  try {
    const { foodChainId } = req.params;
    const { paymentId, amount, description } = req.body;

    // Only super admins can transfer funds
    if (req.user.role !== "super_admin") {
      return res.status(403).json({
        success: false,
        message: "Only super admins can transfer funds",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Check if Razorpay account exists and Route is enabled
    if (!foodChain.razorpayAccountId) {
      return res.status(400).json({
        success: false,
        message: "Razorpay account not set up. Please create an account first.",
      });
    }

    if (!foodChain.razorpayRouteEnabled) {
      return res.status(400).json({
        success: false,
        message: "Route is not enabled for this food chain.",
      });
    }

    // Create a reference for the transfer
    const reference = `transfer_${Date.now()}_${foodChainId.substring(0, 6)}`;

    // Transfer funds
    const transfer = await transferFundsToFoodChain(
      paymentId,
      foodChain.razorpayAccountId,
      amount,
      description || `Transfer to ${foodChain.name}`
    );

    // Create fund transfer record
    const fundTransfer = new FundTransfer({
      foodChainId,
      amount,
      razorpayPayoutId: transfer.id,
      razorpayAccountId: foodChain.razorpayAccountId, // Use account ID for route transfers
      status: mapRazorpayStatusToFundTransferStatus(transfer.status),
      reference,
      description: description || `Transfer to ${foodChain.name}`,
      transferMethod: "route", // Now a valid enum value
      transferFee: transfer.fees || 0,
      transferredBy: req.user._id,
    });

    await fundTransfer.save();

    res.status(201).json({
      success: true,
      message: "Fund transfer initiated successfully",
      data: {
        transfer: fundTransfer,
        razorpayTransfer: transfer,
      },
    });
  } catch (error) {
    console.error("Error transferring funds:", error);
    res.status(500).json({
      success: false,
      message: "Error transferring funds",
      error: error.message,
    });
  }
};

/**
 * Automatically transfer funds to a food chain when payment is received
 * @param {String} paymentId - The Razorpay payment ID
 * @param {Object} order - The order object
 * @returns {Object} - The transfer object or null if transfer failed
 */
export const autoTransferFundsToFoodChain = async (paymentId, order) => {
  try {
    await connectDB();

    // Find the food chain
    const foodChain = await FoodChain.findById(order.foodChainId);
    console.log(
      "food chain => ",
      foodChain.razorpayAccountId,
      foodChain.razorpayRouteEnabled
    );
    if (
      !foodChain ||
      !foodChain.razorpayAccountId ||
      !foodChain.razorpayRouteEnabled
    ) {
      console.log(
        `Food chain ${order.foodChainId} does not have a properly configured Razorpay account`
      );
      return null;
    }

    // Calculate transfer amount (minus platform fee if applicable)
    const platformFeePercentage = 0.01; // 1%
    const transferAmount = order.totalAmount * (1 - platformFeePercentage);

    // Create a reference for the transfer
    const reference = `auto_transfer_${Date.now()}_${order.orderNumber}`;

    // Transfer funds
    const transfer = await transferFundsToFoodChain(
      paymentId,
      foodChain.razorpayAccountId,
      transferAmount,
      `Automatic transfer for order #${order.orderNumber}`
    );

    // Create fund transfer record with completed status for automatic transfers
    const transferStatus =
      transfer.status === "created" ||
      transfer.status === "processing" ||
      transfer.status === "pending"
        ? "completed" // Force completed status for automatic transfers
        : mapRazorpayStatusToFundTransferStatus(transfer.status);

    const fundTransfer = new FundTransfer({
      foodChainId: foodChain._id,
      amount: transferAmount,
      razorpayPayoutId: transfer.id,
      razorpayAccountId: foodChain.razorpayAccountId, // Use account ID for route transfers
      status: transferStatus,
      reference,
      description: `Automatic transfer for order #${order.orderNumber}`,
      transferMethod: "route", // Now a valid enum value
      transferFee: transfer.fees || 0,
    });

    await fundTransfer.save();
    console.log(`Automatic fund transfer initiated for order ${order._id}`);

    return { transfer, fundTransfer };
  } catch (error) {
    console.error("Error auto-transferring funds:", error);
    return null;
  }
};

/**
 * Handle Razorpay transfer webhook
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const handleTransferWebhook = async (req, res) => {
  try {
    await connectDB();
    console.log("Transfer webhook received", req.body.event);

    const { event, payload } = req.body;

    // Handle transfer events
    if (
      (event === "transfer.processed" || event === "transfer.failed") &&
      payload &&
      payload.transfer &&
      payload.transfer.entity
    ) {
      const transferId = payload.transfer.entity.id;
      const transferStatus = payload.transfer.entity.status;

      // Find the fund transfer
      const fundTransfer = await FundTransfer.findOne({
        razorpayPayoutId: transferId,
      });

      if (!fundTransfer) {
        return res.status(404).json({ message: "Fund transfer not found" });
      }

      // Update fund transfer status
      // For automatic transfers, always mark as completed unless failed
      let newStatus;
      if (transferStatus === "failed") {
        newStatus = "failed";
      } else if (
        fundTransfer.transferMethod === "route" &&
        !fundTransfer.transferredBy
      ) {
        // This is an automatic transfer (no transferredBy) using route method
        newStatus = "completed";
      } else {
        newStatus = mapRazorpayStatusToFundTransferStatus(transferStatus);
      }

      if (fundTransfer.status !== newStatus) {
        fundTransfer.status = newStatus;

        // If failed, store failure reason
        if (
          transferStatus === "failed" &&
          payload.transfer.entity.failure_reason
        ) {
          fundTransfer.failureReason = payload.transfer.entity.failure_reason;
        }

        await fundTransfer.save();
        console.log(
          `Updated fund transfer ${fundTransfer._id} status to ${newStatus}`
        );
      }
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error("Error handling transfer webhook:", error);
    res.status(500).json({
      success: false,
      message: "Error handling transfer webhook",
      error: error.message,
    });
  }
};

/**
 * Get the current Razorpay Route setup status for a food chain
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFoodChainRazorpayStatus = async (req, res) => {
  try {
    const { foodChainId } = req.params;

    // Check if user has permission to manage this food chain
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to view this food chain",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Determine current setup status
    let currentStep = "business";
    let setupComplete = false;

    if (foodChain.razorpayRouteEnabled) {
      currentStep = "completed";
      setupComplete = true;
    } else if (foodChain.razorpayAccountId) {
      if (
        foodChain.razorpayStakeholders &&
        foodChain.razorpayStakeholders.length > 0
      ) {
        currentStep = "activation";
      } else {
        currentStep = "stakeholder";
      }
    } else if (
      foodChain.legalInfo &&
      (foodChain.legalInfo.pan || foodChain.legalInfo.gst)
    ) {
      currentStep = "final";
    } else if (foodChain.address && foodChain.address.street) {
      currentStep = "legal";
    } else if (foodChain.phone && foodChain.email) {
      currentStep = "address";
    }

    res.status(200).json({
      success: true,
      data: {
        foodChain,
        setupStatus: {
          currentStep,
          setupComplete,
          hasAccount: !!foodChain.razorpayAccountId,
          hasStakeholders: !!(
            foodChain.razorpayStakeholders &&
            foodChain.razorpayStakeholders.length > 0
          ),
          routeEnabled: !!foodChain.razorpayRouteEnabled,
          accountStatus: foodChain.razorpayAccountStatus || "not_created",
        },
      },
    });
  } catch (error) {
    console.error("Error fetching Razorpay setup status:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching Razorpay setup status",
      error: error.message,
    });
  }
};

/**
 * Add bank account details to a food chain's Razorpay product
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const addBankAccountToFoodChainProduct = async (req, res) => {
  try {
    const { foodChainId } = req.params;
    const { productId, bankAccountName, bankAccountNumber, bankBranchIfsc } =
      req.body;

    // Check if user has permission to manage this food chain
    if (
      req.user.role !== "super_admin" &&
      req.user.foodChain.toString() !== foodChainId
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to manage this food chain",
      });
    }

    // Find the food chain
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({
        success: false,
        message: "Food chain not found",
      });
    }

    // Check if Razorpay account exists
    if (!foodChain.razorpayAccountId) {
      return res.status(400).json({
        success: false,
        message:
          "Razorpay account not set up. Please complete the previous steps first.",
      });
    }

    // Check if product ID is provided
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: "Product ID is required",
      });
    }

    // Check if bank account details are provided
    if (!bankAccountName || !bankAccountNumber || !bankBranchIfsc) {
      return res.status(400).json({
        success: false,
        message: "Bank account details are required",
      });
    }

    // Add bank account details to the product
    const updatedProduct = await addBankAccountToProduct(
      foodChain.razorpayAccountId,
      productId,
      {
        beneficiaryName: bankAccountName,
        accountNumber: bankAccountNumber,
        ifscCode: bankBranchIfsc,
      }
    );

    // Update food chain with bank account info
    foodChain.bankAccountInfo = {
      name: bankAccountName,
      number: bankAccountNumber,
      ifsc: bankBranchIfsc,
    };
    await foodChain.save();

    res.status(200).json({
      success: true,
      message: "Bank account details added successfully",
      data: {
        updatedProduct,
      },
    });
  } catch (error) {
    console.error("Error adding bank account details:", error);
    res.status(500).json({
      success: false,
      message: "Error adding bank account details",
      error: error.message,
    });
  }
};

export default {
  createFoodChainRazorpayAccount,
  addFoodChainStakeholder,
  requestFoodChainRouteConfiguration,
  addBankAccountToFoodChainProduct,
  transferFundsToFoodChainAccount,
  autoTransferFundsToFoodChain,
  handleTransferWebhook,
  getFoodChainRazorpayStatus,
};
