import { 
  getPerformanceReport, 
  resetPerformanceMetrics,
  exportPerformanceData,
  performanceHealthCheck 
} from '../services/performance-monitoring-service.js';
import { 
  getCacheStatistics,
  invalidateCache,
  cacheHealthCheck 
} from '../services/intelligent-cache-service.js';
import { 
  healthCheck as orchestratorHealthCheck 
} from '../services/recommendation-orchestrator.js';
import { 
  fallbackHealthCheck 
} from '../services/fallback-recommendation-service.js';

/**
 * Admin Controller for Recommendation Optimization
 * Provides endpoints for monitoring, managing, and configuring the optimization system
 */

/**
 * Get comprehensive performance dashboard data
 */
export const getPerformanceDashboard = async (req, res) => {
  try {
    const performanceReport = getPerformanceReport();
    const cacheStats = getCacheStatistics();
    const systemHealth = await orchestratorHealthCheck();
    const fallbackHealth = fallbackHealthCheck();
    const performanceHealth = performanceHealthCheck();
    const cacheHealth = cacheHealthCheck();

    const dashboardData = {
      overview: {
        totalRequests: performanceReport.requestPatterns.totalRequests,
        averageResponseTime: performanceReport.responseTimes.averageResponseTime,
        tokenSavingsPercentage: performanceReport.tokenUsage.tokenSavingsPercentage,
        cacheHitRate: performanceReport.cachePerformance.cacheHitRate,
        fallbackUsageRate: performanceReport.recommendationAccuracy.fallbackUsageRate * 100,
        systemStatus: systemHealth.status,
        uptime: performanceReport.uptime
      },
      
      performance: {
        tokenUsage: {
          totalSaved: performanceReport.tokenUsage.totalTokensSaved,
          totalUsed: performanceReport.tokenUsage.totalTokensUsed,
          averagePerRequest: performanceReport.tokenUsage.averageTokensPerRequest,
          savingsPercentage: performanceReport.tokenUsage.tokenSavingsPercentage,
          peakUsage: performanceReport.tokenUsage.peakTokenUsage
        },
        
        responseTimes: {
          average: performanceReport.responseTimes.averageResponseTime,
          median: performanceReport.responseTimes.medianResponseTime,
          p95: performanceReport.responseTimes.p95ResponseTime,
          p99: performanceReport.responseTimes.p99ResponseTime,
          fastest: performanceReport.responseTimes.fastestResponse,
          slowest: performanceReport.responseTimes.slowestResponse
        },
        
        optimization: {
          menuReductionRate: performanceReport.systemOptimization.menuReductionRate,
          averageDishesProcessed: performanceReport.systemOptimization.averageDishesProcessed,
          optimizationEfficiency: performanceReport.systemOptimization.optimizationEfficiency
        }
      },
      
      caching: {
        statistics: cacheStats,
        health: cacheHealth,
        hitRate: cacheStats.hitRate,
        totalHits: cacheStats.hits.total,
        totalMisses: cacheStats.misses.total
      },
      
      systemHealth: {
        orchestrator: systemHealth,
        fallback: fallbackHealth,
        performance: performanceHealth,
        cache: cacheHealth
      },
      
      trends: performanceReport.trends,
      insights: performanceReport.insights,
      recommendations: performanceReport.recommendations,
      
      timestamp: new Date()
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Error getting performance dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving dashboard data',
      error: error.message
    });
  }
};

/**
 * Get detailed performance metrics
 */
export const getDetailedMetrics = async (req, res) => {
  try {
    const { timeRange = '24h', metric = 'all' } = req.query;
    
    const performanceReport = getPerformanceReport();
    
    let filteredData = performanceReport;
    
    // Filter by specific metric if requested
    if (metric !== 'all') {
      const validMetrics = ['tokenUsage', 'responseTimes', 'cachePerformance', 'recommendationAccuracy'];
      if (validMetrics.includes(metric)) {
        filteredData = {
          [metric]: performanceReport[metric],
          requestPatterns: performanceReport.requestPatterns,
          uptime: performanceReport.uptime,
          timestamp: new Date()
        };
      }
    }

    res.json({
      success: true,
      data: filteredData,
      timeRange,
      metric
    });

  } catch (error) {
    console.error('Error getting detailed metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving metrics',
      error: error.message
    });
  }
};

/**
 * Manage cache operations
 */
export const manageCacheOperations = async (req, res) => {
  try {
    const { action, cacheType = 'all' } = req.body;

    let result = {};

    switch (action) {
      case 'clear':
        if (cacheType === 'all') {
          result.cleared = invalidateCache.clearAll();
        } else {
          result.cleared = invalidateCache.byPattern('', [cacheType]);
        }
        result.message = `Cleared ${cacheType} cache`;
        break;

      case 'stats':
        result = getCacheStatistics();
        break;

      case 'health':
        result = cacheHealthCheck();
        break;

      case 'invalidate_user':
        const { userId } = req.body;
        if (!userId) {
          return res.status(400).json({
            success: false,
            message: 'userId is required for user cache invalidation'
          });
        }
        result.invalidated = invalidateCache.userDataChanged(userId);
        result.message = `Invalidated cache for user ${userId}`;
        break;

      case 'invalidate_menu':
        const { outletId, dishIds } = req.body;
        if (!outletId) {
          return res.status(400).json({
            success: false,
            message: 'outletId is required for menu cache invalidation'
          });
        }
        result.invalidated = invalidateCache.menuChanged(outletId, dishIds);
        result.message = `Invalidated menu cache for outlet ${outletId}`;
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action. Supported actions: clear, stats, health, invalidate_user, invalidate_menu'
        });
    }

    res.json({
      success: true,
      action,
      cacheType,
      data: result
    });

  } catch (error) {
    console.error('Error managing cache:', error);
    res.status(500).json({
      success: false,
      message: 'Error managing cache',
      error: error.message
    });
  }
};

/**
 * Reset performance metrics
 */
export const resetMetrics = async (req, res) => {
  try {
    const { confirm } = req.body;
    
    if (!confirm) {
      return res.status(400).json({
        success: false,
        message: 'Please confirm the reset by sending { "confirm": true }'
      });
    }

    resetPerformanceMetrics();

    res.json({
      success: true,
      message: 'Performance metrics have been reset',
      timestamp: new Date()
    });

  } catch (error) {
    console.error('Error resetting metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Error resetting metrics',
      error: error.message
    });
  }
};

/**
 * Export performance data
 */
export const exportMetrics = async (req, res) => {
  try {
    const { format = 'json' } = req.query;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `performance-metrics-${timestamp}.${format}`;
    const filePath = `./exports/${filename}`;

    const exportResult = await exportPerformanceData(filePath);

    if (exportResult.success) {
      res.json({
        success: true,
        message: 'Performance data exported successfully',
        filename,
        filePath: exportResult.filePath
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Error exporting performance data',
        error: exportResult.error
      });
    }

  } catch (error) {
    console.error('Error exporting metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Error exporting metrics',
      error: error.message
    });
  }
};

/**
 * Get system health status
 */
export const getSystemHealth = async (req, res) => {
  try {
    const orchestratorHealth = await orchestratorHealthCheck();
    const fallbackHealth = fallbackHealthCheck();
    const performanceHealth = performanceHealthCheck();
    const cacheHealth = cacheHealthCheck();

    const overallHealth = {
      status: 'healthy',
      components: {
        orchestrator: orchestratorHealth,
        fallback: fallbackHealth,
        performance: performanceHealth,
        cache: cacheHealth
      },
      issues: [],
      recommendations: []
    };

    // Collect issues from all components
    [orchestratorHealth, fallbackHealth, performanceHealth, cacheHealth].forEach(health => {
      if (health.issues) {
        overallHealth.issues.push(...health.issues);
      }
      if (health.recommendations) {
        overallHealth.recommendations.push(...health.recommendations);
      }
    });

    // Determine overall status
    const componentStatuses = Object.values(overallHealth.components).map(c => c.status);
    if (componentStatuses.includes('error')) {
      overallHealth.status = 'error';
    } else if (componentStatuses.includes('warning')) {
      overallHealth.status = 'warning';
    }

    res.json({
      success: true,
      health: overallHealth,
      timestamp: new Date()
    });

  } catch (error) {
    console.error('Error getting system health:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving system health',
      error: error.message,
      health: {
        status: 'error',
        components: {},
        issues: ['System health check failed'],
        recommendations: ['Check system logs and restart services if necessary']
      }
    });
  }
};

/**
 * Get optimization configuration
 */
export const getOptimizationConfig = async (req, res) => {
  try {
    const config = {
      caching: {
        ttl: {
          quickResponse: 180,
          userSpecific: 600,
          aiResponses: 1800,
          menuMetadata: 3600,
          popularQueries: 900
        },
        maxKeys: {
          quickResponse: 1000,
          userSpecific: 5000,
          aiResponses: 2000,
          menuMetadata: 500,
          popularQueries: 1500
        }
      },
      
      optimization: {
        maxDishesToAI: 15,
        preFilterThreshold: 50,
        semanticSearchThreshold: 0.05,
        fallbackEnabled: true
      },
      
      performance: {
        monitoringEnabled: true,
        metricsRetention: '7d',
        alertThresholds: {
          responseTime: 2000,
          cacheHitRate: 40,
          fallbackUsage: 20,
          errorRate: 5
        }
      },
      
      features: {
        optimizedRecommendations: true,
        intelligentCaching: true,
        performanceMonitoring: true,
        fallbackMechanisms: true,
        realTimeAnalytics: false
      }
    };

    res.json({
      success: true,
      config,
      timestamp: new Date()
    });

  } catch (error) {
    console.error('Error getting optimization config:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving configuration',
      error: error.message
    });
  }
};

/**
 * Update optimization configuration
 */
export const updateOptimizationConfig = async (req, res) => {
  try {
    const { config } = req.body;
    
    if (!config) {
      return res.status(400).json({
        success: false,
        message: 'Configuration object is required'
      });
    }

    // In a real implementation, this would update the actual configuration
    // For now, we'll just validate and return the config
    
    res.json({
      success: true,
      message: 'Configuration updated successfully',
      config,
      timestamp: new Date()
    });

  } catch (error) {
    console.error('Error updating optimization config:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating configuration',
      error: error.message
    });
  }
};
