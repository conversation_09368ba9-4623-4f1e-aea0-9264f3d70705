import multer from "multer";
import {
  getEnhancedTranscription,
  validateAudioFile,
  getOptimalLanguageCode,
} from "../services/audio-transcription-service.js";

// Configure multer for audio file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept audio files
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed'), false);
    }
  },
});

/**
 * Handle audio transcription requests
 * POST /api/transcribe-audio
 */
export const transcribeAudio = async (req, res) => {
  try {
    console.log(`🎤 Audio transcription request received`);
    
    // Check if audio file was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: "No audio file provided",
      });
    }

    const { language = "en", browserFallback } = req.body;
    const audioBuffer = req.file.buffer;
    const originalFilename = req.file.originalname || "audio.webm";
    const mimeType = req.file.mimetype;

    console.log(`📁 File info: ${originalFilename} (${mimeType}, ${(audioBuffer.length / 1024).toFixed(1)}KB)`);

    // Validate audio file
    const validation = validateAudioFile(audioBuffer, mimeType);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: validation.error,
      });
    }

    // Get optimal language code
    const optimalLanguage = getOptimalLanguageCode(language);
    console.log(`🌐 Using language: ${optimalLanguage}`);

    // Perform enhanced transcription
    const transcriptionResult = await getEnhancedTranscription(
      audioBuffer,
      optimalLanguage,
      originalFilename,
      browserFallback
    );

    if (transcriptionResult.success) {
      console.log(`✅ Transcription successful: "${transcriptionResult.transcript}"`);
      
      return res.status(200).json({
        success: true,
        transcript: transcriptionResult.transcript,
        language: transcriptionResult.language,
        method: transcriptionResult.method,
        confidence: transcriptionResult.confidence,
        metadata: {
          originalFilename,
          fileSize: validation.sizeFormatted,
          duration: transcriptionResult.duration,
        },
      });
    } else {
      console.error(`❌ Transcription failed: ${transcriptionResult.error}`);
      
      return res.status(500).json({
        success: false,
        error: transcriptionResult.error,
        method: transcriptionResult.method,
        details: {
          groqError: transcriptionResult.groqError,
        },
      });
    }

  } catch (error) {
    console.error("Audio transcription controller error:", error);
    
    return res.status(500).json({
      success: false,
      error: "Internal server error during transcription",
      details: error.message,
    });
  }
};

/**
 * Test endpoint for audio transcription
 * GET /api/transcribe-audio/test
 */
export const testTranscription = async (req, res) => {
  try {
    return res.status(200).json({
      success: true,
      message: "Audio transcription service is available",
      supportedLanguages: [
        "en", "hi", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh", "ar"
      ],
      maxFileSize: "25MB",
      supportedFormats: [
        "audio/webm", "audio/mp4", "audio/mpeg", "audio/wav", "audio/ogg"
      ],
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: "Service unavailable",
    });
  }
};

/**
 * Middleware to handle multer upload
 */
export const uploadAudioMiddleware = upload.single('audio');

/**
 * Error handler for multer upload errors
 */
export const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'Audio file too large. Maximum size is 25MB.',
      });
    }
    
    return res.status(400).json({
      success: false,
      error: `Upload error: ${error.message}`,
    });
  }
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: error.message,
    });
  }
  
  next();
};
