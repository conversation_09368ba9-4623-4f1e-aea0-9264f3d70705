import mongoose from "mongoose";
import dotenv from "dotenv";
import { validateAndApplyOffers } from "./services/offer-validation-service.js";
import Offer from "./models/Offer.js";
import Coupon from "./models/Coupon.js";
import Dish from "./models/Dish.js";
import FoodChain from "./models/FoodChain.js";
import Outlet from "./models/Outlet.js";

dotenv.config();

// Test data
const testData = {
  foodChainId: null,
  outletId: null,
  dishes: [],
  offers: [],
  coupons: [],
};

async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("✅ Connected to MongoDB");
  } catch (error) {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
  }
}

async function setupTestData() {
  console.log("🔧 Setting up test data...");

  // Find or create a food chain
  let foodChain = await FoodChain.findOne();
  if (!foodChain) {
    console.log("❌ No food chain found. Please create a food chain first.");
    process.exit(1);
  }
  testData.foodChainId = foodChain._id;

  // Find or create an outlet
  let outlet = await Outlet.findOne({ foodChain: foodChain._id });
  if (!outlet) {
    console.log("❌ No outlet found. Please create an outlet first.");
    process.exit(1);
  }
  testData.outletId = outlet._id;

  // Get some dishes
  const dishes = await Dish.find({ isAvailable: true }).limit(5);
  if (dishes.length === 0) {
    console.log("❌ No dishes found. Please create some dishes first.");
    process.exit(1);
  }
  testData.dishes = dishes;

  console.log(`✅ Found ${dishes.length} dishes for testing`);
}

async function testOfferTypes() {
  console.log("\n🧪 Testing Offer Types...");

  const orderData = {
    outletId: testData.outletId,
    items: testData.dishes.slice(0, 3).map((dish) => ({
      dishId: dish._id,
      dishName: dish.name,
      quantity: 2,
      price: dish.price,
      category: dish.category,
    })),
    totalAmount: testData.dishes
      .slice(0, 3)
      .reduce((sum, dish) => sum + dish.price * 2, 0),
    foodChainId: testData.foodChainId,
  };

  console.log(`Order total: ₹${orderData.totalAmount}`);

  // Test different offer types
  const offerTypes = [
    "discount",
    "minimumAmount",
    "BOGO",
    "quantityDiscount",
    "freeItem",
    "combo",
    "multiDishType",
    "dayOfWeek",
    "dateRange",
    "customerTier",
    "firstTime",
    "timeBasedSpecial",
  ];

  for (const offerType of offerTypes) {
    try {
      console.log(`\n  Testing ${offerType} offer...`);

      // Create a test offer
      const testOffer = {
        _id: new mongoose.Types.ObjectId(),
        name: `Test ${offerType} Offer`,
        offerType: offerType,
        discountDetails: {
          discountType: "percentage",
          discountValue: 10,
          maxDiscount: 100,
          buyQuantity: 2,
          getQuantity: 1,
          minimumOrderValue: 50,
        },
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        isActive: true,
        foodChainId: testData.foodChainId,
        applicableOutlets: [testData.outletId],
        autoApply: true,
      };

      // Test offer validation
      const result = await validateAndApplyOffers(orderData, null);

      if (result.success) {
        console.log(`    ✅ ${offerType}: Validation successful`);
        if (result.appliedOffers && result.appliedOffers.length > 0) {
          console.log(
            `    💰 Applied ${result.appliedOffers.length} offers, total discount: ₹${result.totalDiscount}`
          );
        }
      } else {
        console.log(
          `    ⚠️  ${offerType}: ${result.error || "No offers applied"}`
        );
      }
    } catch (error) {
      console.log(`    ❌ ${offerType}: Error - ${error.message}`);
    }
  }
}

async function testCouponValidation() {
  console.log("\n🎫 Testing Coupon Validation...");

  // Test coupon validation with different scenarios
  const testScenarios = [
    {
      name: "Valid percentage coupon",
      coupon: {
        code: "TEST10",
        discountType: "percentage",
        discountValue: 10,
        minOrderValue: 100,
        maxDiscount: 50,
        isActive: true,
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        usageLimit: 0,
        usedCount: 0,
      },
      amount: 200,
    },
    {
      name: "Valid fixed coupon",
      coupon: {
        code: "SAVE50",
        discountType: "fixed",
        discountValue: 50,
        minOrderValue: 100,
        isActive: true,
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        usageLimit: 0,
        usedCount: 0,
      },
      amount: 200,
    },
    {
      name: "Minimum order not met",
      coupon: {
        code: "MIN500",
        discountType: "percentage",
        discountValue: 15,
        minOrderValue: 500,
        isActive: true,
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        usageLimit: 0,
        usedCount: 0,
      },
      amount: 200,
    },
  ];

  for (const scenario of testScenarios) {
    try {
      console.log(`\n  Testing: ${scenario.name}`);

      // Note: validateCouponInternal is not exported, so we'll test the logic manually
      const coupon = scenario.coupon;
      const amount = scenario.amount;

      // Basic validation checks
      if (!coupon.isActive) {
        console.log("    ❌ Coupon is inactive");
        continue;
      }

      const now = new Date();
      if (now < coupon.startDate || now > coupon.endDate) {
        console.log("    ❌ Coupon is expired or not yet active");
        continue;
      }

      if (coupon.usageLimit > 0 && coupon.usedCount >= coupon.usageLimit) {
        console.log("    ❌ Coupon usage limit reached");
        continue;
      }

      if (amount < coupon.minOrderValue) {
        console.log(
          `    ❌ Minimum order value ₹${coupon.minOrderValue} not met (order: ₹${amount})`
        );
        continue;
      }

      // Calculate discount
      let discount = 0;
      if (coupon.discountType === "percentage") {
        discount = (amount * coupon.discountValue) / 100;
        if (coupon.maxDiscount && discount > coupon.maxDiscount) {
          discount = coupon.maxDiscount;
        }
      } else {
        discount = coupon.discountValue;
        if (discount > amount) {
          discount = amount;
        }
      }

      console.log(
        `    ✅ Valid coupon: ₹${discount} discount on ₹${amount} order`
      );
    } catch (error) {
      console.log(`    ❌ Error: ${error.message}`);
    }
  }
}

async function testAdminOrderCreation() {
  console.log("\n👨‍💼 Testing Admin Order Creation with Offers...");

  // Simulate admin order creation data
  const orderData = {
    outletId: testData.outletId,
    items: testData.dishes.slice(0, 2).map((dish) => ({
      dishId: dish._id,
      dishName: dish.name,
      quantity: 1,
      price: dish.price,
    })),
    totalAmount: testData.dishes
      .slice(0, 2)
      .reduce((sum, dish) => sum + dish.price, 0),
    foodChainId: testData.foodChainId,
  };

  console.log(`Admin order total: ₹${orderData.totalAmount}`);

  try {
    const result = await validateAndApplyOffers(orderData, null);

    if (result.success) {
      console.log("✅ Admin order offer validation successful");
      if (result.appliedOffers && result.appliedOffers.length > 0) {
        console.log(`💰 Would apply ${result.appliedOffers.length} offers`);
        result.appliedOffers.forEach((offer) => {
          console.log(`  - ${offer.offerName}: ₹${offer.discount} discount`);
        });
      } else {
        console.log("ℹ️  No applicable offers found");
      }
    } else {
      console.log(`❌ Admin order offer validation failed: ${result.error}`);
    }
  } catch (error) {
    console.log(`❌ Error in admin order creation: ${error.message}`);
  }
}

async function runTests() {
  console.log("🚀 Starting Marketing Module Tests\n");

  await connectDB();
  await setupTestData();
  await testOfferTypes();
  await testCouponValidation();
  await testAdminOrderCreation();

  console.log("\n✅ All tests completed!");
  process.exit(0);
}

// Handle errors
process.on("unhandledRejection", (error) => {
  console.error("❌ Unhandled rejection:", error);
  process.exit(1);
});

// Run tests
runTests().catch(console.error);
